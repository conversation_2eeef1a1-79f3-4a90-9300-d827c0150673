/**
 * 应用管理器 - 重构版
 *
 * 负责TaskManager的初始化、配置加载和生命周期管理
 * 分离了应用逻辑和API服务器的职责
 */

import { TaskManager, TaskManagerDependencies } from './task-manager';
import { FetcherManager } from '@/modules/fetcher';
import { ParserManager } from '@/modules/parser';
import { StorageManager } from '@/modules/storage';
import { ProxyManager } from '@/modules/fetcher/proxy-manager';
import { ProxyStrategy, ProxyHealthCheckConfig, ProxyConfig } from '@/interfaces';
import { getLogger, time } from '@/utils';

export interface AppManagerConfig {
  configPath?: string;
}

export class AppManager {
  private taskManager: TaskManager | null = null;
  private dependencies: TaskManagerDependencies | null = null;
  private logger = getLogger('AppManager');

  constructor(private config: AppManagerConfig = {}) {}

  /**
   * 初始化应用
   */
  public async initialize(): Promise<void> {
    if (this.taskManager) {
      this.logger.debug('TaskManager 已经初始化，跳过');
      return;
    }

    const initTimer = time('TaskManager初始化');

    try {
      this.logger.info('开始初始化 TaskManager...');

      // 创建依赖项
      const fetcherManager = new FetcherManager();
      const parserManager = new ParserManager();
      const storageManager = new StorageManager();

      // 初始化存储 - 注册默认的内存存储
      const { MemoryStorage } = await import('@/modules/storage');
      const memoryStorage = new MemoryStorage();
      await memoryStorage.initialize();
      storageManager.registerStorage(memoryStorage);

      // 初始化AI引擎
      const { AIEngine } = await import('@/modules/ai/ai-engine');
      const aiEngine = new AIEngine();
      await aiEngine.initialize();

      // 创建并配置代理管理器
      const proxyHealthCheckConfig: ProxyHealthCheckConfig = {
        enabled: true,
        intervalMs: 300000, // 5分钟检查一次
        timeoutMs: 10000, // 10秒超时
        testUrl: 'https://httpbin.org/ip',
      };

      const proxyManager = new ProxyManager(
        ProxyStrategy.ROUND_ROBIN,
        proxyHealthCheckConfig,
        3, // 失败阈值
        300000 // 恢复时间5分钟
      );

      // 将代理管理器设置到FetcherManager
      fetcherManager.setProxyManager(proxyManager);

      // 启动代理健康检查
      proxyManager.startHealthCheck();

      // 添加一些测试代理（可选，实际使用中应该从配置文件加载）
      const testProxies: ProxyConfig[] = [
        // 注意：这些是示例代理，在生产环境中应该使用真实的代理
        // 用户可以通过前端界面或API添加真实代理
      ];

      if (testProxies.length > 0) {
        proxyManager.addProxies(testProxies);
        this.logger.info(`添加了 ${testProxies.length} 个测试代理`);
      }

      this.dependencies = {
        fetcherManager,
        parserManager,
        storageManager,
      };

      // 创建TaskManager实例
      this.taskManager = new TaskManager(this.dependencies);

      initTimer();
      this.logger.info('✅ TaskManager 初始化完成');
    } catch (initError) {
      initTimer();
      this.logger.error('TaskManager 初始化失败', {
        error: initError instanceof Error ? initError.message : initError,
        stack: initError instanceof Error ? initError.stack : undefined,
      });
      throw initError;
    }
  }

  /**
   * 停止应用
   */
  public async stop(): Promise<void> {
    if (!this.taskManager) {
      this.logger.debug('TaskManager 未初始化，无需停止');
      return;
    }

    try {
      this.logger.info('正在停止 TaskManager...');
      await this.taskManager.cleanup();

      // 清理依赖项
      if (this.dependencies) {
        await this.dependencies.storageManager.cleanup();
        await this.dependencies.parserManager.cleanup();
        await this.dependencies.fetcherManager.cleanup();
      }

      this.logger.info('✅ TaskManager 已停止');
    } catch (stopError) {
      this.logger.error('停止 TaskManager 失败', {
        error: stopError instanceof Error ? stopError.message : stopError,
        stack: stopError instanceof Error ? stopError.stack : undefined,
      });
    }
  }

  /**
   * 清理应用资源
   */
  public async cleanup(): Promise<void> {
    await this.stop();
    this.taskManager = null;
    this.dependencies = null;
    this.logger.info('✅ AppManager 清理完成');
  }

  /**
   * 获取TaskManager实例
   */
  public getTaskManager(): TaskManager | null {
    return this.taskManager;
  }

  /**
   * 获取activeTasks (为了兼容现有接口)
   * @deprecated 使用TaskManager替代
   */
  public getActiveTasks(): Map<string, any> {
    if (!this.taskManager) {
      return new Map();
    }

    // 将Task[]转换为兼容的Map格式
    const tasks = this.taskManager.getAllTasks();
    const activeTasks = new Map();

    tasks.forEach(task => {
      activeTasks.set(task.id, {
        taskId: task.id,
        name: task.name,
        status: task.status,
        config: task.config,
        stats: task.stats,
        createdAt: new Date(task.createdAt),
        startedAt: task.startedAt ? new Date(task.startedAt) : undefined,
        completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
        stoppedAt: task.stoppedAt ? new Date(task.stoppedAt) : undefined,
      });
    });

    return activeTasks;
  }

  /**
   * 获取NodexApp实例 (为了兼容现有接口)
   * @deprecated 直接使用TaskManager
   */
  public getNodexApp(): any {
    // 返回一个兼容的对象
    return {
      getState: () => 'IDLE',
      getStats: () => this.taskManager?.getCrawlerStats() || {},
      healthCheck: () => Promise.resolve({ status: 'healthy' }),
    };
  }
}
