/**
 * API抓取页面
 * 
 * 整合API抓取表单和结果显示
 */

import React, { useState } from 'react';
import { ApiForm } from '@/components/fetch/ApiForm';
import { ApiResult } from '@/components/fetch/ApiResult';

interface ApiFetchConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers: Record<string, string>;
  body?: string;
  timeout: number;
  retryCount: number;
  useProxy: boolean;
  followRedirects: boolean;
}

interface ApiResult {
  id: string;
  taskId: string;
  url: string;
  method: string;
  statusCode: number;
  headers: Record<string, string>;
  body: string;
  responseTime: number;
  timestamp: string;
  proxyUsed?: string;
  error?: string;
}

export const ApiFetchPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ApiResult | null>(null);
  const [error, setError] = useState<string>('');

  const handleSubmit = async (config: ApiFetchConfig) => {
    setLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/v1/fetch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      const data = await response.json();

      if (data.success) {
        setResult(data.data);
      } else {
        setError(data.error || '请求失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络请求失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">API抓取</h1>
        <p className="text-gray-600 mt-1">
          智能代理轮换的API请求工具，支持多种HTTP方法和自定义配置
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：配置表单 */}
        <div>
          <ApiForm onSubmit={handleSubmit} loading={loading} />
        </div>

        {/* 右侧：结果显示 */}
        <div>
          <ApiResult result={result} loading={loading} error={error} />
        </div>
      </div>
    </div>
  );
};