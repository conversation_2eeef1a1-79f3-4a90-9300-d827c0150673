/**
 * 精简版AI功能路由
 * 
 * 提供AI增强功能的核心API端点
 */

import { Router } from 'express';
import { TaskManager } from '@/app/task-manager';
import { error } from '@/utils';

/**
 * 创建AI功能路由
 */
export function createAIRoutes(
  taskManager: TaskManager
): Router {
  const router = Router();

  /**
   * POST /ai/parse - AI智能解析内容
   */
  router.post('/parse', async (req, res) => {
    try {
      const { content, url, options = {} } = req.body;

      if (!content) {
        return res.status(400).json({
          success: false,
          error: 'Content is required',
        });
      }

      // 使用TaskManager中的AI依赖
      const dependencies = taskManager.getDependencies();
      const aiEngine = dependencies.aiEngine;

      if (!aiEngine) {
        return res.status(503).json({
          success: false,
          error: 'AI engine not available',
        });
      }

      // 使用AI引擎生成响应
      const parseResult = await aiEngine.generateResponse({
        prompt: `请从以下内容中提取结构化数据：\n\n${content}`,
        model: options.model || 'gpt-3.5-turbo',
        maxTokens: options.maxTokens || 2000,
        temperature: options.temperature || 0.1,
      });

      res.json({
        success: true,
        data: {
          extractedData: parseResult.content,
          confidence: 0.8,
          processingTime: 0,
          metadata: {
            url,
            contentLength: content.length,
            timestamp: new Date().toISOString(),
          },
        },
      });
    } catch (err) {
      error('AI解析失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to parse content with AI',
      });
    }
  });

  /**
   * GET /ai/models - 获取可用AI模型
   */
  router.get('/models', async (req, res) => {
    try {
      // 使用TaskManager中的AI依赖
      const dependencies = taskManager.getDependencies();
      const aiEngine = dependencies.aiEngine;

      if (!aiEngine) {
        return res.status(503).json({
          success: false,
          error: 'AI engine not available',
        });
      }

      // 获取可用的AI模型列表
      const models = [
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          provider: 'openai',
          type: 'text-generation',
          maxTokens: 4096,
          available: true,
        },
        {
          id: 'gpt-4',
          name: 'GPT-4',
          provider: 'openai',
          type: 'text-generation',
          maxTokens: 8192,
          available: false, // 需要检查API密钥
        },
        {
          id: 'gemini-pro',
          name: 'Gemini Pro',
          provider: 'google',
          type: 'text-generation',
          maxTokens: 32768,
          available: false, // 需要检查API密钥
        },
      ];

      // 检查当前配置的模型 (使用默认值)
      const currentModel = 'gpt-3.5-turbo';
      const currentProvider = 'openai';
      const aiEnabled = !!aiEngine;

      res.json({
        success: true,
        data: {
          models,
          current: {
            model: currentModel,
            provider: currentProvider,
            enabled: aiEnabled,
          },
          capabilities: {
            textGeneration: true,
            contentExtraction: true,
            structuredParsing: true,
            linkExtraction: true,
          },
        },
      });
    } catch (err) {
      error('获取AI模型失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get AI models',
      });
    }
  });

  return router;
}
