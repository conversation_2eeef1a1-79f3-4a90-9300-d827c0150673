# Nodex Dashboard 环境变量配置示例
# 复制此文件为 .env.local 并根据实际情况修改

# API 服务器地址（Vite 环境变量必须以 VITE_ 开头）
VITE_API_URL=http://localhost:3000

# Socket.IO 服务器地址
VITE_SOCKET_URL=http://localhost:3000

# 开发环境配置
NODE_ENV=development

# 生产环境配置（生产环境时使用）
# NODE_ENV=production
# VITE_API_URL=https://your-domain.com
# VITE_SOCKET_URL=https://your-domain.com

# 注意：
# 1. Vite 环境变量必须以 VITE_ 开头才能在客户端代码中使用
# 2. 开发模式下，Vite 配置中的代理会将 /api 请求转发到 localhost:3001
# 3. 生产环境下，需要确保 VITE_API_URL 指向正确的后端服务器地址
