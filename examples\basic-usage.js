/* eslint-env node */
/**
 * Nodex API 基础使用示例
 * 
 * 演示如何使用 JavaScript 调用 Nodex HTTP API
 * 
 * 运行方法: node examples/basic-usage.js
 */

// 如果是 Node.js 18 以下版本，需要安装 node-fetch
// npm install node-fetch
// const fetch = require('node-fetch');

const API_BASE = 'http://localhost:3000';

async function main() {
  console.log('🚀 Nodex API 基础使用示例');
  console.log('========================');

  try {
    // 1. 健康检查
    console.log('\n1. 检查服务器状态...');
    const healthResponse = await fetch(`${API_BASE}/health`);
    const health = await healthResponse.json();
    console.log('服务器状态:', health.status);

    // 2. 创建爬取任务
    console.log('\n2. 创建爬取任务...');
    const taskConfig = {
      name: '基础示例任务',
      description: 'JavaScript 基础调用示例',
      config: {
        startUrls: ['https://httpbin.org/html'],
        maxDepth: 1,
        concurrency: 2,
        delay: 1000,
        parser: {
          type: 'cheerio',
          selectors: {
            title: 'title',
            headings: 'h1, h2, h3'
          }
        }
      }
    };

    const createResponse = await fetch(`${API_BASE}/api/v1/tasks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(taskConfig)
    });

    const createResult = await createResponse.json();
    console.log("createResult:", createResult)
    const taskId = createResult.data.id;
    console.log('任务已创建，ID:', taskId);

    // 3. 启动任务
    console.log('\n3. 启动任务...');
    const startResponse = await fetch(`${API_BASE}/api/v1/tasks/${taskId}/start`, {
      method: 'POST'
    });
    const startResult = await startResponse.json();
    console.log('启动结果:', startResult.message);

    // 4. 等待任务完成
    console.log('\n4. 等待任务完成...');
    let status = 'pending';
    let attempts = 0;
    const maxAttempts = 20;

    while (status !== 'completed' && status !== 'failed' && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
      
      const statusResponse = await fetch(`${API_BASE}/api/v1/tasks/${taskId}`);
      const statusResult = await statusResponse.json();
      status = statusResult.data.status;
      
      console.log(`状态检查 ${attempts + 1}: ${status}`);
      attempts++;
    }

    if (status === 'completed') {
      // 5. 获取结果
      console.log('\n5. 获取爬取结果...');
      const resultsResponse = await fetch(`${API_BASE}/api/v1/results/${taskId}?limit=5`);
      const results = await resultsResponse.json();
      
      console.log(`共获取到 ${results.data.length} 条结果:`);
      results.data.forEach((item, index) => {
        console.log(`\n结果 ${index + 1}:`);
        console.log(`  URL: ${item.url}`);
        console.log(`  标题: ${item.data?.title || 'N/A'}`);
        console.log(`  时间: ${new Date(item.timestamp).toLocaleString()}`);
      });

      // 6. 获取系统统计
      console.log('\n6. 获取系统统计...');
      const statsResponse = await fetch(`${API_BASE}/api/v1/stats`);
      const stats = await statsResponse.json();
      console.log('系统统计:', JSON.stringify(stats.data, null, 2));

    } else {
      console.log('❌ 任务未能完成，状态:', status);
    }

    console.log('\n✅ 示例执行完成！');

  } catch (error) {
    console.error('❌ 发生错误:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 提示: 请确保 Nodex API 服务器正在运行');
      console.log('启动命令: npm start');
    }
  }
}

// 运行示例
if (require.main === module) {
  main();
}
