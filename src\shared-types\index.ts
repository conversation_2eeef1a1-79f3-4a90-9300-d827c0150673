/**
 * 前后端共享类型定义
 *
 * 这个文件包含了前后端都需要使用的类型定义
 * 前端通过相对路径导入: '../../../src/shared-types'
 * 后端通过别名导入: '@/shared-types'
 */

// =============================================================================
// 基础枚举类型
// =============================================================================

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  RETRYING = 'retrying',
  STOPPED = 'stopped',
}

/**
 * 任务类型枚举
 */
export enum TaskType {
  HTML_CRAWLER = 'html_crawler',
  API_CRAWLER = 'api_crawler',
  SITEMAP_CRAWLER = 'sitemap_crawler',
  CUSTOM_CRAWLER = 'custom_crawler',
  BULK_FETCH = 'bulk_fetch',
  DATA_EXTRACTION = 'data_extraction',
}

/**
 * 任务事件类型
 */
export enum TaskEventType {
  TASK_CREATED = 'task_created',
  CREATED = 'created',
  TASK_STARTED = 'task_started',
  STARTED = 'started',
  PROGRESS = 'progress',
  TASK_COMPLETED = 'task_completed',
  COMPLETED = 'completed',
  TASK_FAILED = 'task_failed',
  FAILED = 'failed',
  TASK_PAUSED = 'task_paused',
  PAUSED = 'paused',
  RESUMED = 'resumed',
  TASK_STOPPED = 'task_stopped',
  STOPPED = 'stopped',
  CANCELLED = 'cancelled',
  RETRIED = 'retried',
  TASK_STATS_UPDATED = 'task_stats_updated',
  TASK_DELETED = 'task_deleted',
}

/**
 * 代理状态枚举
 */
export enum ProxyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  TESTING = 'testing',
}

/**
 * 获取器类型枚举
 */
export enum FetcherType {
  HTTP = 'http',
  BROWSER = 'browser',
}

/**
 * 遍历策略枚举
 */
export enum TraversalStrategy {
  BFS = 'BFS',
  DFS = 'DFS',
}

// =============================================================================
// 请求/响应类型
// =============================================================================

/**
 * 创建任务请求接口
 */
export interface CreateTaskRequest {
  name: string;
  type: TaskType;
  urls: string[];
  config?: Partial<TaskConfig>;
  priority?: number;
  metadata?: Record<string, any>;
}

/**
 * 创建任务响应接口
 */
export interface CreateTaskResponse {
  success?: boolean;
  taskId: string;
  status: TaskStatus;
  message?: string;
}

/**
 * API 任务创建请求
 */
export interface CreateApiTaskRequest extends CreateTaskRequest {
  type: TaskType.API_CRAWLER;
  config: TaskConfig & {
    url?: string;
    apiKey?: string;
    apiEndpoint: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    body?: any;
    pagination?: {
      type: 'offset' | 'cursor' | 'page';
      limit: number;
      offsetParam?: string;
      cursorParam?: string;
      pageParam?: string;
    };
  };
}

/**
 * 爬虫任务创建请求
 */
export interface CreateCrawlerTaskRequest extends CreateTaskRequest {
  type: TaskType.HTML_CRAWLER;
  config: TaskConfig & {
    maxDepth: number;
    respectRobotsTxt?: boolean;
    crawlDelay?: number;
  };
}

/**
 * 任务查询参数
 */
export interface TaskListQuery {
  status?: TaskStatus[];
  type?: TaskType[];
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'priority';
  sortOrder?: 'asc' | 'desc';
  search?: string;
  tags?: string[];
  userId?: string;
  dateFrom?: string;
  dateTo?: string;
}

/**
 * 任务操作结果
 */
export interface TaskOperationResult {
  success: boolean;
  taskId: string;
  message?: string;
  error?: string;
  data?: any;
}

/**
 * 爬虫统计信息接口
 */
export interface CrawlerStats {
  totalUrls: number;
  processedUrls: number;
  failedUrls: number;
  queuedUrls: number;
  queueSize: number;
  errorUrls: number;
  totalTasks: number;
  runningTasks: number;
  pausedTasks: number;
  completedTasks: number;
  failedTasks: number;
  stoppedTasks: number;
  successRate: number;
  averageSpeed: number;
  extractedItems: number;
}

/**
 * API 结果接口
 */
export interface ApiResult {
  id?: string;
  taskId?: string;
  url: string;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  data: any;
  responseTime: number;
  timestamp: Date | string;
  error?: string;
  proxyUsed?: boolean;
}

/**
 * 页面结果接口
 */
export interface PageResult {
  url: string;
  title?: string;
  content?: string;
  links?: LinkInfo[];
  images?: string[];
  metadata?: Record<string, any>;
  extractedData?: any;
  responseTime: number;
  timestamp: Date | string;
  statusCode: number;
  error?: string;
}

/**
 * 链接信息接口
 */
export interface LinkInfo {
  url: string;
  text?: string;
  title?: string;
  rel?: string;
  depth: number;
  parentUrl?: string;
  processed?: boolean;
  discovered?: boolean;
}

// =============================================================================
// 任务相关接口
// =============================================================================

/**
 * 任务配置基础接口
 */
export interface TaskConfig {
  name?: string;
  description?: string;
  type?: TaskType;
  urls?: string[];
  startUrls?: string[];
  maxDepth?: number;
  maxPages?: number;
  concurrency?: number;
  maxConcurrency?: number;
  delay?: number;
  delayMs?: number;
  timeout?: number;
  retries?: number;
  retryCount?: number;
  useProxy?: boolean;
  enableProxy?: boolean;
  enableAI?: boolean;
  useBrowser?: boolean;
  followRedirects?: boolean;
  respectRobots?: boolean;
  respectRobotsTxt?: boolean;
  userAgent?: string;
  headers?: Record<string, string>;
  customHeaders?: Record<string, string>;
  outputFormat?: 'json' | 'csv' | 'xml';
  selectors?: {
    title?: string;
    content?: string;
    links?: string;
    images?: string;
    [key: string]: string | undefined;
  };
  filters?: {
    allowedDomains?: string[];
    blockedDomains?: string[];
    urlPatterns?: string[];
    contentTypes?: string[];
  };
  allowedDomains?: string[];
  useReadability?: boolean;
  includeRawHtml?: boolean;
  cookies?: string;
  proxy?: {
    enabled: boolean;
    host?: string;
    port?: number;
    username?: string;
    password?: string;
  };
  storage?: {
    type: 'memory' | 'mongodb' | 'file';
    config?: Record<string, any>;
  };
  schedule?: {
    enabled: boolean;
    cron?: string;
    interval?: number;
  };
  notifications?: {
    enabled: boolean;
    email?: string;
    webhook?: string;
  };
}

/**
 * 任务统计信息
 */
export interface TaskStats {
  total: number;
  processed: number;
  success: number;
  failed: number;
  pending?: number;
  inQueue?: number;
  errors: number;
  startTime?: Date | string;
  endTime?: Date | string;
  duration?: number;
  averageResponseTime?: number;
  requestsPerSecond?: number;
  bytesDownloaded?: number;
  // Legacy properties for compatibility
  totalUrls?: number;
  processedUrls?: number;
  failedUrls?: number;
  queueSize?: number;
  successRate?: number;
  averageProcessingTime?: number;
  extractedItems?: number;
}

/**
 * 任务完整定义
 */
export interface Task {
  id: string;
  type: TaskType;
  name?: string;
  description?: string;
  status: TaskStatus;
  config: TaskConfig;
  stats: TaskStats;
  urls: string[];
  startUrls?: string[];
  results?: any[];
  createdAt: Date | string;
  startedAt?: Date | string;
  completedAt?: Date | string;
  stoppedAt?: Date | string;
  updatedAt?: Date | string;
  userId?: string;
  tags?: string[];
  priority?: number;
  error?: string;
  progress?: number;
  metadata?: Record<string, any>;
}

/**
 * 任务日志
 */
export interface TaskLog {
  id: string;
  taskId: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

/**
 * 任务事件
 */
export interface TaskEvent {
  id: string;
  taskId: string;
  type: TaskEventType;
  data: any;
  timestamp: string;
}

// =============================================================================
// 系统相关接口
// =============================================================================

/**
 * 系统统计信息
 */
export interface SystemStats {
  uptime: number;
  memory: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  disk?: {
    used: number;
    total: number;
    percentage: number;
  };
  network?: {
    bytesReceived: number;
    bytesSent: number;
  };
}

/**
 * 队列统计信息
 */
export interface QueueStats {
  queueName: string;
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
}

/**
 * 健康检查结果
 */
export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  services: Record<
    string,
    {
      status: 'healthy' | 'unhealthy';
      message?: string;
      responseTime?: number;
    }
  >;
  timestamp: Date;
}

// =============================================================================
// 代理相关接口
// =============================================================================

/**
 * 代理配置
 */
export interface ProxyConfig {
  id: string;
  name: string;
  host: string;
  port: number;
  username?: string;
  password?: string;
  type: 'http' | 'https' | 'socks4' | 'socks5';
  protocol?: 'http' | 'https' | 'socks4' | 'socks5'; // 兼容性字段
  auth?: {
    username: string;
    password: string;
  };
  status: ProxyStatus;
  lastChecked?: string;
  responseTime?: number;
  successRate?: number;
  totalRequests?: number;
  failedRequests?: number;
  createdAt: string;
  updatedAt: string;
  // 地域信息字段
  countryCode?: string;
  cityName?: string;
  asnName?: string;
  asnNumber?: number;
  highCountryConfidence?: boolean;
}

/**
 * 代理测试结果
 */
export interface ProxyTestResult {
  proxyId: string;
  success: boolean;
  responseTime: number;
  error?: string;
  timestamp: string;
}

// =============================================================================
// API 响应接口
// =============================================================================

/**
 * 标准API响应
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

/**
 * 分页响应
 */
export interface PaginatedResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  timestamp: string;
}

/**
 * 批量操作结果
 */
export interface BulkOperationResult {
  success: boolean;
  total: number;
  successful: number;
  failed: number;
  errors: string[];
  timestamp: string;
}

// =============================================================================
// WebSocket 事件接口
// =============================================================================

/**
 * WebSocket 事件类型
 */
export type WebSocketEventType =
  | 'task_status_changed'
  | 'task_stats_updated'
  | 'url_processed'
  | 'data_extracted'
  | 'task_error'
  | 'task_completed'
  | 'system_stats_updated'
  | 'queue_stats_updated';

/**
 * WebSocket 事件数据
 */
export interface WebSocketEvent<T = any> {
  type: WebSocketEventType;
  data: T;
  timestamp: string;
}

// =============================================================================
// 类型守卫和工具函数
// =============================================================================

/**
 * 检查是否为有效的任务状态
 */
export function isValidTaskStatus(status: string): status is TaskStatus {
  return Object.values(TaskStatus).includes(status as TaskStatus);
}

/**
 * 检查是否为有效的任务类型
 */
export function isValidTaskType(type: string): type is TaskType {
  return Object.values(TaskType).includes(type as TaskType);
}

/**
 * 检查是否为有效的代理状态
 */
export function isValidProxyStatus(status: string): status is ProxyStatus {
  return Object.values(ProxyStatus).includes(status as ProxyStatus);
}

/**
 * 将字符串转换为任务状态
 */
export function toTaskStatus(status: string): TaskStatus {
  if (isValidTaskStatus(status)) {
    return status;
  }
  throw new Error(`Invalid task status: ${status}`);
}

/**
 * 将字符串转换为任务类型
 */
export function toTaskType(type: string): TaskType {
  if (isValidTaskType(type)) {
    return type;
  }
  throw new Error(`Invalid task type: ${type}`);
}

/**
 * 创建标准API响应
 */
export function createApiResponse<T>(
  success: boolean,
  data?: T,
  error?: string,
  message?: string
): ApiResponse<T> {
  return {
    success,
    data,
    error,
    message,
    timestamp: new Date().toISOString(),
  };
}

/**
 * 创建分页响应
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  pageSize: number,
  total: number
): PaginatedResponse<T> {
  return {
    success: true,
    data,
    pagination: {
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize),
    },
    timestamp: new Date().toISOString(),
  };
}

// =============================================================================
// 导出完成 - 所有类型已通过接口定义直接导出
// =============================================================================

// 额外导出已在上面定义，此处删除重复定义以避免冲突
