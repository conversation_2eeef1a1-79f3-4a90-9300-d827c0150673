/**
 * 策略管理器
 *
 * 管理爬虫的各种策略（遍历策略、优先级策略等）
 */

import { EventEmitter } from 'events';
import { getLogger } from '@/utils';
import { TraversalStrategy } from '@/shared-types';

/**
 * 优先级策略配置
 */
export interface PriorityStrategy {
  name: string;
  calculate: (url: string, depth: number, context?: any) => number;
  description: string;
}

/**
 * 策略配置
 */
export interface StrategyConfig {
  traversal: TraversalStrategy;
  priority?: PriorityStrategy;
  maxRetries: number;
  retryDelay: number;
  timeout: number;
}

/**
 * 策略管理器接口
 */
export interface IStrategyManager {
  configure(config: Partial<StrategyConfig>): void;
  getStrategy<T>(name: string): T | null;
  registerStrategy<T>(name: string, strategy: T): void;
  calculatePriority(url: string, depth: number, context?: any): number;
  getTraversalStrategy(): TraversalStrategy;
  getConfig(): StrategyConfig;
}

/**
 * 内置优先级策略
 */
const BUILTIN_PRIORITY_STRATEGIES: Record<string, PriorityStrategy> = {
  depth: {
    name: 'depth',
    description: '基于深度的优先级策略',
    calculate: (url: string, depth: number) => {
      // 深度越小，优先级越高
      return Math.max(100 - depth * 10, 1);
    },
  },

  domain: {
    name: 'domain',
    description: '基于域名的优先级策略',
    calculate: (url: string, depth: number) => {
      try {
        const urlObj = new URL(url);
        const domain = urlObj.hostname;

        // 常见重要域名优先级更高
        const importantDomains = ['github.com', 'stackoverflow.com', 'wikipedia.org'];
        const isImportant = importantDomains.some(d => domain.includes(d));

        return isImportant ? 90 : 50;
      } catch {
        return 30;
      }
    },
  },

  fileType: {
    name: 'fileType',
    description: '基于文件类型的优先级策略',
    calculate: (url: string, depth: number) => {
      try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname.toLowerCase();

        // HTML页面优先级最高
        if (pathname.endsWith('.html') || pathname.endsWith('/') || !pathname.includes('.')) {
          return 80;
        }

        // 文档类型中等优先级
        if (pathname.match(/\.(pdf|doc|docx|txt)$/)) {
          return 60;
        }

        // 图片和媒体文件优先级较低
        if (pathname.match(/\.(jpg|jpeg|png|gif|mp4|mp3|avi)$/)) {
          return 20;
        }

        return 40;
      } catch {
        return 30;
      }
    },
  },

  combined: {
    name: 'combined',
    description: '综合优先级策略',
    calculate: (url: string, depth: number, context?: any) => {
      const depthStrategy = BUILTIN_PRIORITY_STRATEGIES.depth;
      const domainStrategy = BUILTIN_PRIORITY_STRATEGIES.domain;
      const fileTypeStrategy = BUILTIN_PRIORITY_STRATEGIES.fileType;

      if (!depthStrategy || !domainStrategy || !fileTypeStrategy) {
        return 50; // 默认优先级
      }

      const depthScore = depthStrategy.calculate(url, depth, context);
      const domainScore = domainStrategy.calculate(url, depth, context);
      const fileTypeScore = fileTypeStrategy.calculate(url, depth, context);

      // 加权平均
      return Math.round(depthScore * 0.4 + domainScore * 0.3 + fileTypeScore * 0.3);
    },
  },
};

/**
 * 策略管理器实现
 */
export class StrategyManager extends EventEmitter implements IStrategyManager {
  private config: StrategyConfig;
  private strategies: Map<string, any> = new Map();
  private logger = getLogger('StrategyManager');

  constructor(initialConfig?: Partial<StrategyConfig>) {
    super();

    // 默认配置
    this.config = {
      traversal: TraversalStrategy.BFS,
      priority: BUILTIN_PRIORITY_STRATEGIES.combined,
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000,
      ...initialConfig,
    };

    // 注册内置策略
    this.registerBuiltinStrategies();

    this.logger.info('策略管理器初始化完成', { config: this.config });
  }

  /**
   * 配置策略
   */
  configure(config: Partial<StrategyConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...config };

    this.logger.info('策略配置更新', { oldConfig, newConfig: this.config });
    this.emit('configChanged', { oldConfig, newConfig: this.config });
  }

  /**
   * 获取策略
   */
  getStrategy<T>(name: string): T | null {
    return this.strategies.get(name) || null;
  }

  /**
   * 注册策略
   */
  registerStrategy<T>(name: string, strategy: T): void {
    this.strategies.set(name, strategy);
    this.logger.debug('注册策略', { name, strategy });
    this.emit('strategyRegistered', { name, strategy });
  }

  /**
   * 计算优先级
   */
  calculatePriority(url: string, depth: number, context?: any): number {
    try {
      if (!this.config.priority) {
        return 50; // 默认优先级
      }

      const priority = this.config.priority.calculate(url, depth, context);

      // 确保优先级在合理范围内
      return Math.max(1, Math.min(100, priority));
    } catch (error) {
      this.logger.warn('计算优先级失败', { url, depth, error });
      return 50;
    }
  }

  /**
   * 获取遍历策略
   */
  getTraversalStrategy(): TraversalStrategy {
    return this.config.traversal;
  }

  /**
   * 获取配置
   */
  getConfig(): StrategyConfig {
    return { ...this.config };
  }

  /**
   * 获取可用的优先级策略
   */
  getAvailablePriorityStrategies(): PriorityStrategy[] {
    return Object.values(BUILTIN_PRIORITY_STRATEGIES);
  }

  /**
   * 设置优先级策略
   */
  setPriorityStrategy(strategyName: string): void {
    const strategy = BUILTIN_PRIORITY_STRATEGIES[strategyName];
    if (strategy) {
      this.configure({ priority: strategy });
      this.logger.info('优先级策略已切换', { strategyName });
    } else {
      throw new Error(`Unknown priority strategy: ${strategyName}`);
    }
  }

  /**
   * 注册内置策略
   */
  private registerBuiltinStrategies(): void {
    // 注册遍历策略
    Object.values(TraversalStrategy).forEach(strategy => {
      this.registerStrategy(`traversal.${strategy}`, strategy);
    });

    // 注册优先级策略
    Object.entries(BUILTIN_PRIORITY_STRATEGIES).forEach(([name, strategy]) => {
      this.registerStrategy(`priority.${name}`, strategy);
    });
  }

  /**
   * 验证URL是否应该被爬取
   */
  shouldCrawlUrl(url: string, depth: number, context?: any): boolean {
    try {
      // 基本URL验证
      new URL(url);

      // 深度检查
      const maxDepth = context?.maxDepth || 5;
      if (depth > maxDepth) {
        return false;
      }

      // 文件类型检查
      const pathname = new URL(url).pathname.toLowerCase();
      const excludedExtensions = ['.css', '.js', '.ico', '.xml', '.txt'];
      if (excludedExtensions.some(ext => pathname.endsWith(ext))) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取重试配置
   */
  getRetryConfig(): { maxRetries: number; retryDelay: number } {
    return {
      maxRetries: this.config.maxRetries,
      retryDelay: this.config.retryDelay,
    };
  }

  /**
   * 获取统计信息
   */
  getStats(): any {
    return {
      registeredStrategies: this.strategies.size,
      currentConfig: this.config,
      availablePriorityStrategies: Object.keys(BUILTIN_PRIORITY_STRATEGIES),
    };
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.strategies.clear();
    this.removeAllListeners();
    this.logger.info('策略管理器已清理');
  }
}
