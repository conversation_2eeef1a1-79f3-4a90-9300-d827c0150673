/**
 * OpenAI 提供商实现
 */

import {
  <PERSON><PERSON><PERSON>rov<PERSON>,
  AIProvider,
  AIResponse,
  AIRequestOptions,
  AIModelConfig,
} from '@/interfaces';
import { info, error } from '@/utils';

/**
 * OpenAI API 响应接口
 */
interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  model: string;
}

/**
 * OpenAI 提供商实现类
 */
export class OpenAIProvider implements IAIProvider {
  public readonly name: AIProvider = 'openai';
  public readonly version = '1.0.0';

  private apiKey: string;
  private baseUrl: string;
  private defaultModel: AIModelConfig;
  private stats = {
    totalRequests: 0,
    totalTokens: 0,
    totalResponseTime: 0,
    errorCount: 0,
  };

  constructor(config: {
    apiKey: string;
    baseUrl?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
  }) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://api.openai.com/v1';
    this.defaultModel = {
      model: config.model || 'gpt-3.5-turbo',
      maxTokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.1,
    };
  }

  /**
   * 初始化提供商
   */
  public async initialize(): Promise<void> {
    // 测试 API 连接
    try {
      await this.testConnection();
      info('OpenAI provider initialized successfully');
    } catch (err) {
      error('Failed to initialize OpenAI provider:', err);
      throw err;
    }
  }

  /**
   * 发送聊天请求
   */
  public async chat(
    messages: Array<{
      role: 'system' | 'user' | 'assistant';
      content: string;
    }>,
    options: AIRequestOptions = {}
  ): Promise<AIResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      const model = options.model || this.defaultModel;
      const timeout = options.timeout || 30000;

      const requestBody: any = {
        model: model.model,
        messages,
        max_tokens: model.maxTokens,
        temperature: model.temperature,
        stream: options.stream || false,
      };

      // 只添加非 undefined 的可选参数
      if (model.topP !== undefined) requestBody.top_p = model.topP;
      if (model.frequencyPenalty !== undefined) requestBody.frequency_penalty = model.frequencyPenalty;
      if (model.presencePenalty !== undefined) requestBody.presence_penalty = model.presencePenalty;
      if (model.stopSequences !== undefined) requestBody.stop = model.stopSequences;

      const response = await this.makeRequest('/chat/completions', requestBody, timeout);
      const responseTime = Date.now() - startTime;

      this.stats.totalResponseTime += responseTime;
      this.stats.totalTokens += response.usage.total_tokens;

      return {
        content: response.choices[0]?.message.content || '',
        tokensUsed: {
          prompt: response.usage.prompt_tokens,
          completion: response.usage.completion_tokens,
          total: response.usage.total_tokens,
        },
        responseTime,
        model: response.model,
        finishReason: response.choices[0]?.finish_reason as any,
        rawResponse: response,
      };

    } catch (error) {
      this.stats.errorCount++;
      throw this.handleError(error);
    }
  }

  /**
   * 发送完成请求
   */
  public async complete(prompt: string, options: AIRequestOptions = {}): Promise<AIResponse> {
    // 将完成请求转换为聊天请求
    const messages = [
      { role: 'user' as const, content: prompt }
    ];

    return await this.chat(messages, options);
  }

  /**
   * 检查健康状态
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.testConnection();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取使用统计
   */
  public getUsageStats() {
    return {
      totalRequests: this.stats.totalRequests,
      totalTokens: this.stats.totalTokens,
      averageResponseTime: this.stats.totalRequests > 0 
        ? this.stats.totalResponseTime / this.stats.totalRequests 
        : 0,
      errorRate: this.stats.totalRequests > 0 
        ? this.stats.errorCount / this.stats.totalRequests 
        : 0,
    };
  }

  /**
   * 测试连接
   */
  private async testConnection(): Promise<void> {
    const testMessages = [
      { role: 'user' as const, content: 'Hello' }
    ];

    await this.chat(testMessages, {
      model: { ...this.defaultModel, maxTokens: 10 },
      timeout: 10000,
    });
  }

  /**
   * 发送 HTTP 请求
   */
  private async makeRequest(endpoint: string, body: any, timeout: number): Promise<OpenAIResponse> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(body),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      return await response.json();

    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: any): Error {
    let message = 'OpenAI request failed';
    let code = 'OPENAI_ERROR';

    if (error.name === 'AbortError') {
      code = 'TIMEOUT';
      message = 'OpenAI request timed out';
    } else if (error.message) {
      message = error.message;
      
      if (error.message.includes('401')) {
        code = 'UNAUTHORIZED';
      } else if (error.message.includes('429')) {
        code = 'RATE_LIMITED';
      } else if (error.message.includes('500')) {
        code = 'SERVER_ERROR';
      }
    }

    const enhancedError = new Error(message);
    (enhancedError as any).code = code;
    (enhancedError as any).provider = 'openai';
    (enhancedError as any).originalError = error;

    return enhancedError;
  }

  /**
   * 设置 API 密钥
   */
  public setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  /**
   * 设置基础 URL
   */
  public setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
  }

  /**
   * 设置默认模型配置
   */
  public setDefaultModel(model: AIModelConfig): void {
    this.defaultModel = { ...model };
  }

  /**
   * 获取默认模型配置
   */
  public getDefaultModel(): AIModelConfig {
    return { ...this.defaultModel };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalRequests: 0,
      totalTokens: 0,
      totalResponseTime: 0,
      errorCount: 0,
    };
  }

  /**
   * 估算 token 数量
   */
  public estimateTokens(text: string): number {
    // 简单的 token 估算：大约 4 个字符 = 1 个 token
    return Math.ceil(text.length / 4);
  }

  /**
   * 检查内容是否超过 token 限制
   */
  public isContentTooLong(content: string, maxTokens?: number): boolean {
    const estimatedTokens = this.estimateTokens(content);
    const limit = maxTokens || this.defaultModel.maxTokens || 4000;
    return estimatedTokens > limit * 0.8; // 留 20% 的余量
  }

  /**
   * 截断内容以适应 token 限制
   */
  public truncateContent(content: string, maxTokens?: number): string {
    const limit = maxTokens || this.defaultModel.maxTokens || 4000;
    const maxChars = Math.floor(limit * 0.8 * 4); // 留 20% 余量，4 字符/token
    
    if (content.length <= maxChars) {
      return content;
    }
    
    return content.substring(0, maxChars) + '...';
  }
}
