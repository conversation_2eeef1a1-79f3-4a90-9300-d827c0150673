# Nodex: 高性能智能爬虫系统技术设计文档 v2.0

## 1. 文档概述

### 1.1. 项目愿景与背景
Nodex 的核心目标是构建一个开源的、企业级的、高性能的 NodeJS 爬虫平台。它旨在解决通用爬虫领域的痛点，**实现对任意主题，自动化地获取海量、高质量、有价值的数据**。本文档为 Nodex v2.0 提供全面、详细的技术架构和设计方案，重点突出其智能性、扩展性和反屏蔽能力。

### 1.2. 设计目标
- **高性能 (High-Performance):** 极致的并发性能和资源利用率。
- **智能化 (Intelligence):** 引入 AI 模块，实现智能策略规划、内容提取和反爬对抗。
- **高扩展性 (High-Scalability):** 采用可插拔的模块化设计，支持功能扩展和分布式部署。
- **强对抗性 (Anti-Detection):** 内置多种先进的下载器和反屏蔽策略，有效应对主流网站的反爬虫机制。
- **内置电池 (Battery-Included):** 内置Google Search, Bing Search, DuckDuckGo 等搜索引擎，实现自动抓取计划。也可自己配置引擎（只需实现了搜索引擎接口即可）。
- **易用性与灵活性 (Usability & Flexibility):** 提供简洁的配置和强大的 API，支持灵活的抓取策略。

---

## 2. 系统架构设计

我们采用 **AI 增强的、基于分布式队列的生产者-消费者架构**。此架构将爬虫流程深度解耦，并通过引入 AI 模块，赋予系统“思考”和“规划”的能力。

### 2.1. 架构图 v2.0

```
+---------------------------------------------------------------------------------...+
|                                  用户 / 配置层 / AI Planner                       |
|                       (config.js / API / CLI)                                   |
+---------------------------------------------------------------------------------...+
                                        |
                                        v
+---------------------------------------------------------------------------------...+
|                                  主控调度模块 (Master)                          |
| - 读取配置, 管理抓取策略 (BFS/DFS)                                                |
| - 初始化种子URL (可由AI Planner生成)                                            |
| - 监控各模块状态                                                                |
+---------------------------------------------------------------------------------...+
         |                                      ^
         | (URL任务)                            | (状态/统计)
         v                                      |
+---------------------------------------------------------------------------------...+
|                                  URL 任务队列 (Message Queue)                   |
|                                  (e.g., Redis BullMQ)                           |
| - 存储待抓取URL (支持优先级 for BFS/DFS)                                        |
| - 任务去重                                                                      |
+---------------------------------------------------------------------------------...+
         | (消费URL)                              ^ (新URL/解析结果)
         |                                      |
+--------v--------------------------------------+--------------------------------+
| 下载模块 (Fetcher) - 可水平扩展                | 解析模块 (Parser) - 可水平扩展         |
|                                              |                                |
| - HTTP Client (Got/Axios)                    | - 从队列获取下载内容                 |
| - Headless Browser (Puppeteer/Playwright)    | - 解析器管理器 (Parser Manager)      |
|   - with Anti-Detection (puppeteer-extra)    |   - CheerioParser, RegexParser     |
| - 代理管理器 (Proxy Manager)                 |   - AIParser (调用AI模块)          |
| - 将(URL, Content)送入内容队列               | - 将新URL送入URL任务队列             |
+---------------------------------------------+--------------------------------+
                   |                                       |
(HTML/Content)     | (结构化数据)                          | (调用)
                   v                                       v
+---------------------------------------------------------------------------------...+
|                                  AI 赋能模块 (AI Engine)                        |
| - 智能内容提取 (LLM)                                                            |
| - 数据清洗与生成                                                                |
| - 抓取策略规划                                                                  |
+---------------------------------------------------------------------------------...+
                   |
                   v
+---------------------------------------------------------------------------------...+
|                                  数据存储与索引模块 (Storage)                   |
| - DB (MongoDB / PostgreSQL)                                                     |
| - Search Index (Elasticsearch / MeiliSearch)                                    |
+---------------------------------------------------------------------------------...+
```

### 2.2. 核心模块详解

- **主控调度模块 (Master):** 爬虫的“大脑”。新增**遍历策略管理**，根据用户配置（广度优先/深度优先）调整URL入队的优先级。可接收由 **AI Planner** 生成的种子URL和抓取方案。

- **URL任务队列 (URL Task Queue):** 系统的核心枢纽。利用 **BullMQ 的优先级** 特性实现遍历策略：
    - **广度优先 (BFS):** 新发现的URL赋予较低的优先级，实现层级遍历。
    - **深度优先 (DFS):** 新发现的URL赋予较高的优先级，优先探索“深层”链接。

- **下载模块 (Fetcher):** 系统的“手脚”，全面升级。负责执行网络请求，根据任务需求和目标网站特点，动态选择下载器。
    - **标准下载器:** `Got`, `Axios` 用于请求API或静态页面。
    - **无头浏览器下载器:** `Puppeteer`, `Playwright` 用于执行JS渲染的动态页面。
    - **反屏蔽增强:** 必须集成 `puppeteer-extra` 及其 `stealth` 插件，以规避Cloudflare、Akamai等主流反爬服务。

- **解析模块 (Parser):** 系统的“眼睛”，采用**可插拔接口**设计。不再硬编码解析逻辑，而是通过一个**解析器管理器 (Parser Manager)** 来动态加载和选择解析器。

- **代理管理器 (Proxy Manager):**
  - **职责:** 作为下载模块的一部分，集中管理代理IP池。
  - **功能:**
    - **代理策略:** 实现轮询（Round-robin）、随机（Random）、粘性会话（Sticky Session）等多种代理切换策略。
    - **健康检查:** 定期检测代理可用性，自动移除失效代理，恢复后自动加入。

- **AI 赋能模块 (AI Engine):** **本次升级的核心**。作为一个独立的服务模块，为其他模块提供AI能力。

- **数据存储与索引模块 (Storage):** 系统的“仓库”。除传统数据库外，新增**搜索引擎对接**能力，方便后续的数据检索和分析。

---

## 3. 技术选型

- **核心框架:** **Node.js** (LTS), **TypeScript**
- **任务队列:** **BullMQ** + **Redis**
- **下载器 (Fetcher) 技术栈:**
  - **HTTP 客户端:** **Got** (首选), **Axios**
  - **无头浏览器:** **Puppeteer** (社区生态更成熟), **Playwright** (API更现代)
  - **反屏蔽:** **puppeteer-extra** (及其 `stealth` 插件)
- **HTML/DOM 解析:** **Cheerio** (用于静态解析)
- **数据库:** **MongoDB** (灵活), **PostgreSQL** (强大)
- **搜索引擎:** **Elasticsearch** (功能全面), **MeiliSearch** (轻量易用)
- **AI/LLM 集成:** 通过 API 调用 **OpenAI (GPT系列)**, **Google (Gemini系列)** 或部署本地开源模型。

---

## 4. 功能模块详细设计

### 4.1. 全局配置 (config.ts)
配置项进行精简和调整，解析器部分被移除。

```typescript
// config.ts
export const config = {
    // 1. 任务定义
    task: {
        seedUrls: ['https://example.com'],
        traversalStrategy: 'BFS', // 'BFS' (广度优先) or 'DFS' (深度优先)
    },

    // 2. 抓取范围与策略
    crawlScope: {
        allowedDomains: ['example.com', 'sub.example.com'], // 域名白名单
        disallowedPatterns: [/logout/, /ads\.php/], // URL路径黑名单 (正则表达式)
        followSubdomains: true, // 是否抓取子域名
        ignoreWWW: true, // 是否将 example.com 和 www.example.com 视为同一域名
    },

    // 3. 深度控制
    depthControl: {
        global: 5, // 全局最大抓取深度
        perDomain: { // 特定域名的深度覆盖全局配置
            'another.com': 3
        }
    },

    // 4. 性能与并发
    concurrency: {
        maxConcurrentRequests: 10, // 全局最大并发请求数
        delayBetweenRequestsMs: 100, // 每个请求之间的最小延迟（毫秒）
        domainSpecificDelay: { // 特定域名的延迟
            'example.com': 500
        }
    },

    // 5. 代理配置
    proxy: {
        enabled: true,
        proxies: [ // 代理服务器列表
            { host: '127.0.0.1', port: 8080, auth: { username: 'user', password: 'pw' } },
            { host: '127.0.0.1', port: 8081 }
        ],
        extraProxyConfig: './proxies.json', // 额外代理配置文件路径（JSON格式）
        strategy: 'round-robin', // 'round-robin', 'random', 'sticky'
        healthCheck: {
            enabled: true,
            intervalMs: 60000, // 60秒检查一次
            testUrl: 'https://www.google.com'
        }
    },

    // 6. 解析器配置

    // 7. 存储配置
    storage: {
        type: 'mongodb', // 'mongodb', 'json', 'console'
        connectionString: 'mongodb://localhost:27017/crawler_data',
        collection: 'products'
    },

    // 8. 其他
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.82 Safari/537.36',
    respectRobotsTxt: true, // 严格遵守 robots.txt
    requestTimeoutMs: 30000, // 30秒请求超时
};
```

### 4.2. 可插拔解析器模块 (Parser Module)
这是设计的关键变更。我们定义一个标准的解析器接口，用户可以实现自己的解析器并注册到系统中。

```typescript
// interfaces/parser.ts
export interface ParserInput {
    content: string;      // 页面内容 (HTML, JSON, etc.)
    url: string;          // 页面URL
    fetcherType: 'http' | 'browser'; // 下载方式
    // ... 其他上下文信息
}

export interface ParserOutput {
    extractedData: Record<string, any> | null;
    newUrls: string[];
}

export interface IParser {
    // 判断此解析器是否适用于该页面
    canParse(input: ParserInput): boolean;
    // 执行解析
    parse(input: ParserInput): Promise<ParserOutput>;
}
```
**解析器管理器 (ParserManager)** 会按顺序检查所有已注册的解析器，使用第一个 `canParse` 返回 `true` 的解析器。

### 4.3. AI 赋能模块 (AI Engine)
此模块提供统一的API接口，供系统其他部分调用。
- **`POST /ai/extract`**:
  - **输入:** `{ content: string, schema: Record<string, string> }` (例如: `{ "title": "文章主标题", "author": "作者姓名" }`)
  - **输出:** `{ extractedData: Record<string, any> }`
  - **功能:** 调用LLM，根据用户提供的自然语言描述 (`schema`)，从非结构化文本中提取数据。这是对传统CSS选择器的革命性替代。

- **`POST /ai/plan`**:
  - **输入:** `{ topic: string }` (例如: "2024年新能源汽车技术趋势")
  - **输出:** `{ seedUrls: string[], keywords: string[], potentialSchema: ... }`
  - **功能:** 根据一个主题，自动生成爬虫启动所需的种子URL、相关关键词等。

---

## 5. 性能与可扩展性

- **垂直扩展:**
  - **Worker Threads:** 对于CPU密集型的解析任务，可以使用Node.js的`worker_threads`模块，将解析工作移出主事件循环，避免阻塞。
- **水平扩展:**
  - **分布式Workers:** 由于采用队列架构，下载模块和解析模块可以作为独立的Node.js进程部署在多台服务器上。它们都连接到同一个中心化的Redis实例，实现了“无状态”的Worker，可以无限水平扩展。
- **速率控制:**
  - **全局限速:** BullMQ的速率限制功能可以轻松实现全局`maxConcurrentRequests`。
  - **域名级限速:** 可以为每个域名创建一个独立的队列或使用BullMQ的分组限速功能，从而实现对特定域名更精细的访问频率控制，这是非常重要的“爬虫礼仪”。
架构的分布式特性保持不变，允许 `Fetcher` 和 `Parser` 模块独立水平扩展。无头浏览器的使用会显著增加资源消耗，建议建立独立的 **JS渲染服务集群** (Browser-as-a-Service)，集中管理浏览器实例，并通过API供 `Fetcher` 调用。

---

## 6. 易用性与生态

### 6.1. 命令行工具 (CLI)
CLI更名为 `nodex`。
- `nodex run --task my_task.ts`: 使用指定的任务配置启动爬虫。
- `nodex plan --topic "..." --ai`: 调用AI模块为主题生成抓取计划。

### 6.2. 监控面板
自研一个Web界面，可视化地展示队列状态、任务进度、成功/失败率、Worker健康状况等信息。

### 6.3. 未来功能扩展
- **JS渲染即服务:** 建立一个独立的`Puppeteer`服务集群。当解析模块遇到需要JS渲染的URL时，不是自己启动浏览器，而是将URL发送到这个服务，获取渲染后的HTML，这样可以集中管理资源消耗巨大的浏览器实例。
- **机器学习集成:**
  - **智能边界识别:** 使用ML模型自动识别“下一页”按钮、列表页和详情页，减少人工配置。
  - **反爬虫策略识别:** 分析请求失败的模式，自动调整UA、延迟或切换代理策略。
- **增量抓取:** 通过比对页面内容的哈希值，判断页面是否更新，只抓取和处理有变化的页面。

---

## 7. 总结
Nodex v2.0 的设计在原有的高性能分布式架构基础上，通过**深度集成AI能力**、**强化反屏蔽下载器**和**实现完全模块化的解析器**，从一个传统的爬虫框架演进为一个真正的**智能数据获取平台**。它不仅能“爬”，更能“理解”和“规划”，为获取海量高质量数据提供了强大的、面向未来的解决方案。