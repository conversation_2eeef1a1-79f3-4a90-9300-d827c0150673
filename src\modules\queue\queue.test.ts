/**
 * 队列模块测试
 */

import { RedisClientManager, TaskScheduler, QueueMonitor } from './index';
import { QueueConfig, FetchTask, TaskStatus, FetcherType } from '@/interfaces';

// Mock Redis for testing
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    quit: jest.fn().mockResolvedValue(undefined),
    ping: jest.fn().mockResolvedValue('PONG'),
    info: jest.fn().mockResolvedValue('used_memory:1000\r\nused_memory_peak:2000\r\ntotal_system_memory:8000\r\nconnected_clients:5'),
    dbsize: jest.fn().mockResolvedValue(100),
    flushdb: jest.fn().mockResolvedValue('OK'),
    set: jest.fn().mockResolvedValue('OK'),
    get: jest.fn().mockResolvedValue('test-value'),
    del: jest.fn().mockResolvedValue(1),
    exists: jest.fn().mockResolvedValue(1),
    keys: jest.fn().mockResolvedValue(['key1', 'key2']),
    expire: jest.fn().mockResolvedValue(1),
    ttl: jest.fn().mockResolvedValue(3600),
    setex: jest.fn().mockResolvedValue('OK'),
    on: jest.fn(),
  }));
});

// Mock BullMQ
jest.mock('bullmq', () => ({
  Queue: jest.fn().mockImplementation(() => ({
    add: jest.fn().mockResolvedValue({ id: 'job-1' }),
    addBulk: jest.fn().mockResolvedValue([{ id: 'job-1' }, { id: 'job-2' }]),
    getNextJob: jest.fn().mockResolvedValue({ data: { id: 'task-1' } }),
    getWaiting: jest.fn().mockResolvedValue([]),
    getActive: jest.fn().mockResolvedValue([]),
    getCompleted: jest.fn().mockResolvedValue([]),
    getFailed: jest.fn().mockResolvedValue([]),
    getDelayed: jest.fn().mockResolvedValue([]),
    isPaused: jest.fn().mockResolvedValue(false),
    pause: jest.fn().mockResolvedValue(undefined),
    resume: jest.fn().mockResolvedValue(undefined),
    obliterate: jest.fn().mockResolvedValue(undefined),
    close: jest.fn().mockResolvedValue(undefined),
  })),
  Worker: jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    close: jest.fn().mockResolvedValue(undefined),
  })),
}));

describe('Queue Module', () => {
  const mockConfig: QueueConfig = {
    redis: {
      host: 'localhost',
      port: 6379,
      db: 0,
      maxConnections: 10,
      connectTimeoutMs: 10000,
      commandTimeoutMs: 5000,
    },
    queuePrefix: 'test',
    defaultJobOptions: {
      delay: 0,
      priority: 0,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    },
    concurrency: 5,
  };

  describe('RedisClientManager', () => {
    let redisManager: RedisClientManager;

    beforeEach(() => {
      redisManager = new RedisClientManager(mockConfig.redis);
    });

    afterEach(async () => {
      if (redisManager.isClientConnected()) {
        await redisManager.disconnect();
      }
    });

    it('should connect to Redis successfully', async () => {
      await redisManager.connect();
      expect(redisManager.isClientConnected()).toBe(true);
    });

    it('should perform health check', async () => {
      await redisManager.connect();
      const healthResult = await redisManager.healthCheck();
      
      expect(healthResult.status).toBe('healthy');
      expect(healthResult.details).toBeDefined();
    });

    it('should get Redis info', async () => {
      await redisManager.connect();
      const info = await redisManager.getInfo();
      
      expect(info).toBeDefined();
      expect(info.used_memory).toBe('1000');
    });

    it('should get memory usage', async () => {
      await redisManager.connect();
      const memoryUsage = await redisManager.getMemoryUsage();
      
      expect(memoryUsage.used).toBe(1000);
      expect(memoryUsage.peak).toBe(2000);
      expect(memoryUsage.total).toBe(8000);
    });

    it('should perform basic Redis operations', async () => {
      await redisManager.connect();
      
      await redisManager.set('test-key', 'test-value');
      const value = await redisManager.get('test-key');
      expect(value).toBe('test-value');
      
      const exists = await redisManager.exists('test-key');
      expect(exists).toBe(true);
      
      const deleted = await redisManager.del('test-key');
      expect(deleted).toBe(1);
    });

    it('should handle TTL operations', async () => {
      await redisManager.connect();
      
      await redisManager.set('test-key', 'test-value', 3600);
      const ttl = await redisManager.ttl('test-key');
      expect(ttl).toBe(3600);
      
      const expired = await redisManager.expire('test-key', 1800);
      expect(expired).toBe(true);
    });
  });

  describe('TaskScheduler', () => {
    let scheduler: TaskScheduler;

    beforeEach(() => {
      scheduler = new TaskScheduler(mockConfig);
    });

    afterEach(async () => {
      if (scheduler.isSchedulerStarted()) {
        await scheduler.stop();
      }
    });

    it('should start and stop successfully', async () => {
      await scheduler.start();
      expect(scheduler.isSchedulerStarted()).toBe(true);
      
      await scheduler.stop();
      expect(scheduler.isSchedulerStarted()).toBe(false);
    });

    it('should add fetch tasks', async () => {
      await scheduler.start();
      
      const task: FetchTask = {
        id: 'task-1',
        url: {
          url: 'https://example.com',
          depth: 0,
          domain: 'example.com',
          timestamp: new Date(),
          retryCount: 0,
          priority: 1,
        },
        status: TaskStatus.PENDING,
        createdAt: new Date(),
        priority: 1,
        retryCount: 0,
        maxRetries: 3,
        fetcherType: FetcherType.HTTP,
      };
      
      await expect(scheduler.addFetchTask(task)).resolves.not.toThrow();
    });

    it('should add multiple fetch tasks', async () => {
      await scheduler.start();
      
      const tasks: FetchTask[] = [
        {
          id: 'task-1',
          url: {
            url: 'https://example.com/1',
            depth: 0,
            domain: 'example.com',
            timestamp: new Date(),
            retryCount: 0,
            priority: 1,
          },
          status: TaskStatus.PENDING,
          createdAt: new Date(),
          priority: 1,
          retryCount: 0,
          maxRetries: 3,
          fetcherType: FetcherType.HTTP,
        },
        {
          id: 'task-2',
          url: {
            url: 'https://example.com/2',
            depth: 0,
            domain: 'example.com',
            timestamp: new Date(),
            retryCount: 0,
            priority: 2,
          },
          status: TaskStatus.PENDING,
          createdAt: new Date(),
          priority: 2,
          retryCount: 0,
          maxRetries: 3,
          fetcherType: FetcherType.BROWSER,
        },
      ];
      
      await expect(scheduler.addFetchTasks(tasks)).resolves.not.toThrow();
    });

    it('should get queue statistics', async () => {
      await scheduler.start();
      
      const stats = await scheduler.getQueueStats();
      expect(Array.isArray(stats)).toBe(true);
      expect(stats.length).toBeGreaterThan(0);
      
      stats.forEach(stat => {
        expect(stat).toHaveProperty('queueName');
        expect(stat).toHaveProperty('waiting');
        expect(stat).toHaveProperty('active');
        expect(stat).toHaveProperty('completed');
        expect(stat).toHaveProperty('failed');
        expect(stat).toHaveProperty('timestamp');
      });
    });

    it('should handle queue operations', async () => {
      await scheduler.start();
      
      await expect(scheduler.pauseQueue('fetch')).resolves.not.toThrow();
      await expect(scheduler.resumeQueue('fetch')).resolves.not.toThrow();
      await expect(scheduler.clearQueue('fetch')).resolves.not.toThrow();
    });
  });

  describe('QueueMonitor', () => {
    let scheduler: TaskScheduler;
    let monitor: QueueMonitor;

    beforeEach(async () => {
      scheduler = new TaskScheduler(mockConfig);
      await scheduler.start();
      monitor = new QueueMonitor(scheduler, 1000); // 1 second interval for testing
    });

    afterEach(async () => {
      monitor.stopMonitoring();
      await scheduler.stop();
    });

    it('should start and stop monitoring', () => {
      monitor.startMonitoring();
      expect(monitor.isMonitoringActive()).toBe(true);
      
      monitor.stopMonitoring();
      expect(monitor.isMonitoringActive()).toBe(false);
    });

    it('should perform health check', async () => {
      const healthResult = await monitor.getHealthCheck();
      expect(healthResult.status).toBeDefined();
      expect(healthResult.timestamp).toBeInstanceOf(Date);
    });

    it('should generate detailed report', async () => {
      const report = await monitor.getDetailedReport();
      
      expect(report).toHaveProperty('overview');
      expect(report).toHaveProperty('queues');
      expect(report).toHaveProperty('redis');
      
      expect(report.overview).toHaveProperty('totalQueues');
      expect(report.overview).toHaveProperty('healthyQueues');
      expect(report.overview).toHaveProperty('totalJobs');
      
      expect(Array.isArray(report.queues)).toBe(true);
      expect(report.redis).toHaveProperty('connected');
    });

    it('should emit events during monitoring', (done) => {
      let eventReceived = false;
      
      monitor.on('stats-updated', (stats) => {
        expect(Array.isArray(stats)).toBe(true);
        eventReceived = true;
      });
      
      monitor.startMonitoring();
      
      // Wait for at least one monitoring cycle
      setTimeout(() => {
        monitor.stopMonitoring();
        if (eventReceived) {
          done();
        } else {
          done(new Error('No stats-updated event received'));
        }
      }, 1500);
    });

    it('should allow setting health thresholds', () => {
      const newThresholds = {
        maxMemoryUsage: 0.9,
        maxFailedRatio: 0.2,
      };
      
      monitor.setHealthThresholds(newThresholds);
      const currentThresholds = monitor.getHealthThresholds();
      
      expect(currentThresholds.maxMemoryUsage).toBe(0.9);
      expect(currentThresholds.maxFailedRatio).toBe(0.2);
    });
  });
});
