/**
 * HTML 爬虫路由
 * 
 * 提供多层级爬取、AI解析、链接收集的爬虫服务
 */

import { Router } from 'express';
import { CreateCrawlerTaskRequest, PageResult, LinkInfo } from '@/types/task';
import { getLogger, error as logError } from '@/utils';
import { TaskManager } from '@/app/task-manager';

const logger = getLogger('CrawlerRoute');

/**
 * 创建 HTML 爬虫路由
 */
export function createCrawlerRoutes(taskManager: TaskManager): Router {
  const router = Router();

  /**
   * POST /api/v1/crawler/task - 创建HTML爬虫任务
   */
  router.post('/task', async (req, res) => {
    try {
      const request: CreateCrawlerTaskRequest = req.body;

      // 验证请求参数
      if (!request.name || !request.config?.startUrls?.length) {
        return res.status(400).json({
          success: false,
          error: 'Task name and start URLs are required'
        });
      }

      // 设置默认配置
      const config = {
        ...request.config,
        maxDepth: request.config.maxDepth || 2,
        maxConcurrency: request.config.maxConcurrency || 5,
        delayMs: request.config.delayMs || 1000,
        useProxy: request.config.useProxy !== false,
        respectRobots: request.config.respectRobots !== false,
        userAgent: request.config.userAgent || 'Nodex-Crawler/1.0',
        customHeaders: request.config.customHeaders || {},
        cookies: request.config.cookies || ''
      };

      const taskId = `crawler-task-${Date.now()}`;
      
      logger.info('创建HTML爬虫任务', { 
        taskId, 
        name: request.name, 
        startUrls: (config.startUrls || []).length,
        maxDepth: config.maxDepth,
        maxConcurrency: config.maxConcurrency
      });

      // 使用TaskManager创建实际的爬虫任务
      try {
        const createTaskRequest = {
          name: request.name,
          type: 'html_crawler' as any,
          urls: config.startUrls || [],
          config: {
            name: request.name,
            urls: config.startUrls || [],
            maxDepth: config.maxDepth,
            maxConcurrency: config.maxConcurrency,
            delayMs: config.delayMs,
            enableProxy: config.useProxy,
            enableAI: config.enableAI,
            allowedDomains: config.allowedDomains,
            useReadability: config.useReadability ?? true,
            includeRawHtml: config.includeRawHtml ?? true,
            customHeaders: config.customHeaders,
            cookies: config.cookies,
            userAgent: config.userAgent
          }
        };

        const actualTaskId = await taskManager.createTask(createTaskRequest);
        
        logger.info('HTML爬虫任务创建成功', { 
          taskId: actualTaskId, 
          name: request.name 
        });

        res.json({
          success: true,
          taskId: actualTaskId,
          status: 'pending',
          message: '爬虫任务创建成功',
          data: {
            taskId: actualTaskId,
            name: request.name,
            config
          }
        });

      } catch (taskError) {
        logger.error('创建TaskManager任务失败', taskError);
        
        // 返回基本成功响应（保持向后兼容）
        res.json({
          success: false,
          taskId,
          status: 'pending',
          message: '爬虫任务创建失败',
          data: {
            taskId,
            name: request.name,
            config
          }
        });
      }

    } catch (err) {
      logError('创建HTML爬虫任务失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to create crawler task'
      });
    }
  });

  /**
   * GET /api/v1/crawler/results/:taskId - 获取爬虫抓取结果
   */
  router.get('/results/:taskId', async (req, res) => {
    try {
      const { taskId } = req.params;
      const { 
        page = 1, 
        limit = 20, 
        depth,
        status,
        search 
      } = req.query;

      logger.info('获取爬虫抓取结果', { 
        taskId, 
        page, 
        limit, 
        depth, 
        status, 
        search 
      });
      
      let results: PageResult[] = [];
      let total = 0;
      
      try {
        // 从存储中获取任务结果
        const taskResults = await taskManager.getTaskResults(taskId);
        
        // 过滤和分页处理
        let filteredResults = taskResults;
        
        // 深度过滤
        if (depth !== undefined) {
          const depthNum = Number(depth);
          filteredResults = filteredResults.filter(item => 
            item.metadata?.depth === depthNum
          );
        }
        
        // 状态过滤
        if (status) {
          filteredResults = filteredResults.filter(item => 
            item.status === status
          );
        }
        
        // 搜索过滤
        if (search) {
          const searchTerm = (search as string).toLowerCase();
          filteredResults = filteredResults.filter(item => 
            item.url.toLowerCase().includes(searchTerm) ||
            item.title.toLowerCase().includes(searchTerm) ||
            item.content.toLowerCase().includes(searchTerm)
          );
        }
        
        total = filteredResults.length;
        
        // 分页处理
        const offset = (Number(page) - 1) * Number(limit);
        const paginatedResults = filteredResults.slice(offset, offset + Number(limit));
        
        // 转换为 PageResult 格式
        results = paginatedResults.map(item => ({
          id: item.id,
          taskId: item.taskId || taskId,
          url: item.url,
          title: item.title,
          content: item.content,
          description: item.extractedData?.description || '',
          images: item.extractedData?.images || [],
          links: item.extractedData?.links || [],
          statusCode: item.metadata?.statusCode || 200,
          depth: item.metadata?.depth || 0,
          timestamp: item.timestamp,
          responseTime: item.metadata?.responseTime || 0
        }));
        
        logger.info('成功获取爬虫抓取结果', { 
          taskId, 
          total: total,
          returned: results.length,
          filters: { depth, status, search }
        });
        
      } catch (storageError) {
        logger.error('从存储获取结果失败', { 
          taskId, 
          error: storageError instanceof Error ? storageError.message : storageError 
        });
        // 返回空结果而不是错误，保持API稳定性
      }

      res.json({
        success: true,
        data: results,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        },
        filters: {
          depth: depth ? Number(depth) : undefined,
          status: status as string,
          search: search as string
        }
      });

    } catch (err) {
      logError('获取爬虫抓取结果失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get crawler results'
      });
    }
  });

  /**
   * GET /api/v1/crawler/links/:taskId - 获取发现的链接
   */
  router.get('/links/:taskId', async (req, res) => {
    try {
      const { taskId } = req.params;
      const { 
        page = 1, 
        limit = 50, 
        depth,
        processed,
        search 
      } = req.query;

      logger.info('获取发现的链接', { 
        taskId, 
        page, 
        limit, 
        depth, 
        processed 
      });

      // 从存储管理器获取链接信息
      const dependencies = taskManager.getDependencies();
      
      let links: LinkInfo[] = [];
      let total = 0;
      
      try {
        // 从存储中获取任务结果
        const taskResults = await taskManager.getTaskResults(taskId);
        
        // 提取所有链接信息
        const allLinks: LinkInfo[] = [];
        
        taskResults.forEach(item => {
          const itemDepth = item.metadata?.depth || 0;
          
          // 从extractedData中提取链接
          if (item.extractedData?.links && Array.isArray(item.extractedData.links)) {
            item.extractedData.links.forEach((linkUrl: string) => {
              allLinks.push({
                url: linkUrl,
                parentUrl: item.url,
                depth: itemDepth + 1,
                processed: false, // 默认未处理，实际应该检查是否已在任务结果中
                discovered: true,
              });
            });
          }
          
          // 添加当前URL作为已处理的链接
          allLinks.push({
            url: item.url,
            parentUrl: '', // 根URL没有父URL
            depth: itemDepth,
            processed: true,
            discovered: true,
          });
        });
        
        // 去重处理（相同URL只保留一个）
        const uniqueLinks = new Map<string, LinkInfo>();
        allLinks.forEach(link => {
          const existing = uniqueLinks.get(link.url);
          if (!existing || (link.processed && !existing.processed)) {
            uniqueLinks.set(link.url, link);
          }
        });
        
        let filteredLinks = Array.from(uniqueLinks.values());
        
        // 深度过滤
        if (depth !== undefined) {
          const depthNum = Number(depth);
          filteredLinks = filteredLinks.filter(link => link.depth === depthNum);
        }
        
        // 处理状态过滤
        if (processed !== undefined) {
          const isProcessed = processed === 'true';
          filteredLinks = filteredLinks.filter(link => link.processed === isProcessed);
        }
        
        // 搜索过滤
        if (search) {
          const searchTerm = (search as string).toLowerCase();
          filteredLinks = filteredLinks.filter(link => 
            link.url.toLowerCase().includes(searchTerm) ||
            (link.parentUrl && link.parentUrl.toLowerCase().includes(searchTerm))
          );
        }
        
        total = filteredLinks.length;
        
        // 分页处理
        const offset = (Number(page) - 1) * Number(limit);
        links = filteredLinks.slice(offset, offset + Number(limit));
        
        logger.info('成功获取发现的链接', { 
          taskId, 
          total: total,
          returned: links.length,
          filters: { depth, processed, search }
        });
        
      } catch (storageError) {
        logger.error('从存储获取链接失败', { 
          taskId, 
          error: storageError instanceof Error ? storageError.message : storageError 
        });
        // 返回空结果而不是错误，保持API稳定性
      }

      res.json({
        success: true,
        data: links,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        },
        filters: {
          depth: depth ? Number(depth) : undefined,
          processed: processed === 'true' ? true : processed === 'false' ? false : undefined,
          search: search as string
        }
      });

    } catch (err) {
      logError('获取发现的链接失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get discovered links'
      });
    }
  });

  /**
   * POST /api/v1/crawler/extract - 单页面内容提取
   */
  router.post('/extract', async (req, res) => {
    try {
      const { url, selectors, enableAI } = req.body;

      if (!url) {
        return res.status(400).json({
          success: false,
          error: 'URL is required'
        });
      }

      logger.info('执行单页面内容提取', { url, enableAI });

      // 获取依赖管理器
      const dependencies = taskManager.getDependencies();
      const fetcherManager = dependencies.fetcherManager;

      const startTime = Date.now();
      
      try {
        // 获取页面内容
        const fetchResponse = await fetcherManager.fetch(url, {
          method: 'GET' as any,
          timeout: 30000,
          useProxy: true
        });

        let extractedContent = fetchResponse.content;
        let title = 'Untitled';
        let description = '';
        let images: string[] = [];
        let links: string[] = [];

        // 基本HTML解析
        if (fetchResponse.content) {
          // 提取标题
          const titleMatch = fetchResponse.content.match(/<title[^>]*>([^<]+)<\/title>/i);
          if (titleMatch?.[1]) {
            title = titleMatch[1].trim();
          }

          // 提取描述
          const descMatch = fetchResponse.content.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
          if (descMatch?.[1]) {
            description = descMatch[1].trim();
          }

          // 提取图片
          const imgMatches = fetchResponse.content.match(/<img[^>]*src=["\']([^"']+)["\'][^>]*>/gi);
          if (imgMatches) {
            images = imgMatches.map(match => {
              const srcMatch = match.match(/src=["\']([^"']+)["\']/i);
              return srcMatch ? srcMatch[1] : '';
            }).filter((url): url is string => Boolean(url));
          }

          // 提取链接
          const linkMatches = fetchResponse.content.match(/<a[^>]*href=["\']([^"']+)["\'][^>]*>/gi);
          if (linkMatches) {
            links = linkMatches.map(match => {
              const hrefMatch = match.match(/href=["\']([^"']+)["\']/i);
              return hrefMatch ? hrefMatch[1] : '';
            }).filter((url): url is string => Boolean(url));
          }

          // 基本文本提取
          extractedContent = fetchResponse.content
            .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
            .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
            .replace(/<[^>]*>/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
        }

        // 如果启用AI，进行智能内容提取
        if (enableAI) {
          try {
            const { AIEngine } = await import('@/modules/ai/ai-engine');
            const aiEngine = new AIEngine();
            
            const aiPrompt = `请从以下HTML内容中提取主要内容、标题和摘要：

HTML内容：
${fetchResponse.content.substring(0, 8000)}

请返回JSON格式的响应：
{
  "title": "页面标题",
  "summary": "内容摘要",
  "mainContent": "主要内容"
}`;

            const aiResponse = await aiEngine.generateResponse({
              prompt: aiPrompt,
              maxTokens: 1000,
              temperature: 0.3
            });

            if (aiResponse.content) {
              try {
                const aiResult = JSON.parse(aiResponse.content);
                if (aiResult.title) title = aiResult.title;
                if (aiResult.summary) description = aiResult.summary;
                if (aiResult.mainContent) extractedContent = aiResult.mainContent;
              } catch (parseError) {
                logger.warn('AI响应解析失败', { error: parseError });
              }
            }
          } catch (aiError) {
            logger.warn('AI内容提取失败', { error: aiError });
          }
        }

        const responseTime = Date.now() - startTime;

        const result: PageResult = {
          url,
          title,
          content: extractedContent,
          links: links.map(linkUrl => ({
            url: linkUrl,
            text: '',
            depth: 1
          })),
          metadata: {
            description,
            images,
            statusCode: fetchResponse.statusCode,
            depth: 0,
            responseTime
          },
          responseTime,
          statusCode: fetchResponse.statusCode,
          timestamp: new Date().toISOString()
        };

        logger.info('单页面内容提取成功', { 
          url, 
          statusCode: fetchResponse.statusCode, 
          responseTime,
          contentLength: extractedContent.length 
        });

        res.json({
          success: true,
          data: result,
          message: '页面内容提取完成'
        });

      } catch (extractError) {
        const responseTime = Date.now() - startTime;
        logger.error('页面内容提取失败', { 
          url, 
          error: extractError instanceof Error ? extractError.message : extractError,
          responseTime 
        });

        res.status(500).json({
          success: false,
          error: extractError instanceof Error ? extractError.message : 'Content extraction failed'
        });
      }

    } catch (err) {
      logError('单页面内容提取失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to extract page content'
      });
    }
  });

  return router;
}