# Nodex v2.0 - 高性能智能爬虫系统

> AI 增强的企业级 Node.js 爬虫平台

## 🚀 项目概述

Nodex 是一个开源的、企业级的、高性能的 Node.js 爬虫平台，旨在解决通用爬虫领域的痛点，实现对任意主题的自动化、海量、高质量数据获取。

### ✨ 核心特性

- **🧠 AI 增强**: 集成 LLM，实现智能内容提取和抓取策略规划
- **⚡ 高性能**: 基于分布式队列的生产者-消费者架构
- **🔧 可扩展**: 可插拔的模块化设计，支持水平扩展
- **🛡️ 反屏蔽**: 内置多种反爬虫策略和代理管理
- **🔋 内置电池**: 集成主流搜索引擎，支持自动抓取计划
- **📊 可视化**: Web 监控面板，实时查看系统状态
- **📝 智能日志**: 多级别日志系统，支持彩色输出和环境变量控制

## 🏗️ 系统架构

```
用户/配置层/AI Planner
         ↓
    主控调度模块 (Master)
         ↓
    URL 任务队列 (BullMQ + Redis)
         ↓
下载模块 (Fetcher) ←→ 解析模块 (Parser)
         ↓                ↓
    AI 赋能模块 (AI Engine)
         ↓
数据存储与索引模块 (Storage)
```

## 🛠️ 技术栈

- **核心**: Node.js (LTS) + TypeScript
- **队列**: BullMQ + Redis
- **下载器**: Got, Axios, Puppeteer, Playwright
- **解析**: Cheerio + AI 解析器
- **数据库**: MongoDB / PostgreSQL
- **搜索**: Elasticsearch / MeiliSearch
- **AI**: OpenAI GPT / Google Gemini

## 📦 安装

```bash
# 克隆项目
git clone https://github.com/your-org/nodex.git
cd nodex

# 安装依赖
npm install

# 复制环境变量配置
cp .env.example .env

# 构建项目
npm run build
```

## 🚀 快速开始

```bash
# 开发模式
npm run dev

# 使用 CLI 工具
npm run cli -- --help

# 运行测试
npm test

# 启动监控面板
npm start
```

## 📖 使用文档

详细文档请参考 [docs](./docs) 目录：

- [快速开始](./docs/quick-start.md)
- [配置指南](./docs/configuration.md)
- [API 文档](./docs/api.md)
- [日志系统](./docs/logging.md)
- [部署指南](./docs/deployment.md)

## 🤝 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](./CONTRIBUTING.md) 了解详细信息。

## 📄 许可证

本项目采用 [MIT](./LICENSE) 许可证。

## 🔗 相关链接

- [项目主页](https://github.com/your-org/nodex)
- [问题反馈](https://github.com/your-org/nodex/issues)
- [更新日志](./CHANGELOG.md)
