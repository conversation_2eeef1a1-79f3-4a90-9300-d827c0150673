import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';
import { StatusBadge } from '@/components/ui/Badge';
import {
  Activity,
  Globe,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface CrawlerStatsData {
  totalTasks: number;
  runningTasks: number;
  completedTasks: number;
  failedTasks: number;
  totalUrls: number;
  processedUrls: number;
  averageSpeed: number; // URLs per minute
  successRate: number; // percentage
}

interface CrawlerStatsProps {
  stats?: CrawlerStatsData;
  loading?: boolean;
}

export const CrawlerStats: React.FC<CrawlerStatsProps> = ({ stats, loading = false }) => {
  // 默认统计数据
  const defaultStats: CrawlerStatsData = {
    totalTasks: 0,
    runningTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    totalUrls: 0,
    processedUrls: 0,
    averageSpeed: 0,
    successRate: 0,
  };

  const currentStats = stats || defaultStats;

  const statCards = [
    {
      title: '总任务数',
      value: currentStats.totalTasks,
      icon: <Globe className="h-5 w-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: '运行中',
      value: currentStats.runningTasks,
      icon: <Activity className="h-5 w-5" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '已完成',
      value: currentStats.completedTasks,
      icon: <CheckCircle className="h-5 w-5" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: '失败数',
      value: currentStats.failedTasks,
      icon: <AlertTriangle className="h-5 w-5" />,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  const performanceCards = [
    {
      title: 'URL 处理总数',
      value: currentStats.processedUrls.toLocaleString(),
      subtitle: `/ ${currentStats.totalUrls.toLocaleString()} 总数`,
      icon: <Globe className="h-5 w-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: '平均处理速度',
      value: `${currentStats.averageSpeed.toFixed(1)}`,
      subtitle: 'URLs/分钟',
      icon: <TrendingUp className="h-5 w-5" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '成功率',
      value: `${currentStats.successRate.toFixed(1)}%`,
      subtitle: '成功处理的 URL 比例',
      icon: <CheckCircle className="h-5 w-5" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: '系统状态',
      value: currentStats.runningTasks > 0 ? '活跃' : '空闲',
      subtitle: currentStats.runningTasks > 0 ? `${currentStats.runningTasks} 个任务运行中` : '无任务运行',
      icon: <Activity className="h-5 w-5" />,
      color: currentStats.runningTasks > 0 ? 'text-green-600' : 'text-gray-600',
      bgColor: currentStats.runningTasks > 0 ? 'bg-green-50' : 'bg-gray-50',
    },
  ];

  // if (loading) {
  //   return (
  //     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
  //       {Array.from({ length: 4 }).map((_, index) => (
  //         <Card key={index} className="animate-pulse">
  //           <CardContent className="p-6">
  //             <div className="flex items-center justify-between">
  //               <div className="space-y-2">
  //                 <div className="h-4 bg-gray-300 rounded w-16"></div>
  //                 <div className="h-8 bg-gray-300 rounded w-12"></div>
  //               </div>
  //               <div className="h-10 w-10 bg-gray-300 rounded-lg"></div>
  //             </div>
  //           </CardContent>
  //         </Card>
  //       ))}
  //     </div>
  //   );
  // }

  return (
    <div className="space-y-6">
      {/* 任务统计 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">任务统计</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((card, index) => (
            <Card key={index}>
              {loading ? <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-16"></div>
                    <div className="h-8 bg-gray-300 rounded w-12"></div>
                  </div>
                  <div className="h-10 w-10 bg-gray-300 rounded-lg"></div>
                </div>
              </CardContent> : <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{card.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{card.value}</p>
                  </div>
                  <div className={`${card.bgColor} ${card.color} p-3 rounded-lg`}>
                    {card.icon}
                  </div>
                </div>
              </CardContent>}
            </Card>
          ))}
        </div>
      </div>

      {/* 性能统计 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">性能统计</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {performanceCards.map((card, index) => (
            <Card key={index}>
              {loading ? <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-16"></div>
                    <div className="h-6 bg-gray-300 rounded w-12"></div>
                    <div className="h-4 bg-gray-300 rounded w-12"></div>
                  </div>
                  <div className="h-10 w-10 bg-gray-300 rounded-lg"></div>
                </div>
              </CardContent>:<CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{card.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                    <p className="text-xs text-gray-500 mt-1">{card.subtitle}</p>
                  </div>
                  <div className={`${card.bgColor} ${card.color} p-3 rounded-lg`}>
                    {card.icon}
                  </div>
                </div>
              </CardContent>}
            </Card>
          ))}
        </div>
      </div>

      {/* 处理进度条 */}
      {currentStats.totalUrls > 0 && (
        <Card>
          <CardHeader>
            <CardTitle icon={<TrendingUp className="h-5 w-5" />}>
              整体处理进度
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>已处理: {currentStats.processedUrls.toLocaleString()}</span>
                <span>总计: {currentStats.totalUrls.toLocaleString()}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-4">
                <div
                  className="bg-gradient-to-r from-blue-500 to-blue-600 h-4 rounded-full transition-all duration-300"
                  style={{
                    width: `${Math.min((currentStats.processedUrls / currentStats.totalUrls) * 100, 100)}%`,
                  }}
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>
                  {currentStats.totalUrls > 0
                    ? `${((currentStats.processedUrls / currentStats.totalUrls) * 100).toFixed(1)}%`
                    : '0%'}
                </span>
                <span>成功率: {currentStats.successRate.toFixed(1)}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};