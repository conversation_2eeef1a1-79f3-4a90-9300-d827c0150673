/**
 * API抓取结果显示组件
 * 
 * 显示API请求的响应结果
 */

import React from 'react';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

interface ApiResultProps {
  result: ApiResult | null;
  loading?: boolean;
  error?: string;
}

interface ApiResult {
  id: string;
  taskId: string;
  url: string;
  method: string;
  statusCode: number;
  headers: Record<string, string>;
  data: any;
  responseTime: number;
  timestamp: string;
  proxyUsed?: string;
  error?: string;
}

export const ApiResult: React.FC<ApiResultProps> = ({ result, loading = false, error }) => {
  console.log("result:", result)
  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2">请求中...</span>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6 border-red-200">
        <h3 className="text-lg font-semibold text-red-600 mb-2">请求失败</h3>
        <p className="text-red-500">{error}</p>
      </Card>
    );
  }

  if (!result) {
    return (
      <Card className="p-6 bg-gray-50">
        <div className="text-center text-gray-500 py-8">
          请配置并发起API请求，结果将在此处显示
        </div>
      </Card>
    );
  }

  const getStatusColor = (statusCode: number): "success" | "warning" | "danger" | "default" => {
    if (statusCode >= 200 && statusCode < 300) return 'success';
    if (statusCode >= 300 && statusCode < 400) return 'warning';
    if (statusCode >= 400 && statusCode < 500) return 'warning';
    return 'danger';
  };

  const formatHeaders = (headers: Record<string, string>) => {
    return Object.entries(headers)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
  };
  const isObj = (body: any) => {
    return typeof body === 'object' && body
  }
  const formatBody = (body: string) => {
    try {
      const parsed = isObj(body) ? body : JSON.parse(body);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return body;
    }
  };

  return (
    <Card className="p-6">
      <div className="space-y-4">
        {/* 请求信息头 */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">API响应结果</h3>
          <div className="flex items-center space-x-2">
            <Badge variant={getStatusColor(result.statusCode)}>
              {result.statusCode}
            </Badge>
            <span className="text-sm text-gray-500">
              {result.responseTime}ms
            </span>
          </div>
        </div>

        {/* 基本信息 */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-600">请求方法:</span>
            <span className="ml-2">{result.method}</span>
          </div>
          <div>
            <span className="font-medium text-gray-600">请求时间:</span>
            <span className="ml-2">{new Date(result.timestamp).toLocaleString()}</span>
          </div>
          {result.proxyUsed && (
            <div>
              <span className="font-medium text-gray-600">使用代理:</span>
              <span className="ml-2">{result.proxyUsed}</span>
            </div>
          )}
        </div>

        {/* URL */}
        <div>
          <span className="font-medium text-gray-600 block mb-1">请求URL:</span>
          <div className="bg-gray-100 p-2 rounded text-sm font-mono break-all">
            {result.url}
          </div>
        </div>

        {/* 响应头 */}
        <div>
          <span className="font-medium text-gray-600 block mb-2">响应头:</span>
          <div className="bg-gray-100 p-3 rounded text-sm font-mono whitespace-pre-wrap max-h-40 overflow-auto">
            {formatHeaders(result.headers)}
          </div>
        </div>

        {/* 响应体 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium text-gray-600">响应体:</span>
            <span className="text-xs text-gray-500">
              {result?.data?.length} 字符
            </span>
          </div>
          <div className="bg-gray-100 p-3 rounded text-sm font-mono whitespace-pre-wrap max-h-96 overflow-auto">
            {formatBody(result?.data)}
          </div>
        </div>

        {/* 错误信息 */}
        {result.error && (
          <div>
            <span className="font-medium text-red-600 block mb-2">错误信息:</span>
            <div className="bg-red-50 p-3 rounded text-sm text-red-700">
              {result.error}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};