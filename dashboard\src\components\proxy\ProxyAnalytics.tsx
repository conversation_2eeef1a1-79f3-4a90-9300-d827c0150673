import React, { useState, useMemo } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface ProxyAnalyticsData {
  proxy: {
    host: string;
    port: number;
    protocol?: string;
  };
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  uptime: number;
  lastUsed: Date;
  healthScore: number;
  regions: string[];
  errorTypes: Record<string, number>;
  timeSeriesData: {
    timestamp: Date;
    responseTime: number;
    success: boolean;
  }[];
}

interface ProxyAnalyticsProps {
  data: ProxyAnalyticsData[];
  loading?: boolean;
  onRefresh?: () => void;
  className?: string;
}

export const ProxyAnalytics: React.FC<ProxyAnalyticsProps> = ({
  data,
  loading = false,
  onRefresh,
  className = '',
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  const [sortBy, setSortBy] = useState<'performance' | 'reliability' | 'usage'>('performance');

  const filteredAndSortedData = useMemo(() => {
    let filtered = [...data];
    
    // 根据时间范围过滤数据
    const now = new Date();
    let cutoffTime: Date;
    
    switch (selectedTimeRange) {
      case '1h':
        cutoffTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        cutoffTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        cutoffTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        cutoffTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    filtered = filtered.filter(proxy => proxy.lastUsed >= cutoffTime);

    // 排序
    switch (sortBy) {
      case 'performance':
        filtered.sort((a, b) => a.averageResponseTime - b.averageResponseTime);
        break;
      case 'reliability':
        filtered.sort((a, b) => b.healthScore - a.healthScore);
        break;
      case 'usage':
        filtered.sort((a, b) => b.totalRequests - a.totalRequests);
        break;
    }

    return filtered;
  }, [data, selectedTimeRange, sortBy]);

  const aggregatedStats = useMemo(() => {
    if (data.length === 0) return null;

    const totalRequests = data.reduce((sum, proxy) => sum + proxy.totalRequests, 0);
    const totalSuccessful = data.reduce((sum, proxy) => sum + proxy.successfulRequests, 0);
    const totalFailed = data.reduce((sum, proxy) => sum + proxy.failedRequests, 0);
    const averageResponseTime = data.reduce((sum, proxy) => sum + proxy.averageResponseTime, 0) / data.length;
    const averageHealthScore = data.reduce((sum, proxy) => sum + proxy.healthScore, 0) / data.length;
    const averageUptime = data.reduce((sum, proxy) => sum + proxy.uptime, 0) / data.length;

    return {
      totalRequests,
      totalSuccessful,
      totalFailed,
      successRate: totalRequests > 0 ? (totalSuccessful / totalRequests) * 100 : 0,
      averageResponseTime,
      averageHealthScore,
      averageUptime,
      activeProxies: data.filter(p => p.healthScore > 70).length,
      degradedProxies: data.filter(p => p.healthScore > 30 && p.healthScore <= 70).length,
      failedProxies: data.filter(p => p.healthScore <= 30).length,
    };
  }, [data]);

  const getPerformanceColor = (responseTime: number) => {
    if (responseTime <= 200) return 'text-green-600';
    if (responseTime <= 500) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    if (score >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const formatUptime = (uptime: number) => {
    const days = Math.floor(uptime / (24 * 60 * 60 * 1000));
    const hours = Math.floor((uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
    const minutes = Math.floor((uptime % (60 * 60 * 1000)) / (60 * 1000));
    
    if (days > 0) return `${days}天 ${hours}小时`;
    if (hours > 0) return `${hours}小时 ${minutes}分钟`;
    return `${minutes}分钟`;
  };

  if (loading) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-500">加载代理分析数据中...</span>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 总体统计 */}
      {aggregatedStats && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">代理池性能总览</h3>
            {onRefresh && (
              <Button onClick={onRefresh} variant="secondary" size="sm">
                刷新数据
              </Button>
            )}
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {aggregatedStats.totalRequests.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500">总请求数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {aggregatedStats.successRate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">成功率</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(aggregatedStats.averageResponseTime)}ms
              </div>
              <div className="text-sm text-gray-500">平均响应时间</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {aggregatedStats.averageHealthScore.toFixed(0)}
              </div>
              <div className="text-sm text-gray-500">平均健康分</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">
                {aggregatedStats.activeProxies}
              </div>
              <div className="text-sm text-gray-500">活跃代理</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {formatUptime(aggregatedStats.averageUptime)}
              </div>
              <div className="text-sm text-gray-500">平均运行时间</div>
            </div>
          </div>
        </Card>
      )}

      {/* 控制面板 */}
      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex gap-2">
            <span className="text-sm text-gray-500">时间范围:</span>
            {(['1h', '24h', '7d', '30d'] as const).map((range) => (
              <Button
                key={range}
                onClick={() => setSelectedTimeRange(range)}
                variant={selectedTimeRange === range ? 'primary' : 'secondary'}
                size="sm"
              >
                {range === '1h' ? '1小时' : range === '24h' ? '24小时' : 
                 range === '7d' ? '7天' : '30天'}
              </Button>
            ))}
          </div>
          
          <div className="flex gap-2">
            <span className="text-sm text-gray-500">排序:</span>
            <Button
              onClick={() => setSortBy('performance')}
              variant={sortBy === 'performance' ? 'primary' : 'secondary'}
              size="sm"
            >
              性能
            </Button>
            <Button
              onClick={() => setSortBy('reliability')}
              variant={sortBy === 'reliability' ? 'primary' : 'secondary'}
              size="sm"
            >
              可靠性
            </Button>
            <Button
              onClick={() => setSortBy('usage')}
              variant={sortBy === 'usage' ? 'primary' : 'secondary'}
              size="sm"
            >
              使用量
            </Button>
          </div>
        </div>
      </Card>

      {/* 详细代理分析 */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">代理详细分析</h3>
        
        {filteredAndSortedData.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">所选时间范围内没有数据</p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAndSortedData.map((proxy, index) => (
              <div key={`${proxy.proxy.host}:${proxy.proxy.port}`} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="text-sm font-medium text-gray-900">
                      {proxy.proxy.host}:{proxy.proxy.port}
                    </div>
                    <Badge variant="default">{proxy.proxy.protocol || 'http'}</Badge>
                    {proxy.regions.length > 0 && (
                      <Badge variant="info">{proxy.regions[0]}</Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-gray-500">健康分:</div>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${getHealthScoreColor(proxy.healthScore)}`}></div>
                      <span className="text-sm font-medium">{proxy.healthScore}</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 text-sm">
                  <div>
                    <div className="text-gray-500">请求总数</div>
                    <div className="font-medium">{proxy.totalRequests.toLocaleString()}</div>
                  </div>
                  <div>
                    <div className="text-gray-500">成功率</div>
                    <div className="font-medium text-green-600">
                      {((proxy.successfulRequests / proxy.totalRequests) * 100).toFixed(1)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-500">平均响应时间</div>
                    <div className={`font-medium ${getPerformanceColor(proxy.averageResponseTime)}`}>
                      {Math.round(proxy.averageResponseTime)}ms
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-500">最快响应</div>
                    <div className="font-medium text-green-600">{proxy.minResponseTime}ms</div>
                  </div>
                  <div>
                    <div className="text-gray-500">最慢响应</div>
                    <div className="font-medium text-red-600">{proxy.maxResponseTime}ms</div>
                  </div>
                  <div>
                    <div className="text-gray-500">运行时间</div>
                    <div className="font-medium">{formatUptime(proxy.uptime)}</div>
                  </div>
                </div>

                {Object.keys(proxy.errorTypes).length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="text-sm text-gray-500 mb-2">错误类型分布:</div>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(proxy.errorTypes).map(([errorType, count]) => (
                        <Badge key={errorType} variant="warning">
                          {errorType}: {count}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};