import { useState } from 'react';
import { Settings, Play, Pause, RotateCcw } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useSystemControl } from '@/hooks/useMonitoring';

interface SystemControlProps {
  systemStatus?: string;
  disabled?: boolean;
}

interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmText: string;
  onConfirm: () => void;
  onCancel: () => void;
  variant?: 'warning' | 'danger';
}

function ConfirmDialog({
  isOpen,
  title,
  message,
  confirmText,
  onConfirm,
  onCancel,
  variant = 'warning',
}: ConfirmDialogProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-6">{message}</p>
        <div className="flex justify-end space-x-3">
          <Button variant="secondary" onClick={onCancel}>
            取消
          </Button>
          <Button
            variant={variant === 'danger' ? 'danger' : 'warning'}
            onClick={onConfirm}
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </div>
  );
}

export function SystemControl({ systemStatus, disabled }: SystemControlProps) {
  const {
    loading,
    error,
    pauseSystem,
    resumeSystem,
    restartSystem,
  } = useSystemControl();

  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    confirmText: string;
    action: () => void;
    variant?: 'warning' | 'danger';
  }>({
    isOpen: false,
    title: '',
    message: '',
    confirmText: '',
    action: () => {},
  });

  const showConfirmDialog = (
    title: string,
    message: string,
    confirmText: string,
    action: () => void,
    variant: 'warning' | 'danger' = 'warning'
  ) => {
    setConfirmDialog({
      isOpen: true,
      title,
      message,
      confirmText,
      action,
      variant,
    });
  };

  const hideConfirmDialog = () => {
    setConfirmDialog(prev => ({ ...prev, isOpen: false }));
  };

  const handleConfirm = () => {
    confirmDialog.action();
    hideConfirmDialog();
  };

  const handlePauseSystem = () => {
    showConfirmDialog(
      '暂停系统',
      '确定要暂停整个爬虫系统吗？这将停止所有正在进行的任务。',
      '暂停系统',
      pauseSystem,
      'warning'
    );
  };

  const handleResumeSystem = () => {
    showConfirmDialog(
      '恢复系统',
      '确定要恢复系统运行吗？这将重新启动所有暂停的任务。',
      '恢复系统',
      resumeSystem,
      'warning'
    );
  };

  const handleRestartSystem = () => {
    showConfirmDialog(
      '重启系统',
      '确定要重启整个爬虫系统吗？这将中断所有正在进行的任务并重新启动。',
      '重启系统',
      restartSystem,
      'danger'
    );
  };

  const isRunning = systemStatus === 'running';
  const isPaused = systemStatus === 'paused';
  const isStopped = systemStatus === 'stopped';

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>
            <Settings className="h-5 w-5 mr-2 text-gray-500" />
            系统控制
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          <div className="space-y-3">
            {/* 暂停/恢复按钮 */}
            {isRunning && (
              <Button
                variant="warning"
                className="w-full"
                icon={<Pause className="h-4 w-4" />}
                onClick={handlePauseSystem}
                disabled={disabled || loading}
                loading={loading}
              >
                暂停系统
              </Button>
            )}

            {isPaused && (
              <Button
                variant="success"
                className="w-full"
                icon={<Play className="h-4 w-4" />}
                onClick={handleResumeSystem}
                disabled={disabled || loading}
                loading={loading}
              >
                恢复系统
              </Button>
            )}

            {/* 重启按钮 */}
            <Button
              variant="primary"
              className="w-full"
              icon={<RotateCcw className="h-4 w-4" />}
              onClick={handleRestartSystem}
              disabled={disabled || loading}
              loading={loading}
            >
              重启系统
            </Button>

            {/* 系统状态指示 */}
            <div className="mt-4 p-3 bg-gray-50 rounded-md">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">当前状态:</span>
                <span className={`font-medium ${
                  isRunning ? 'text-green-600' :
                  isPaused ? 'text-yellow-600' :
                  isStopped ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {isRunning ? '运行中' :
                   isPaused ? '已暂停' :
                   isStopped ? '已停止' :
                   systemStatus || '未知'}
                </span>
              </div>
            </div>

            {/* 操作说明 */}
            <div className="mt-4 text-xs text-gray-500 space-y-1">
              <p>• 暂停: 停止所有任务处理，但保持连接</p>
              <p>• 恢复: 重新开始处理暂停的任务</p>
              <p>• 重启: 完全重启系统，重新初始化所有模块</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 确认对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        title={confirmDialog.title}
        message={confirmDialog.message}
        confirmText={confirmDialog.confirmText}
        onConfirm={handleConfirm}
        onCancel={hideConfirmDialog}
        variant={confirmDialog.variant}
      />
    </>
  );
}
