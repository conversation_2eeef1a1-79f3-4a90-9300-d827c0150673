import React, { useState, useMemo } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface ProxyUsageReport {
  proxy: {
    host: string;
    port: number;
    protocol?: string;
    region?: string;
  };
  timeRange: {
    start: Date;
    end: Date;
  };
  usage: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    dataTransferred: number; // in bytes
    uniqueTargets: number;
  };
  performance: {
    averageResponseTime: number;
    minResponseTime: number;
    maxResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    throughput: number; // requests per second
  };
  reliability: {
    uptime: number;
    errorRate: number;
    mtbf: number;
    mttr: number;
    consecutiveFailures: number;
  };
  cost: {
    totalCost: number;
    costPerRequest: number;
    costPerGB: number;
    costEfficiency: number;
  };
  hourlyStats: {
    hour: number;
    requests: number;
    errors: number;
    avgResponseTime: number;
  }[];
  topTargets: {
    domain: string;
    requests: number;
    successRate: number;
  }[];
  errorAnalysis: {
    errorType: string;
    count: number;
    percentage: number;
    examples: string[];
  }[];
}

interface ProxyUsageAnalyticsProps {
  reports: ProxyUsageReport[];
  loading?: boolean;
  onGenerateReport?: (timeRange: { start: Date; end: Date }) => void;
  onExportReport?: (format: 'pdf' | 'csv' | 'json') => void;
}

export const ProxyUsageAnalytics: React.FC<ProxyUsageAnalyticsProps> = ({
  reports,
  loading = false,
  onGenerateReport,
  onExportReport,
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '24h' | '7d' | '30d' | 'custom'>('24h');
  const [customDateRange, setCustomDateRange] = useState({ start: '', end: '' });
  const [selectedProxy, setSelectedProxy] = useState<ProxyUsageReport | null>(null);
  const [viewMode, setViewMode] = useState<'overview' | 'performance' | 'reliability' | 'cost'>('overview');

  const aggregatedStats = useMemo(() => {
    if (reports.length === 0) return null;

    return {
      totalRequests: reports.reduce((sum, report) => sum + report.usage.totalRequests, 0),
      totalSuccessful: reports.reduce((sum, report) => sum + report.usage.successfulRequests, 0),
      totalFailed: reports.reduce((sum, report) => sum + report.usage.failedRequests, 0),
      totalDataTransferred: reports.reduce((sum, report) => sum + report.usage.dataTransferred, 0),
      averageResponseTime: reports.reduce((sum, report) => sum + report.performance.averageResponseTime, 0) / reports.length,
      averageUptime: reports.reduce((sum, report) => sum + report.reliability.uptime, 0) / reports.length,
      totalCost: reports.reduce((sum, report) => sum + report.cost.totalCost, 0),
      averageCostPerRequest: reports.reduce((sum, report) => sum + report.cost.costPerRequest, 0) / reports.length,
      topPerformingProxies: reports
        .sort((a, b) => b.performance.throughput - a.performance.throughput)
        .slice(0, 5),
      mostReliableProxies: reports
        .sort((a, b) => b.reliability.uptime - a.reliability.uptime)
        .slice(0, 5),
      mostCostEfficient: reports
        .sort((a, b) => b.cost.costEfficiency - a.cost.costEfficiency)
        .slice(0, 5),
    };
  }, [reports]);

  const handleGenerateReport = () => {
    if (!onGenerateReport) return;

    let start: Date, end: Date;
    const now = new Date();

    switch (selectedTimeRange) {
      case '1h':
        start = new Date(now.getTime() - 60 * 60 * 1000);
        end = now;
        break;
      case '24h':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        end = now;
        break;
      case '7d':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        end = now;
        break;
      case '30d':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        end = now;
        break;
      case 'custom':
        if (!customDateRange.start || !customDateRange.end) return;
        start = new Date(customDateRange.start);
        end = new Date(customDateRange.end);
        break;
      default:
        return;
    }

    onGenerateReport({ start, end });
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount);
  };

  const getPerformanceColor = (value: number, type: 'response' | 'uptime' | 'efficiency') => {
    switch (type) {
      case 'response':
        if (value <= 200) return 'text-green-600';
        if (value <= 500) return 'text-yellow-600';
        return 'text-red-600';
      case 'uptime':
        if (value >= 99) return 'text-green-600';
        if (value >= 95) return 'text-yellow-600';
        return 'text-red-600';
      case 'efficiency':
        if (value >= 80) return 'text-green-600';
        if (value >= 60) return 'text-yellow-600';
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-500">生成使用报告中...</span>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">代理使用分析</h3>
          <div className="flex gap-2">
            {onExportReport && (
              <div className="relative">
                <select
                  onChange={(e) => e.target.value && onExportReport(e.target.value as 'pdf' | 'csv' | 'json')}
                  className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={reports.length === 0}
                >
                  <option value="">导出报告...</option>
                  <option value="pdf">PDF 报告</option>
                  <option value="csv">CSV 数据</option>
                  <option value="json">JSON 数据</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            )}
            <Button
              onClick={handleGenerateReport}
              variant="primary"
              size="sm"
              disabled={!onGenerateReport}
            >
              生成报告
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex gap-2">
            <span className="text-sm text-gray-500">时间范围:</span>
            {(['1h', '24h', '7d', '30d', 'custom'] as const).map((range) => (
              <Button
                key={range}
                onClick={() => setSelectedTimeRange(range)}
                variant={selectedTimeRange === range ? 'primary' : 'secondary'}
                size="sm"
              >
                {range === '1h' ? '1小时' : 
                 range === '24h' ? '24小时' : 
                 range === '7d' ? '7天' : 
                 range === '30d' ? '30天' : '自定义'}
              </Button>
            ))}
          </div>

          {selectedTimeRange === 'custom' && (
            <div className="flex gap-2 items-center">
              <input
                type="datetime-local"
                value={customDateRange.start}
                onChange={(e) => setCustomDateRange({...customDateRange, start: e.target.value})}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-500">至</span>
              <input
                type="datetime-local"
                value={customDateRange.end}
                onChange={(e) => setCustomDateRange({...customDateRange, end: e.target.value})}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
        </div>
      </Card>

      {/* 总体统计 */}
      {aggregatedStats && (
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">总体统计</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {aggregatedStats.totalRequests.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500">总请求数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {((aggregatedStats.totalSuccessful / aggregatedStats.totalRequests) * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">成功率</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(aggregatedStats.averageResponseTime)}ms
              </div>
              <div className="text-sm text-gray-500">平均响应时间</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {formatBytes(aggregatedStats.totalDataTransferred)}
              </div>
              <div className="text-sm text-gray-500">数据传输量</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">
                {aggregatedStats.averageUptime.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">平均运行时间</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {formatCurrency(aggregatedStats.totalCost)}
              </div>
              <div className="text-sm text-gray-500">总成本</div>
            </div>
          </div>
        </Card>
      )}

      {/* 视图模式切换 */}
      <Card className="p-6">
        <div className="flex gap-2 mb-4">
          {(['overview', 'performance', 'reliability', 'cost'] as const).map((mode) => (
            <Button
              key={mode}
              onClick={() => setViewMode(mode)}
              variant={viewMode === mode ? 'primary' : 'secondary'}
              size="sm"
            >
              {mode === 'overview' ? '概览' :
               mode === 'performance' ? '性能' :
               mode === 'reliability' ? '可靠性' : '成本'}
            </Button>
          ))}
        </div>

        {/* 代理报告列表 */}
        {reports.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">暂无使用报告数据</p>
          </div>
        ) : (
          <div className="space-y-4">
            {reports.map((report, index) => (
              <div key={`${report.proxy.host}:${report.proxy.port}`} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="text-sm font-medium text-gray-900">
                      {report.proxy.host}:{report.proxy.port}
                    </div>
                    <Badge variant="default">{report.proxy.protocol || 'http'}</Badge>
                    {report.proxy.region && (
                      <Badge variant="info">{report.proxy.region}</Badge>
                    )}
                  </div>
                  <div className="text-sm text-gray-500">
                    {report.timeRange.start.toLocaleDateString()} - {report.timeRange.end.toLocaleDateString()}
                  </div>
                </div>

                {viewMode === 'overview' && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">请求总数</div>
                      <div className="text-lg font-medium">{report.usage.totalRequests.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">成功率</div>
                      <div className="text-lg font-medium text-green-600">
                        {((report.usage.successfulRequests / report.usage.totalRequests) * 100).toFixed(1)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">平均响应时间</div>
                      <div className={`text-lg font-medium ${getPerformanceColor(report.performance.averageResponseTime, 'response')}`}>
                        {Math.round(report.performance.averageResponseTime)}ms
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">数据传输</div>
                      <div className="text-lg font-medium">{formatBytes(report.usage.dataTransferred)}</div>
                    </div>
                  </div>
                )}

                {viewMode === 'performance' && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">吞吐量</div>
                      <div className="text-lg font-medium">{report.performance.throughput.toFixed(1)} req/s</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">P95 响应时间</div>
                      <div className="text-lg font-medium">{Math.round(report.performance.p95ResponseTime)}ms</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">P99 响应时间</div>
                      <div className="text-lg font-medium">{Math.round(report.performance.p99ResponseTime)}ms</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">唯一目标</div>
                      <div className="text-lg font-medium">{report.usage.uniqueTargets}</div>
                    </div>
                  </div>
                )}

                {viewMode === 'reliability' && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">运行时间</div>
                      <div className={`text-lg font-medium ${getPerformanceColor(report.reliability.uptime, 'uptime')}`}>
                        {report.reliability.uptime.toFixed(2)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">错误率</div>
                      <div className="text-lg font-medium text-red-600">
                        {report.reliability.errorRate.toFixed(2)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">MTBF</div>
                      <div className="text-lg font-medium">{Math.round(report.reliability.mtbf)}分钟</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">MTTR</div>
                      <div className="text-lg font-medium">{Math.round(report.reliability.mttr)}分钟</div>
                    </div>
                  </div>
                )}

                {viewMode === 'cost' && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">总成本</div>
                      <div className="text-lg font-medium">{formatCurrency(report.cost.totalCost)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">每请求成本</div>
                      <div className="text-lg font-medium">{formatCurrency(report.cost.costPerRequest)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">每GB成本</div>
                      <div className="text-lg font-medium">{formatCurrency(report.cost.costPerGB)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">成本效率</div>
                      <div className={`text-lg font-medium ${getPerformanceColor(report.cost.costEfficiency, 'efficiency')}`}>
                        {report.cost.costEfficiency.toFixed(1)}%
                      </div>
                    </div>
                  </div>
                )}

                <div className="mt-4 pt-4 border-t border-gray-100">
                  <Button
                    onClick={() => setSelectedProxy(selectedProxy?.proxy.host === report.proxy.host ? null : report)}
                    variant="secondary"
                    size="sm"
                  >
                    {selectedProxy?.proxy.host === report.proxy.host ? '隐藏详情' : '查看详情'}
                  </Button>
                </div>

                {selectedProxy?.proxy.host === report.proxy.host && (
                  <div className="mt-4 pt-4 border-t border-gray-100 space-y-4">
                    {/* 小时统计 */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">24小时使用分布</h4>
                      <div className="flex items-end gap-1 h-16">
                        {report.hourlyStats.map((stat, index) => (
                          <div key={index} className="flex-1 bg-blue-200 rounded-t" style={{
                            height: `${(stat.requests / Math.max(...report.hourlyStats.map(s => s.requests))) * 100}%`
                          }}>
                            <div className="text-xs text-center pt-1">{stat.hour}</div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 热门目标 */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">热门目标域名</h4>
                      <div className="space-y-2">
                        {report.topTargets.slice(0, 5).map((target, index) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <span className="font-medium">{target.domain}</span>
                            <div className="flex items-center gap-2">
                              <span>{target.requests.toLocaleString()} 请求</span>
                              <Badge variant={target.successRate > 95 ? 'success' : target.successRate > 90 ? 'warning' : 'danger'}>
                                {target.successRate.toFixed(1)}%
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 错误分析 */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">错误分析</h4>
                      <div className="space-y-2">
                        {report.errorAnalysis.slice(0, 3).map((error, index) => (
                          <div key={index} className="bg-red-50 p-3 rounded-md">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm font-medium text-red-800">{error.errorType}</span>
                              <Badge variant="danger">{error.count} 次 ({error.percentage.toFixed(1)}%)</Badge>
                            </div>
                            <div className="text-xs text-red-700">
                              示例: {error.examples.slice(0, 2).join(', ')}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};