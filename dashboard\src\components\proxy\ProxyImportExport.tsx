import React, { useState, useRef } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface ProxyConfig {
  host: string;
  port: number;
  auth?: {
    username: string;
    password: string;
  };
  protocol?: 'http' | 'https' | 'socks4' | 'socks5';
}

interface ProxyStatus {
  proxy: ProxyConfig;
  status: 'active' | 'failed' | 'recovering';
  successCount: number;
  failureCount: number;
  lastUsed?: Date;
  lastChecked?: Date;
  responseTime?: number;
  healthScore?: number;
}

interface ProxyImportExportProps {
  proxies: ProxyStatus[];
  onImport: (proxies: ProxyConfig[]) => Promise<void>;
  onExport: (format: 'json' | 'csv' | 'txt') => void;
  loading?: boolean;
}

interface ImportValidationResult {
  valid: ProxyConfig[];
  invalid: Array<{ line: number; data: any; error: string }>;
  duplicates: ProxyConfig[];
}

export const ProxyImportExport: React.FC<ProxyImportExportProps> = ({
  proxies,
  onImport,
  onExport,
  loading = false,
}) => {
  const [showImportModal, setShowImportModal] = useState(false);
  const [importData, setImportData] = useState('');
  const [importFormat, setImportFormat] = useState<'json' | 'csv' | 'txt'>('json');
  const [validationResult, setValidationResult] = useState<ImportValidationResult | null>(null);
  const [importing, setImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateImportData = (data: string, format: 'json' | 'csv' | 'txt'): ImportValidationResult => {
    const result: ImportValidationResult = {
      valid: [],
      invalid: [],
      duplicates: [],
    };

    const existingProxies = new Set(proxies.map(p => `${p.proxy.host}:${p.proxy.port}`));

    try {
      let parsedData: any[];

      switch (format) {
        case 'json':
          parsedData = JSON.parse(data);
          if (!Array.isArray(parsedData)) {
            throw new Error('JSON 数据必须是数组格式');
          }
          break;

        case 'csv':
          const lines = data.trim().split('\n');
          parsedData = lines.map((line, index) => {
            const [host, port, username, password, protocol] = line.split(',').map(s => s.trim());
            return {
              host,
              port: parseInt(port),
              auth: username && password ? { username, password } : undefined,
              protocol: protocol || 'http',
              _lineNumber: index + 1,
            };
          });
          break;

        case 'txt':
          const txtLines = data.trim().split('\n');
          parsedData = txtLines.map((line, index) => {
            // 支持多种格式: host:port, host:port:username:password, host:port@username:password
            const atIndex = line.indexOf('@');
            let host, port, username, password;

            if (atIndex > -1) {
              // host:port@username:password 格式
              const [hostPort, auth] = line.split('@');
              const [h, p] = hostPort.split(':');
              const [u, pw] = auth.split(':');
              host = h;
              port = parseInt(p);
              username = u;
              password = pw;
            } else {
              // host:port 或 host:port:username:password 格式
              const parts = line.split(':');
              host = parts[0];
              port = parseInt(parts[1]);
              username = parts[2];
              password = parts[3];
            }

            return {
              host,
              port,
              auth: username && password ? { username, password } : undefined,
              protocol: 'http',
              _lineNumber: index + 1,
            };
          });
          break;

        default:
          throw new Error('不支持的格式');
      }

      // 验证每个代理
      parsedData.forEach((item, index) => {
        const lineNumber = item._lineNumber || index + 1;
        delete item._lineNumber;

        // 基本验证
        if (!item.host || !item.port) {
          result.invalid.push({
            line: lineNumber,
            data: item,
            error: '缺少必需的主机地址或端口',
          });
          return;
        }

        if (isNaN(item.port) || item.port < 1 || item.port > 65535) {
          result.invalid.push({
            line: lineNumber,
            data: item,
            error: '端口号必须是1-65535之间的数字',
          });
          return;
        }

        const proxyKey = `${item.host}:${item.port}`;
        
        // 检查是否已存在
        if (existingProxies.has(proxyKey)) {
          result.duplicates.push(item);
          return;
        }

        // 检查是否在当前导入中重复
        if (result.valid.some(p => `${p.host}:${p.port}` === proxyKey)) {
          result.invalid.push({
            line: lineNumber,
            data: item,
            error: '在导入数据中重复',
          });
          return;
        }

        result.valid.push(item);
      });
    } catch (error) {
      result.invalid.push({
        line: 1,
        data: null,
        error: error instanceof Error ? error.message : '解析数据时出错',
      });
    }

    return result;
  };

  const handleImportDataChange = (data: string) => {
    setImportData(data);
    if (data.trim()) {
      const validation = validateImportData(data, importFormat);
      setValidationResult(validation);
    } else {
      setValidationResult(null);
    }
  };

  const handleFormatChange = (format: 'json' | 'csv' | 'txt') => {
    setImportFormat(format);
    if (importData.trim()) {
      const validation = validateImportData(importData, format);
      setValidationResult(validation);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setImportData(content);
        
        // 根据文件扩展名自动检测格式
        const extension = file.name.split('.').pop()?.toLowerCase();
        let detectedFormat: 'json' | 'csv' | 'txt' = 'txt';
        
        if (extension === 'json') detectedFormat = 'json';
        else if (extension === 'csv') detectedFormat = 'csv';
        
        setImportFormat(detectedFormat);
        const validation = validateImportData(content, detectedFormat);
        setValidationResult(validation);
      };
      reader.readAsText(file);
    }
  };

  const handleImport = async () => {
    if (!validationResult || validationResult.valid.length === 0) return;

    setImporting(true);
    try {
      await onImport(validationResult.valid);
      setShowImportModal(false);
      setImportData('');
      setValidationResult(null);
    } catch (error) {
      console.error('Import failed:', error);
    } finally {
      setImporting(false);
    }
  };

  const generateExportData = (format: 'json' | 'csv' | 'txt') => {
    const proxyData = proxies.map(p => ({
      host: p.proxy.host,
      port: p.proxy.port,
      username: p.proxy.auth?.username || '',
      password: p.proxy.auth?.password || '',
      protocol: p.proxy.protocol || 'http',
      status: p.status,
      successCount: p.successCount,
      failureCount: p.failureCount,
      healthScore: p.healthScore || 0,
      responseTime: p.responseTime || 0,
    }));

    switch (format) {
      case 'json':
        return JSON.stringify(proxyData, null, 2);
      
      case 'csv':
        const csvHeader = 'host,port,username,password,protocol,status,successCount,failureCount,healthScore,responseTime';
        const csvRows = proxyData.map(p => 
          `${p.host},${p.port},${p.username},${p.password},${p.protocol},${p.status},${p.successCount},${p.failureCount},${p.healthScore},${p.responseTime}`
        );
        return [csvHeader, ...csvRows].join('\n');
      
      case 'txt':
        return proxyData.map(p => {
          const auth = p.username && p.password ? `@${p.username}:${p.password}` : '';
          return `${p.host}:${p.port}${auth}`;
        }).join('\n');
    }
  };

  const handleExport = (format: 'json' | 'csv' | 'txt') => {
    const data = generateExportData(format);
    const blob = new Blob([data], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `proxies.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    onExport(format);
  };

  const getFormatExample = (format: 'json' | 'csv' | 'txt') => {
    switch (format) {
      case 'json':
        return `[
  {
    "host": "proxy1.example.com",
    "port": 8080,
    "auth": {
      "username": "user1",
      "password": "pass1"
    },
    "protocol": "http"
  }
]`;
      
      case 'csv':
        return `proxy1.example.com,8080,user1,pass1,http
proxy2.example.com,3128,user2,pass2,https`;
      
      case 'txt':
        return `proxy1.example.com:8080@user1:pass1
proxy2.example.com:3128
proxy3.example.com:8080:user3:pass3`;
    }
  };

  return (
    <div className="space-y-4">
      {/* 导入/导出控制面板 */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">代理导入/导出</h3>
          <div className="flex gap-2">
            <Button
              onClick={() => setShowImportModal(true)}
              variant="primary"
              size="sm"
              disabled={loading}
            >
              导入代理
            </Button>
            
            <div className="relative">
              <select
                onChange={(e) => handleExport(e.target.value as 'json' | 'csv' | 'txt')}
                className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading || proxies.length === 0}
              >
                <option value="">导出为...</option>
                <option value="json">JSON 格式</option>
                <option value="csv">CSV 格式</option>
                <option value="txt">TXT 格式</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <div className="text-sm text-gray-600">
          <p>• 支持 JSON、CSV 和 TXT 格式的代理导入导出</p>
          <p>• 当前共有 {proxies.length} 个代理</p>
          <p>• 导入时会自动检测重复和无效的代理</p>
        </div>
      </Card>

      {/* 导入模态框 */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">导入代理</h3>
                <Button
                  onClick={() => setShowImportModal(false)}
                  variant="secondary"
                  size="sm"
                >
                  关闭
                </Button>
              </div>

              {/* 格式选择 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  导入格式
                </label>
                <div className="flex gap-2">
                  {(['json', 'csv', 'txt'] as const).map((format) => (
                    <Button
                      key={format}
                      onClick={() => handleFormatChange(format)}
                      variant={importFormat === format ? 'primary' : 'secondary'}
                      size="sm"
                    >
                      {format.toUpperCase()}
                    </Button>
                  ))}
                </div>
              </div>

              {/* 文件上传 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  从文件导入
                </label>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".json,.csv,.txt"
                  onChange={handleFileUpload}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
              </div>

              {/* 文本输入 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  或直接输入代理数据
                </label>
                <textarea
                  value={importData}
                  onChange={(e) => handleImportDataChange(e.target.value)}
                  className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={`${importFormat.toUpperCase()} 格式示例:\n\n${getFormatExample(importFormat)}`}
                />
              </div>

              {/* 验证结果 */}
              {validationResult && (
                <div className="mb-4 space-y-2">
                  <div className="flex gap-4">
                    <Badge variant="success">{validationResult.valid.length} 个有效</Badge>
                    <Badge variant="danger">{validationResult.invalid.length} 个无效</Badge>
                    <Badge variant="warning">{validationResult.duplicates.length} 个重复</Badge>
                  </div>

                  {validationResult.invalid.length > 0 && (
                    <div className="bg-red-50 p-3 rounded-md">
                      <h4 className="text-sm font-medium text-red-800 mb-2">无效数据:</h4>
                      <div className="text-sm text-red-700 space-y-1">
                        {validationResult.invalid.slice(0, 5).map((item, index) => (
                          <div key={index}>
                            第 {item.line} 行: {item.error}
                          </div>
                        ))}
                        {validationResult.invalid.length > 5 && (
                          <div>...还有 {validationResult.invalid.length - 5} 个错误</div>
                        )}
                      </div>
                    </div>
                  )}

                  {validationResult.duplicates.length > 0 && (
                    <div className="bg-yellow-50 p-3 rounded-md">
                      <h4 className="text-sm font-medium text-yellow-800 mb-2">重复代理:</h4>
                      <div className="text-sm text-yellow-700">
                        {validationResult.duplicates.slice(0, 3).map((proxy, index) => (
                          <div key={index}>{proxy.host}:{proxy.port}</div>
                        ))}
                        {validationResult.duplicates.length > 3 && (
                          <div>...还有 {validationResult.duplicates.length - 3} 个重复</div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex justify-end gap-2">
                <Button
                  onClick={() => setShowImportModal(false)}
                  variant="secondary"
                >
                  取消
                </Button>
                <Button
                  onClick={handleImport}
                  variant="primary"
                  disabled={!validationResult || validationResult.valid.length === 0 || importing}
                >
                  {importing ? '导入中...' : `导入 ${validationResult?.valid.length || 0} 个代理`}
                </Button>
              </div>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};