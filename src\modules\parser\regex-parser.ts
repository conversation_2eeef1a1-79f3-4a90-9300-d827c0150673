/**
 * 正则表达式解析器实现
 */

import {
  IParser,
  ParserInput,
  ParserOutput,
  RegexParserOptions,
} from '@/interfaces';

/**
 * 正则表达式解析器实现类
 */
export class RegexParser implements IParser {
  public readonly name = 'regex-parser';
  public readonly version = '1.0.0';
  public readonly description = 'Text parser using regular expressions';

  private options: RegexParserOptions;

  constructor(options: RegexParserOptions) {
    this.options = {
      extractLinks: true,
      linkPattern: /<a[^>]+href\s*=\s*["']([^"']+)["'][^>]*>/gi,
      multiline: true,
      global: true,
      ...options,
    };
  }

  /**
   * 判断此解析器是否适用于该页面
   */
  public canParse(input: ParserInput): boolean {
    // 正则解析器可以处理任何文本内容
    return true;
  }

  /**
   * 执行解析
   */
  public async parse(input: ParserInput): Promise<ParserOutput> {
    const startTime = Date.now();
    
    try {
      // 提取数据
      const extractedData = this.extractData(input.content);
      
      // 提取链接
      const newUrls = this.options.extractLinks ? this.extractLinks(input.content, input.url) : [];
      
      const parseTime = Date.now() - startTime;
      
      return {
        extractedData,
        newUrls,
        metadata: {
          parserName: this.name,
          parseTime,
          extractedFieldsCount: extractedData ? Object.keys(extractedData).length : 0,
          discoveredUrlsCount: newUrls.length,
        },
      };
      
    } catch (error) {
      return {
        extractedData: null,
        newUrls: [],
        error: {
          message: error instanceof Error ? error.message : 'Unknown parsing error',
          code: 'REGEX_PARSE_ERROR',
          details: error,
        },
      };
    }
  }

  /**
   * 提取数据
   */
  private extractData(content: string): Record<string, any> | null {
    const data: Record<string, any> = {};
    
    for (const [field, pattern] of Object.entries(this.options.patterns)) {
      try {
        const matches = this.executeRegex(pattern, content);
        
        if (matches.length === 0) {
          data[field] = null;
        } else if (matches.length === 1) {
          data[field] = matches[0] ? this.processMatch(matches[0]) : null;
        } else {
          data[field] = matches.map(match => this.processMatch(match));
        }
      } catch (error) {
        console.warn(`Failed to extract field '${field}' with pattern '${pattern}':`, error);
        data[field] = null;
      }
    }

    return Object.keys(data).length > 0 ? data : null;
  }

  /**
   * 执行正则表达式匹配
   */
  private executeRegex(pattern: RegExp, content: string): RegExpMatchArray[] {
    const matches: RegExpMatchArray[] = [];
    
    if (this.options.global && pattern.global) {
      // 全局匹配
      let match;
      while ((match = pattern.exec(content)) !== null) {
        matches.push(match);
        
        // 防止无限循环
        if (!pattern.global) break;
      }
    } else {
      // 单次匹配
      const match = content.match(pattern);
      if (match) {
        matches.push(match);
      }
    }
    
    return matches;
  }

  /**
   * 处理匹配结果
   */
  private processMatch(match: RegExpMatchArray): any {
    // 如果只有一个捕获组，返回捕获的内容
    if (match.length === 2) {
      return match[1];
    }
    
    // 如果有多个捕获组，返回对象
    if (match.length > 2) {
      const result: Record<string, string> = {};
      for (let i = 1; i < match.length; i++) {
        result[`group${i}`] = match[i] || '';
      }
      return result;
    }
    
    // 如果没有捕获组，返回完整匹配
    return match[0];
  }

  /**
   * 提取链接
   */
  private extractLinks(content: string, baseUrl: string): string[] {
    if (!this.options.linkPattern) {
      return [];
    }

    const links: string[] = [];
    const baseUrlObj = new URL(baseUrl);
    
    try {
      const matches = this.executeRegex(this.options.linkPattern, content);
      
      for (const match of matches) {
        try {
          // 假设第一个捕获组是URL
          const href = match[1];
          if (href) {
            // 解析相对URL为绝对URL
            const absoluteUrl = new URL(href, baseUrlObj).toString();
            
            if (this.isValidLink(absoluteUrl)) {
              links.push(absoluteUrl);
            }
          }
        } catch (error) {
          // 忽略无效的URL
        }
      }
    } catch (error) {
      console.warn('Failed to extract links:', error);
    }

    // 去重
    return [...new Set(links)];
  }

  /**
   * 验证链接是否有效
   */
  private isValidLink(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      
      // 只接受 HTTP 和 HTTPS 协议
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return false;
      }
      
      // 过滤掉一些常见的无用链接
      const pathname = parsedUrl.pathname.toLowerCase();
      const excludePatterns = [
        /\.(css|js|ico|png|jpg|jpeg|gif|svg|pdf|zip|rar|exe)$/i,
        /^mailto:/i,
        /^tel:/i,
        /^javascript:/i,
      ];
      
      for (const pattern of excludePatterns) {
        if (pattern.test(url) || pattern.test(pathname)) {
          return false;
        }
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 添加模式
   */
  public addPattern(field: string, pattern: RegExp): void {
    this.options.patterns[field] = pattern;
  }

  /**
   * 移除模式
   */
  public removePattern(field: string): void {
    delete this.options.patterns[field];
  }

  /**
   * 更新模式
   */
  public updatePatterns(patterns: Record<string, RegExp>): void {
    this.options.patterns = { ...this.options.patterns, ...patterns };
  }

  /**
   * 设置链接提取模式
   */
  public setLinkPattern(pattern: RegExp): void {
    this.options.linkPattern = pattern;
  }

  /**
   * 获取当前配置
   */
  public getOptions(): RegexParserOptions {
    return { ...this.options };
  }

  /**
   * 设置配置
   */
  public setOptions(options: Partial<RegexParserOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * 测试模式
   */
  public testPattern(field: string, content: string): any {
    const pattern = this.options.patterns[field];
    if (!pattern) {
      throw new Error(`Pattern for field '${field}' not found`);
    }

    const matches = this.executeRegex(pattern, content);
    return matches.map(match => this.processMatch(match));
  }

  /**
   * 创建常用模式
   */
  public static createCommonPatterns(): Record<string, RegExp> {
    return {
      // 邮箱地址
      email: /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g,
      
      // 电话号码
      phone: /(\+?[\d\s\-\(\)]{10,})/g,
      
      // URL
      url: /(https?:\/\/[^\s<>"']+)/g,
      
      // 价格
      price: /[\$¥€£]?\s*(\d+(?:[.,]\d{2})?)\s*[\$¥€£]?/g,
      
      // 日期 (YYYY-MM-DD)
      date: /(\d{4}-\d{2}-\d{2})/g,
      
      // 时间 (HH:MM)
      time: /(\d{1,2}:\d{2}(?::\d{2})?)/g,
      
      // IP地址
      ip: /(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/g,
      
      // 标题 (HTML h1-h6)
      title: /<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi,
      
      // 段落内容
      paragraph: /<p[^>]*>(.*?)<\/p>/gi,
      
      // 图片链接
      image: /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi,
      
      // 链接文本
      linkText: /<a[^>]*>(.*?)<\/a>/gi,
    };
  }

  /**
   * 应用常用模式
   */
  public applyCommonPatterns(): void {
    const commonPatterns = RegexParser.createCommonPatterns();
    this.updatePatterns(commonPatterns);
  }

  /**
   * 创建自定义HTML标签模式
   */
  public static createHtmlTagPattern(tagName: string, attribute?: string): RegExp {
    if (attribute) {
      return new RegExp(`<${tagName}[^>]+${attribute}\\s*=\\s*["']([^"']+)["'][^>]*>`, 'gi');
    } else {
      return new RegExp(`<${tagName}[^>]*>(.*?)<\\/${tagName}>`, 'gi');
    }
  }

  /**
   * 创建自定义属性模式
   */
  public static createAttributePattern(attribute: string): RegExp {
    return new RegExp(`${attribute}\\s*=\\s*["']([^"']+)["']`, 'gi');
  }
}
