import React, { useState, useMemo, useCallback } from 'react';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';
import { ProxyConfig } from '../../../src/shared-types';

interface ProxyStatus {
  proxy: ProxyConfig;
  status: 'active' | 'failed' | 'recovering';
  successCount: number;
  failureCount: number;
  lastUsed?: Date;
  lastChecked?: Date;
  responseTime?: number;
  healthScore?: number;
  weight?: number;
  priority?: number;
  consecutiveFailures?: number;
  lastError?: {
    message: string;
    code?: string;
  };
}

interface ProxyListProps {
  proxies: ProxyStatus[];
  loading: boolean;
  selectedProxies: string[];
  onSelectionChange: (selected: string[]) => void;
  onRemoveProxy: (proxy: ProxyConfig) => void;
  onUpdateProxy: (proxy: ProxyConfig) => void;
}

export const ProxyList = React.forwardRef<{ resetSorting: () => void }, ProxyListProps>(({
  proxies,
  loading,
  selectedProxies,
  onSelectionChange,
  onRemoveProxy,
  onUpdateProxy,
}, ref) => {
  const [sortBy, setSortBy] = useState<'host' | 'status' | 'responseTime' | 'successRate'>('host');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [proxyToDelete, setProxyToDelete] = useState<ProxyConfig | null>(null);

  const resetSorting = useCallback(() => {
    setSortBy('host');
    setSortOrder('asc');
  }, [sortBy, sortOrder]);

  // 暴露resetSorting方法给父组件
  React.useImperativeHandle(ref, () => ({
    resetSorting
  }), [resetSorting]);

  const getProxyKey = (proxy: ProxyConfig): string => {
    return `${proxy.host}:${proxy.port}`;
  };

  const sortedProxies = useMemo(() => {
    return [...proxies].sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'host':
          aValue = a.proxy.host;
          bValue = b.proxy.host;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'responseTime':
          aValue = a.responseTime || 0;
          bValue = b.responseTime || 0;
          break;
        case 'successRate':
          aValue = a.successCount / (a.successCount + a.failureCount) || 0;
          bValue = b.successCount / (b.successCount + b.failureCount) || 0;
          break;
        default:
          aValue = a.proxy.host;
          bValue = b.proxy.host;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [proxies, sortBy, sortOrder]);

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleSelectAll = () => {
    if (selectedProxies.length === proxies.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(proxies.map(p => getProxyKey(p.proxy)));
    }
  };

  const handleSelectProxy = (proxyKey: string) => {
    if (selectedProxies.includes(proxyKey)) {
      onSelectionChange(selectedProxies.filter(key => key !== proxyKey));
    } else {
      onSelectionChange([...selectedProxies, proxyKey]);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success">活跃</Badge>;
      case 'failed':
        return <Badge variant="danger">失败</Badge>;
      case 'recovering':
        return <Badge variant="warning">恢复中</Badge>;
      default:
        return <Badge variant="default">未知</Badge>;
    }
  };

  const formatResponseTime = (responseTime?: number) => {
    if (!responseTime) return '-';
    return `${responseTime}ms`;
  };

  const formatSuccessRate = (successCount: number, failureCount: number) => {
    const total = successCount + failureCount;
    if (total === 0) return '-';
    return `${((successCount / total) * 100).toFixed(1)}%`;
  };

  const getLocationInfo = (proxy: ProxyConfig) => {
    if (proxy.countryCode && proxy.cityName) {
      return `${proxy.countryCode} - ${proxy.cityName}`;
    } else if (proxy.countryCode) {
      return proxy.countryCode;
    } else if (proxy.cityName) {
      return proxy.cityName;
    }
    return '-';
  };

  const handleDeleteProxy = (proxy: ProxyConfig) => {
    setProxyToDelete(proxy);
  };

  const confirmDeleteProxy = () => {
    if (proxyToDelete) {
      onRemoveProxy(proxyToDelete);
      setProxyToDelete(null);
    }
  };

  const cancelDeleteProxy = () => {
    setProxyToDelete(null);
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
        </div>
      </Card>
    );
  }

  if (proxies.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无代理</h3>
          <p className="text-gray-500">点击上方的"添加代理"或"从文件导入"来添加代理</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">代理列表</h3>
          <div className="text-sm text-gray-500">
            共 {proxies.length} 个代理
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedProxies.length === proxies.length && proxies.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('host')}
              >
                <div className="flex items-center space-x-1">
                  <span>代理地址</span>
                  {sortBy === 'host' && (
                    <span className="text-blue-500">
                      {sortOrder === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                协议
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                地域
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center space-x-1">
                  <span>状态</span>
                  {sortBy === 'status' && (
                    <span className="text-blue-500">
                      {sortOrder === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('responseTime')}
              >
                <div className="flex items-center space-x-1">
                  <span>响应时间</span>
                  {sortBy === 'responseTime' && (
                    <span className="text-blue-500">
                      {sortOrder === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('successRate')}
              >
                <div className="flex items-center space-x-1">
                  <span>成功率</span>
                  {sortBy === 'successRate' && (
                    <span className="text-blue-500">
                      {sortOrder === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedProxies.map((proxyStatus) => {
              const proxyKey = getProxyKey(proxyStatus.proxy);
              const isSelected = selectedProxies.includes(proxyKey);

              return (
                <tr key={proxyKey} className={`hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''}`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => handleSelectProxy(proxyKey)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {proxyStatus.proxy.host}:{proxyStatus.proxy.port}
                    </div>
                    {proxyStatus.proxy.auth && (
                      <div className="text-xs text-gray-500">
                        用户: {proxyStatus.proxy.auth.username}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge variant="default">
                      {(proxyStatus.proxy.protocol || proxyStatus.proxy.type || 'http').toUpperCase()}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {getLocationInfo(proxyStatus.proxy)}
                    {proxyStatus.proxy.asnName && (
                      <div className="text-xs text-gray-500">
                        {proxyStatus.proxy.asnName}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(proxyStatus.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatResponseTime(proxyStatus.responseTime)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatSuccessRate(proxyStatus.successCount, proxyStatus.failureCount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onUpdateProxy(proxyStatus.proxy)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      >
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        编辑
                      </button>
                      <button
                        onClick={() => handleDeleteProxy(proxyStatus.proxy)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
                      >
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        isOpen={!!proxyToDelete}
        title="确认删除代理"
        message={
          proxyToDelete
            ? `确定要删除代理 ${proxyToDelete.host}:${proxyToDelete.port} 吗？此操作不可撤销。`
            : ''
        }
        confirmText="删除"
        cancelText="取消"
        onConfirm={confirmDeleteProxy}
        onCancel={cancelDeleteProxy}
        variant="danger"
      />
    </Card>
  );
});