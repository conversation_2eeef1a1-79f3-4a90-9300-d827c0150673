import type {
  SystemStats,
  QueueStats,
  ModuleStatus,
  CrawlSession,
  ValidationResult,
} from '@/types/monitoring';

/**
 * 数据验证器类
 */
export class DataValidator {
  /**
   * 验证系统状态数据
   */
  static validateSystemStats(data: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!data || typeof data !== 'object') {
      errors.push('SystemStats data is not an object');
      return { isValid: false, errors, warnings };
    }

    // 检查必需字段
    const requiredFields = ['status', 'uptime', 'startTime', 'memory', 'sessions', 'urls'];
    for (const field of requiredFields) {
      if (!(field in data)) {
        errors.push(`Missing required field: ${field}`);
      }
    }

    // 检查嵌套对象
    if (data.memory && typeof data.memory !== 'object') {
      errors.push('memory field must be an object');
    }

    if (data.sessions && typeof data.sessions !== 'object') {
      errors.push('sessions field must be an object');
    }

    if (data.urls && typeof data.urls !== 'object') {
      errors.push('urls field must be an object');
    }

    // 检查数据类型
    if (data.uptime !== undefined && typeof data.uptime !== 'number') {
      warnings.push('uptime should be a number');
    }

    if (data.startTime !== undefined && typeof data.startTime !== 'string') {
      warnings.push('startTime should be a string');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 验证队列状态数据
   */
  static validateQueueStats(data: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!Array.isArray(data)) {
      errors.push('QueueStats data must be an array');
      return { isValid: false, errors, warnings };
    }

    data.forEach((queue, index) => {
      if (!queue || typeof queue !== 'object') {
        errors.push(`Queue at index ${index} is not an object`);
        return;
      }

      const requiredFields = ['queueName', 'waiting', 'active', 'completed', 'failed', 'delayed', 'paused', 'timestamp'];
      for (const field of requiredFields) {
        if (!(field in queue)) {
          errors.push(`Queue at index ${index} missing field: ${field}`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 验证模块状态数据
   */
  static validateModuleStatus(data: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!Array.isArray(data)) {
      errors.push('ModuleStatus data must be an array');
      return { isValid: false, errors, warnings };
    }

    data.forEach((module, index) => {
      if (!module || typeof module !== 'object') {
        errors.push(`Module at index ${index} is not an object`);
        return;
      }

      const requiredFields = ['name', 'status', 'lastChecked'];
      for (const field of requiredFields) {
        if (!(field in module)) {
          errors.push(`Module at index ${index} missing field: ${field}`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}

/**
 * 默认值提供器类
 */
export class DataDefaults {
  /**
   * 获取默认系统状态
   */
  static getSystemStats(): SystemStats {
    return {
      status: 'stopped',
      uptime: 0,
      startTime: new Date().toISOString(),
      memory: {
        heapUsed: 0,
        heapTotal: 0,
        external: 0,
        rss: 0,
      },
      cpu: {
        usage: 0,
        user: 0,
        system: 0,
      },
      sessions: {
        total: 0,
        active: 0,
        completed: 0,
        failed: 0,
      },
      urls: {
        total: 0,
        processed: 0,
        failed: 0,
        successRate: 0,
      },
    };
  }

  /**
   * 获取默认队列状态
   */
  static getQueueStats(): QueueStats[] {
    return [
      {
        queueName: 'main',
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        paused: false,
        timestamp: new Date(),
        totalJobs: 0,
        successRate: 0,
        throughput: 0,
        health: 'healthy',
      },
    ];
  }

  /**
   * 获取默认模块状态
   */
  static getModuleStatus(): ModuleStatus[] {
    return [
      {
        name: 'crawler',
        status: 'stopped',
        healthy: false,
        initialized: false,
        lastChecked: new Date(),
        lastUpdate: new Date().toISOString(),
        uptime: 0,
      },
      {
        name: 'storage',
        status: 'stopped',
        healthy: false,
        initialized: false,
        lastChecked: new Date(),
        lastUpdate: new Date().toISOString(),
        uptime: 0,
      },
      {
        name: 'ai',
        status: 'stopped',
        healthy: false,
        initialized: false,
        lastChecked: new Date(),
        lastUpdate: new Date().toISOString(),
        uptime: 0,
      },
    ];
  }

  /**
   * 获取默认会话列表
   */
  static getSessions(): CrawlSession[] {
    return [];
  }
}

/**
 * 数据清理器类
 */
export class DataSanitizer {
  /**
   * 清理和修复系统状态数据
   */
  static sanitizeSystemStats(data: any): SystemStats {
    if (!data || typeof data !== 'object') {
      return DataDefaults.getSystemStats();
    }

    const defaults = DataDefaults.getSystemStats();
    
    return {
      status: data.status || defaults.status,
      uptime: typeof data.uptime === 'number' ? data.uptime : defaults.uptime,
      startTime: typeof data.startTime === 'string' ? data.startTime : defaults.startTime,
      memory: {
        heapUsed: data.memory?.heapUsed || defaults.memory.heapUsed,
        heapTotal: data.memory?.heapTotal || defaults.memory.heapTotal,
        external: data.memory?.external || defaults.memory.external,
        rss: data.memory?.rss || defaults.memory.rss,
      },
      cpu: {
        usage: data.cpu?.usage || defaults.cpu.usage,
        user: data.cpu?.user || defaults.cpu.user,
        system: data.cpu?.system || defaults.cpu.system,
      },
      sessions: {
        total: data.sessions?.total || defaults.sessions.total,
        active: data.sessions?.active || defaults.sessions.active,
        completed: data.sessions?.completed || defaults.sessions.completed,
        failed: data.sessions?.failed || defaults.sessions.failed,
      },
      urls: {
        total: data.urls?.total || defaults.urls.total,
        processed: data.urls?.processed || defaults.urls.processed,
        failed: data.urls?.failed || defaults.urls.failed,
        successRate: data.urls?.successRate || defaults.urls.successRate,
      },
    };
  }

  /**
   * 清理和修复队列状态数据
   */
  static sanitizeQueueStats(data: any): QueueStats[] {
    if (!Array.isArray(data)) {
      return DataDefaults.getQueueStats();
    }

    return data.map((queue, index) => {
      if (!queue || typeof queue !== 'object') {
        console.warn(`Invalid queue data at index ${index}, using defaults`);
        return DataDefaults.getQueueStats()[0];
      }

      return {
        queueName: queue.queueName || `queue-${index}`,
        waiting: typeof queue.waiting === 'number' ? queue.waiting : 0,
        active: typeof queue.active === 'number' ? queue.active : 0,
        completed: typeof queue.completed === 'number' ? queue.completed : 0,
        failed: typeof queue.failed === 'number' ? queue.failed : 0,
        delayed: typeof queue.delayed === 'number' ? queue.delayed : 0,
        paused: typeof queue.paused === 'boolean' ? queue.paused : false,
        timestamp: queue.timestamp ? new Date(queue.timestamp) : new Date(),
        totalJobs: queue.totalJobs,
        successRate: queue.successRate,
        throughput: queue.throughput,
        health: queue.health || 'healthy',
      };
    });
  }

  /**
   * 清理和修复模块状态数据
   */
  static sanitizeModuleStatus(data: any): ModuleStatus[] {
    if (!Array.isArray(data)) {
      return DataDefaults.getModuleStatus();
    }

    return data.map((module, index) => {
      if (!module || typeof module !== 'object') {
        console.warn(`Invalid module data at index ${index}, using defaults`);
        return DataDefaults.getModuleStatus()[index] || {
          name: `module-${index}`,
          status: 'stopped',
          healthy: false,
          initialized: false,
          lastChecked: new Date(),
          lastUpdate: new Date().toISOString(),
        };
      }

      return {
        name: module.name || `module-${index}`,
        status: module.status || 'stopped',
        healthy: Boolean(module.healthy),
        initialized: Boolean(module.initialized),
        lastChecked: module.lastChecked ? new Date(module.lastChecked) : new Date(),
        lastUpdate: module.lastUpdate || new Date().toISOString(),
        uptime: module.uptime,
        details: module.details,
        metrics: module.metrics,
        error: module.error,
      };
    });
  }
}
