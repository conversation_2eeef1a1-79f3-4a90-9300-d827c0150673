/**
 * 配置工厂类
 */

import {
  NodexConfig,
  TaskConfig,
  CrawlScopeConfig,
  DepthControlConfig,
  ConcurrencyConfig,
  ProxyManagerConfig,
  StorageConfig,
  QueueConfig,
  AIConfig,
  LogConfig,
  MonitoringConfig,
  TraversalStrategy,
  StorageType,
  ProxyStrategy,
  LogLevel,
  ProxyConfig,
} from '@/interfaces';

/**
 * 配置工厂类
 */
export class ConfigFactory {
  /**
   * 创建任务配置
   */
  public static createTaskConfig(options: {
    seedUrls: string[];
    strategy?: TraversalStrategy;
    name?: string;
    description?: string;
    tags?: string[];
  }): TaskConfig {
    return {
      seedUrls: options.seedUrls,
      traversalStrategy: options.strategy ?? TraversalStrategy.BFS,
      name: options.name ?? 'auto-generated-task',
      description: options.description ?? 'Auto-generated crawl task',
      tags: options.tags ?? [],
    };
  }

  /**
   * 创建抓取范围配置
   */
  public static createCrawlScopeConfig(options: {
    allowedDomains: string[];
    followSubdomains?: boolean;
    maxDepth?: number;
    disallowedPatterns?: RegExp[];
  }): CrawlScopeConfig {
    return {
      allowedDomains: options.allowedDomains,
      disallowedPatterns: options.disallowedPatterns ?? [
        /logout/i,
        /signin/i,
        /login/i,
        /register/i,
        /admin/i,
      ],
      followSubdomains: options.followSubdomains ?? false,
      ignoreWWW: true,
      allowedExtensions: ['html', 'htm', 'php', 'asp', 'aspx', 'jsp'],
      disallowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'zip', 'rar', 'exe'],
      maxFileSize: 10 * 1024 * 1024, // 10MB
    };
  }

  /**
   * 创建深度控制配置
   */
  public static createDepthControlConfig(options: {
    globalDepth: number;
    perDomainDepth?: Record<string, number>;
  }): DepthControlConfig {
    return {
      global: options.globalDepth,
      perDomain: options.perDomainDepth ?? {},
    };
  }

  /**
   * 创建并发配置
   */
  public static createConcurrencyConfig(options: {
    maxConcurrent?: number;
    delay?: number;
    timeout?: number;
    maxRetries?: number;
  }): ConcurrencyConfig {
    return {
      maxConcurrentRequests: options.maxConcurrent ?? 5,
      delayBetweenRequestsMs: options.delay ?? 1000,
      requestTimeoutMs: options.timeout ?? 30000,
      maxRetries: options.maxRetries ?? 3,
      retryDelayMs: 5000,
      domainSpecificDelay: {},
    };
  }

  /**
   * 创建代理配置
   */
  public static createProxyConfig(options: {
    enabled?: boolean;
    proxies?: Array<{ host: string; port: number; auth?: { username: string; password: string } }>;
    strategy?: ProxyStrategy;
  }): ProxyManagerConfig {
    return {
      enabled: options.enabled ?? false,
      proxies:
        (options.proxies?.map(p => ({
          host: p.host,
          port: p.port,
          ...(p.auth && { auth: p.auth }),
        })) as ProxyConfig[]) ?? [],
      strategy: options.strategy ?? ProxyStrategy.ROUND_ROBIN,
      healthCheck: {
        enabled: false,
        intervalMs: 60000,
        testUrl: 'https://httpbin.org/ip',
        timeoutMs: 10000,
      },
      failureThreshold: 3,
      recoveryTimeMs: 300000,
    };
  }

  /**
   * 创建存储配置
   */
  public static createStorageConfig(options: {
    type: StorageType;
    connectionString?: string;
    collection?: string;
    database?: string;
    batchSize?: number;
  }): StorageConfig {
    return {
      type: options.type,
      connectionString: options.connectionString ?? 'mongodb://localhost:27017/nodex',
      collection: options.collection ?? 'crawl_data',
      database: options.database ?? 'nodex',
      batchSize: options.batchSize ?? 100,
      writeIntervalMs: 5000,
      options: {
        useUnifiedTopology: true,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
      },
    };
  }

  /**
   * 创建队列配置
   */
  public static createQueueConfig(options: {
    redisHost?: string;
    redisPort?: number;
    redisPassword?: string;
    redisDb?: number;
    concurrency?: number;
  }): QueueConfig {
    return {
      redis: {
        host: options.redisHost ?? 'localhost',
        port: options.redisPort ?? 6379,
        ...(options.redisPassword && { password: options.redisPassword }),
        db: options.redisDb ?? 0,
        maxConnections: 10,
        connectTimeoutMs: 10000,
        commandTimeoutMs: 5000,
      },
      queuePrefix: 'nodex',
      defaultJobOptions: {
        delay: 0,
        priority: 0,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
      concurrency: options.concurrency ?? 5,
    };
  }

  /**
   * 创建AI配置
   */
  public static createAIConfig(options: {
    enabled?: boolean;
    provider?: 'openai' | 'gemini' | 'local';
    openaiApiKey?: string;
    geminiApiKey?: string;
    localEndpoint?: string;
  }): AIConfig {
    const config: AIConfig = {
      enabled: options.enabled ?? false,
      defaultProvider: options.provider ?? 'openai',
      timeoutMs: 30000,
      maxRetries: 3,
    };

    if (options.openaiApiKey) {
      config.openai = {
        apiKey: options.openaiApiKey,
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo',
        maxTokens: 4000,
        temperature: 0.1,
      };
    }

    if (options.geminiApiKey) {
      config.gemini = {
        apiKey: options.geminiApiKey,
        model: 'gemini-pro',
        maxTokens: 4000,
        temperature: 0.1,
      };
    }

    if (options.localEndpoint) {
      config.local = {
        endpoint: options.localEndpoint,
        model: 'local-model',
        maxTokens: 4000,
        temperature: 0.1,
      };
    }

    return config;
  }

  /**
   * 创建日志配置
   */
  public static createLoggingConfig(options: {
    level?: LogLevel;
    console?: boolean;
    filePath?: string;
    format?: 'json' | 'simple' | 'combined';
  }): LogConfig {
    return {
      level: options.level ?? LogLevel.INFO,
      format: options.format ?? 'json',
      console: options.console !== false,
      filePath: options.filePath ?? './logs/nodex.log',
      rotation: {
        maxSize: '100m',
        maxFiles: 10,
      },
    };
  }

  /**
   * 创建监控配置
   */
  public static createMonitoringConfig(options: {
    enabled?: boolean;
    port?: number;
    statsInterval?: number;
  }): MonitoringConfig {
    return {
      enabled: options.enabled !== false,
      port: options.port ?? 3001,
      statsIntervalMs: options.statsInterval ?? 10000,
      healthCheck: true,
      metrics: true,
    };
  }

  /**
   * 创建完整的配置
   */
  public static createFullConfig(options: {
    seedUrls: string[];
    allowedDomains: string[];
    strategy?: TraversalStrategy;
    maxDepth?: number;
    maxConcurrent?: number;
    storageType?: StorageType;
    mongoUri?: string;
    redisHost?: string;
    redisPort?: number;
    enableAI?: boolean;
    openaiApiKey?: string;
    enableProxy?: boolean;
    logLevel?: LogLevel;
  }): NodexConfig {
    return {
      task: this.createTaskConfig({
        seedUrls: options.seedUrls,
        ...(options.strategy && { strategy: options.strategy }),
      }),
      crawlScope: this.createCrawlScopeConfig({
        allowedDomains: options.allowedDomains,
        ...(options.maxDepth && { maxDepth: options.maxDepth }),
      }),
      depthControl: this.createDepthControlConfig({
        globalDepth: options.maxDepth ?? 3,
      }),
      concurrency: this.createConcurrencyConfig({
        ...(options.maxConcurrent && { maxConcurrent: options.maxConcurrent }),
      }),
      proxy: this.createProxyConfig({
        ...(options.enableProxy !== undefined && { enabled: options.enableProxy }),
      }),
      storage: this.createStorageConfig({
        type: options.storageType ?? StorageType.MONGODB,
        ...(options.mongoUri && { connectionString: options.mongoUri }),
      }),
      queue: this.createQueueConfig({
        ...(options.redisHost && { redisHost: options.redisHost }),
        ...(options.redisPort && { redisPort: options.redisPort }),
      }),
      ai: this.createAIConfig({
        ...(options.enableAI !== undefined && { enabled: options.enableAI }),
        ...(options.openaiApiKey && { openaiApiKey: options.openaiApiKey }),
      }),
      logging: this.createLoggingConfig({
        ...(options.logLevel && { level: options.logLevel }),
      }),
      monitoring: this.createMonitoringConfig({}),
      userAgent:
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      respectRobotsTxt: true,
      requestTimeoutMs: 30000,
    };
  }

  /**
   * 创建开发环境配置
   */
  public static createDevelopmentConfig(seedUrls: string[], allowedDomains: string[]): NodexConfig {
    return this.createFullConfig({
      seedUrls,
      allowedDomains,
      strategy: TraversalStrategy.BFS,
      maxDepth: 2,
      maxConcurrent: 3,
      storageType: StorageType.MONGODB,
      enableAI: false,
      enableProxy: false,
      logLevel: LogLevel.DEBUG,
    });
  }

  /**
   * 创建生产环境配置
   */
  public static createProductionConfig(seedUrls: string[], allowedDomains: string[]): NodexConfig {
    return this.createFullConfig({
      seedUrls,
      allowedDomains,
      strategy: TraversalStrategy.BFS,
      maxDepth: 5,
      maxConcurrent: 10,
      storageType: StorageType.MONGODB,
      enableAI: true,
      enableProxy: true,
      logLevel: LogLevel.INFO,
    });
  }

  /**
   * 创建默认配置
   */
  public static createDefaultConfig(): NodexConfig {
    return this.createFullConfig({
      seedUrls: ['https://example.com'],
      allowedDomains: ['example.com'],
      strategy: TraversalStrategy.BFS,
      maxDepth: 3,
      maxConcurrent: 5,
      storageType: StorageType.MONGODB,
      enableAI: false,
      enableProxy: false,
      logLevel: LogLevel.INFO,
    });
  }
}
