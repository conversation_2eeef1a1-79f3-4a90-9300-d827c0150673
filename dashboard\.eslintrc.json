{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:react/jsx-runtime", "prettier"], "ignorePatterns": ["dist", ".eslintrc.cjs"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "react-hooks", "react-refresh", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "warn", "prefer-const": "error", "no-var": "error", "no-console": "warn", "react-hooks/exhaustive-deps": "warn", "react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "react/prop-types": "off", "react/react-in-jsx-scope": "off"}, "settings": {"react": {"version": "detect"}}}