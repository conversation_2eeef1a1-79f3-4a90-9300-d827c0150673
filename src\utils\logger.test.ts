/**
 * Logger 模块测试
 */

import { Logger, LogLevel } from './logger';
import { loggerManager, getLogger } from './logger-manager';
import { debug, info, warn, error, time, errorWithStack } from './logger';
import fs from 'fs';
import path from 'path';

describe('Logger 模块测试', () => {
  const testLogFile = path.join(__dirname, '../../test-logs/test.log');

  beforeEach(() => {
    // 清理测试日志文件
    if (fs.existsSync(testLogFile)) {
      fs.unlinkSync(testLogFile);
    }

    // 确保测试日志目录存在
    const logDir = path.dirname(testLogFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  });

  afterEach(() => {
    // 清理测试日志文件
    if (fs.existsSync(testLogFile)) {
      fs.unlinkSync(testLogFile);
    }
  });

  describe('基础日志功能', () => {
    test('应该正确创建 Logger 实例', () => {
      const logger = new Logger({
        level: LogLevel.DEBUG,
        console: false,
      });

      expect(logger).toBeDefined();
      expect(logger.isLevelEnabled(LogLevel.DEBUG)).toBe(true);
      expect(logger.isLevelEnabled(LogLevel.INFO)).toBe(true);
    });

    test('应该正确过滤日志级别', () => {
      const logger = new Logger({
        level: LogLevel.WARN,
        console: false,
      });

      expect(logger.isLevelEnabled(LogLevel.DEBUG)).toBe(false);
      expect(logger.isLevelEnabled(LogLevel.INFO)).toBe(false);
      expect(logger.isLevelEnabled(LogLevel.WARN)).toBe(true);
      expect(logger.isLevelEnabled(LogLevel.ERROR)).toBe(true);
    });

    test('应该支持环境变量配置', () => {
      // 设置环境变量
      process.env.LOG_LEVEL = 'debug';

      const logger = new Logger();
      expect(logger.isLevelEnabled(LogLevel.DEBUG)).toBe(true);

      // 清理环境变量
      delete process.env.LOG_LEVEL;
    });
  });

  describe('文件日志功能', () => {
    test('应该正确写入日志文件', async () => {
      const logger = new Logger({
        level: LogLevel.INFO,
        console: false,
        file: testLogFile,
        format: 'text',
      });

      await logger.initialize();
      logger.info('测试日志消息', { test: true });

      // 等待文件写入
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(fs.existsSync(testLogFile)).toBe(true);
      const content = fs.readFileSync(testLogFile, 'utf-8');
      expect(content).toContain('测试日志消息');
      expect(content).toContain('INFO');
    });

    test('应该支持 JSON 格式', async () => {
      const logger = new Logger({
        level: LogLevel.INFO,
        console: false,
        file: testLogFile,
        format: 'json',
      });

      await logger.initialize();
      logger.info('JSON 测试', { data: 'test' });

      // 等待文件写入
      await new Promise(resolve => setTimeout(resolve, 100));

      const content = fs.readFileSync(testLogFile, 'utf-8');
      const logLine = JSON.parse(content.trim());

      expect(logLine.level).toBe('INFO');
      expect(logLine.message).toBe('JSON 测试');
      expect(logLine.data).toEqual({ data: 'test' });
    });
  });

  describe('日志管理器功能', () => {
    test('应该正确创建模块日志器', () => {
      const userLogger = getLogger('UserService');
      const authLogger = getLogger('AuthService');

      expect(userLogger).toBeDefined();
      expect(authLogger).toBeDefined();
      expect(userLogger).not.toBe(authLogger);
    });

    test('应该正确管理日志器统计', () => {
      getLogger('TestModule1');
      getLogger('TestModule2');

      const stats = loggerManager.getStats();
      expect(stats.totalLoggers).toBeGreaterThanOrEqual(3); // 包括全局日志器
      expect(stats.moduleLoggers).toContain('TestModule1');
      expect(stats.moduleLoggers).toContain('TestModule2');
    });
  });

  describe('便捷日志方法', () => {
    test('应该正确工作全局日志方法', () => {
      // 这些方法应该不抛出错误
      expect(() => {
        debug('调试消息');
        info('信息消息');
        warn('警告消息');
        error('错误消息');
      }).not.toThrow();
    });

    test('应该正确测量执行时间', async () => {
      const timer = time('测试操作');

      // 模拟一些工作
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(() => timer()).not.toThrow();
    });

    test('应该正确记录错误堆栈', () => {
      const testError = new Error('测试错误');

      expect(() => {
        errorWithStack('操作失败', testError, 'TestModule');
      }).not.toThrow();
    });
  });

  describe('性能优化功能', () => {
    test('懒加载日志应该在级别不够时不执行', () => {
      const logger = new Logger({
        level: LogLevel.ERROR,
        console: false,
      });

      let executed = false;
      logger.debugLazy(() => {
        executed = true;
        return '不应该执行的消息';
      });

      expect(executed).toBe(false);
    });

    test('懒加载日志应该在级别足够时执行', () => {
      const logger = new Logger({
        level: LogLevel.DEBUG,
        console: false,
      });

      let executed = false;
      logger.debugLazy(() => {
        executed = true;
        return '应该执行的消息';
      });

      expect(executed).toBe(true);
    });
  });

  describe('子日志器功能', () => {
    test('应该正确创建子日志器', () => {
      const parentLogger = new Logger({
        level: LogLevel.DEBUG,
        console: false,
      });

      const childLogger = parentLogger.createChild('ChildModule');
      expect(childLogger).toBeDefined();
    });
  });
});
