/**
 * 解析器管理器实现
 */

import {
  IParserManager,
  IParser,
  ParserInput,
  ParserOutput,
  ParserConfig,
} from '@/interfaces';
import { CheerioParser } from './cheerio-parser';
import { ReadabilityParser } from './readability-parser';
import { getLogger, info, error, warn, debug } from '@/utils';
import { AIParser } from './ai-parser';
import { JsonParser } from './json-parser';
import { XmlParser } from './xml-parser';
import { ParserFactory, ParserType } from './parser-factory';
// import { ParserAnalyzer } from './parser-analyzer';

/**
 * 解析器注册信息
 */
interface ParserRegistration {
  parser: IParser;
  config: ParserConfig;
}

/**
 * 解析器管理器实现类
 */
export class ParserManager implements IParserManager {
  private parsers: Map<string, ParserRegistration> = new Map();
  private parserFactory: ParserFactory;
  private aiEngine: any = null;
  private logger = getLogger('ParserManager');
  private stats = {
    totalParses: 0,
    successfulParses: 0,
    failedParses: 0,
    totalParseTime: 0,
    averageParseTime: 0,
    lastParsed: new Date(),
    parserUsage: new Map<string, number>(),
  };

  constructor(aiEngine?: any) {
    this.parserFactory = ParserFactory.getInstance();
    this.aiEngine = aiEngine;

    // 注册默认解析器
    this.registerDefaultParsers();
  }

  /**
   * 注册默认解析器
   */
  private registerDefaultParsers(): void {
    // 注册Readability解析器（优先级最高）
    const readabilityParser = new ReadabilityParser({
      extractLinks: true,
      includeRawHtml: true,
      minContentLength: 140,
      minScore: 20,
    });

    this.registerParser(readabilityParser, {
      name: 'readability-parser',
      enabled: true,
      priority: 95, // 最高优先级
      contentTypes: ['text/html', 'application/xhtml+xml'],
      urlPatterns: [],
    });

    // 注册基础HTML解析器（作为后备）
    const cheerioParser = new CheerioParser({
      selectors: {
        title: 'h1, title, .title',
        content: '.content, .main, article, .post',
        links: 'a[href]',
      },
      extractLinks: true,
      linkSelector: 'a[href]',
    });

    this.registerParser(cheerioParser, {
      name: 'cheerio-parser',
      enabled: true,
      priority: 70, // 较低优先级，作为后备
      contentTypes: ['text/html', 'application/xhtml+xml'],
      urlPatterns: [],
    });

    // 注册JSON解析器
    const jsonParser = new JsonParser({
      pathMappings: {},
      extractNested: true,
      extractArrays: true,
    });

    this.registerParser(jsonParser, {
      name: 'json-parser',
      enabled: true,
      priority: 90,
      contentTypes: ['application/json', 'text/json'],
      urlPatterns: [/\/api\//, /\.json$/],
    });

    // 注册XML解析器
    const xmlParser = new XmlParser({
      elementMappings: {},
      linkExtraction: {
        enabled: true,
        linkElements: ['link', 'url', 'loc'],
        linkAttributes: ['href', 'src'],
      },
    });

    this.registerParser(xmlParser, {
      name: 'xml-parser',
      enabled: true,
      priority: 85,
      contentTypes: ['application/xml', 'text/xml', 'application/rss+xml', 'application/atom+xml'],
      urlPatterns: [/\.xml$/, /\.rss$/, /\.atom$/, /\/feed/, /\/rss/],
    });

    // 如果有AI引擎，注册AI解析器
    if (this.aiEngine) {
      const aiParser = new AIParser(this.aiEngine, {
        schema: {},
        extractLinks: true,
        model: 'gpt-3.5-turbo',
        maxTokens: 4000,
        temperature: 0.1,
      });

      this.registerParser(aiParser, {
        name: 'ai-parser',
        enabled: true,
        priority: 60,
        contentTypes: [],
        urlPatterns: [],
      });
    }

    debug(`Registered ${this.parsers.size} default parsers`);
  }

  /**
   * 注册解析器
   */
  public registerParser(parser: IParser, config: ParserConfig): void {
    if (this.parsers.has(parser.name)) {
      warn(`Parser '${parser.name}' is already registered. Overwriting...`);
    }

    this.parsers.set(parser.name, { parser, config });
    this.stats.parserUsage.set(parser.name, 0);
    
    info(`Registered parser: ${parser.name} (priority: ${config.priority}, enabled: ${config.enabled})`);
  }

  /**
   * 注销解析器
   */
  public unregisterParser(name: string): void {
    const registration = this.parsers.get(name);
    if (registration) {
      this.parsers.delete(name);
      this.stats.parserUsage.delete(name);
      info(`Unregistered parser: ${name}`);
    }
  }

  /**
   * 获取适用的解析器
   */
  public async getApplicableParsers(input: ParserInput): Promise<IParser[]> {
    const applicableParsers: Array<{ parser: IParser; priority: number }> = [];

    for (const [name, registration] of this.parsers) {
      const { parser, config } = registration;
      
      // 检查解析器是否启用
      if (!config.enabled) {
        continue;
      }

      // 检查URL模式匹配
      if (config.urlPatterns && config.urlPatterns.length > 0) {
        const urlMatches = config.urlPatterns.some(pattern => pattern.test(input.url));
        if (!urlMatches) {
          continue;
        }
      }

      // 检查域名匹配
      if (config.domains && config.domains.length > 0) {
        const url = new URL(input.url);
        const domainMatches = config.domains.some(domain => 
          url.hostname === domain || url.hostname.endsWith(`.${domain}`)
        );
        if (!domainMatches) {
          continue;
        }
      }

      // 检查内容类型匹配
      if (config.contentTypes && config.contentTypes.length > 0) {
        const contentType = input.headers?.['content-type'] || '';
        const contentTypeMatches = config.contentTypes.some(type => 
          contentType.includes(type)
        );
        if (!contentTypeMatches) {
          continue;
        }
      }

      // 检查解析器是否可以处理该输入
      try {
        const canParse = await parser.canParse(input);
        if (canParse) {
          applicableParsers.push({ parser, priority: config.priority });
        }
      } catch (error) {
        warn(`Error checking if parser '${name}' can parse:`, error);
      }
    }

    // 按优先级排序（数字越大优先级越高）
    applicableParsers.sort((a, b) => b.priority - a.priority);
    
    return applicableParsers.map(item => item.parser);
  }

  /**
   * 执行解析
   */
  public async parse(input: ParserInput): Promise<ParserOutput> {
    const startTime = Date.now();
    this.stats.totalParses++;

    try {
      // 获取适用的解析器
      const applicableParsers = await this.getApplicableParsers(input);
      
      if (applicableParsers.length === 0) {
        return {
          extractedData: null,
          newUrls: [],
          error: {
            message: 'No applicable parser found',
            code: 'NO_PARSER',
          },
        };
      }

      // 尝试使用适用的解析器，如果失败则尝试下一个
      let lastError: any = null;
      
      for (const parser of applicableParsers) {
        try {
          // 更新使用统计
          const currentUsage = this.stats.parserUsage.get(parser.name) || 0;
          this.stats.parserUsage.set(parser.name, currentUsage + 1);

          // 执行解析
          const result = await parser.parse(input);
          
          // 如果解析成功（没有错误），返回结果
          if (!result.error) {
            // 更新统计信息
            const parseTime = Date.now() - startTime;
            this.stats.totalParseTime += parseTime;
            this.stats.successfulParses++;
            this.updateStats(parseTime, true);
            
            info(`Successfully parsed with ${parser.name}: ${input.url}`);
            return result;
          } else {
            // 解析器返回了错误，记录并尝试下一个
            warn(`Parser ${parser.name} failed for ${input.url}: ${result.error.message}`);
            lastError = result.error;
            continue;
          }
        } catch (error) {
          // 解析器抛出异常，记录并尝试下一个
          warn(`Parser ${parser.name} threw exception for ${input.url}:`, error);
          lastError = error;
          continue;
        }
      }
      
      // 所有解析器都失败了，返回最后一个错误
      const parseTime = Date.now() - startTime;
      this.stats.totalParseTime += parseTime;
      this.stats.failedParses++;
      this.updateStats(parseTime, false);
      
      return {
        extractedData: null,
        newUrls: [],
        error: {
          message: lastError?.message || 'All parsers failed',
          code: lastError?.code || 'ALL_PARSERS_FAILED',
          details: lastError?.details || lastError,
        },
      };
      
    } catch (error) {
      this.stats.failedParses++;
      
      return {
        extractedData: null,
        newUrls: [],
        error: {
          message: error instanceof Error ? error.message : 'Unknown parsing error',
          code: 'PARSE_ERROR',
          details: error,
        },
      };
    }
  }

  /**
   * 获取所有已注册的解析器
   */
  public getRegisteredParsers(): Map<string, { parser: IParser; config: ParserConfig }> {
    return new Map(this.parsers);
  }

  /**
   * 初始化所有解析器
   */
  public async initialize(): Promise<void> {
    const initPromises = Array.from(this.parsers.values()).map(async (registration) => {
      const { parser } = registration;
      if (parser.initialize) {
        try {
          await parser.initialize();
          info(`Initialized parser: ${parser.name}`);
        } catch (err) {
          error(`Failed to initialize parser '${parser.name}':`, err);
        }
      }
    });

    await Promise.all(initPromises);
    info('All parsers initialized');
  }

  /**
   * 清理所有解析器
   */
  public async cleanup(): Promise<void> {
    const cleanupPromises = Array.from(this.parsers.values()).map(async (registration) => {
      const { parser } = registration;
      if (parser.cleanup) {
        try {
          await parser.cleanup();
          info(`Cleaned up parser: ${parser.name}`);
        } catch (err) {
          error(`Failed to cleanup parser '${parser.name}':`, err);
        }
      }
    });

    await Promise.all(cleanupPromises);
    info('All parsers cleaned up');
  }

  /**
   * 获取解析器统计信息
   */
  public getStats(): {
    totalParses: number;
    successfulParses: number;
    failedParses: number;
    averageParseTime: number;
    successRate: number;
    parserUsage: Record<string, number>;
    registeredParsers: Array<{
      name: string;
      enabled: boolean;
      priority: number;
      usageCount: number;
    }>;
  } {
    const parserUsage: Record<string, number> = {};
    this.stats.parserUsage.forEach((count, name) => {
      parserUsage[name] = count;
    });

    const registeredParsers = Array.from(this.parsers.entries()).map(([name, registration]) => ({
      name,
      enabled: registration.config.enabled,
      priority: registration.config.priority,
      usageCount: this.stats.parserUsage.get(name) || 0,
    }));

    return {
      totalParses: this.stats.totalParses,
      successfulParses: this.stats.successfulParses,
      failedParses: this.stats.failedParses,
      averageParseTime: this.stats.totalParses > 0 
        ? this.stats.totalParseTime / this.stats.totalParses 
        : 0,
      successRate: this.stats.totalParses > 0 
        ? this.stats.successfulParses / this.stats.totalParses 
        : 0,
      parserUsage,
      registeredParsers,
    };
  }

  /**
   * 启用解析器
   */
  public enableParser(name: string): void {
    const registration = this.parsers.get(name);
    if (registration) {
      registration.config.enabled = true;
      info(`Enabled parser: ${name}`);
    }
  }

  /**
   * 禁用解析器
   */
  public disableParser(name: string): void {
    const registration = this.parsers.get(name);
    if (registration) {
      registration.config.enabled = false;
      info(`Disabled parser: ${name}`);
    }
  }

  /**
   * 设置解析器优先级
   */
  public setParserPriority(name: string, priority: number): void {
    const registration = this.parsers.get(name);
    if (registration) {
      registration.config.priority = priority;
      info(`Set parser '${name}' priority to ${priority}`);
    }
  }

  /**
   * 更新解析器配置
   */
  public updateParserConfig(name: string, updates: Partial<ParserConfig>): void {
    const registration = this.parsers.get(name);
    if (registration) {
      registration.config = { ...registration.config, ...updates };
      info(`Updated parser '${name}' configuration`);
    }
  }

  /**
   * 获取解析器配置
   */
  public getParserConfig(name: string): ParserConfig | null {
    const registration = this.parsers.get(name);
    return registration ? { ...registration.config } : null;
  }

  /**
   * 检查解析器是否存在
   */
  public hasParser(name: string): boolean {
    return this.parsers.has(name);
  }

  /**
   * 获取解析器实例
   */
  public getParser(name: string): IParser | null {
    const registration = this.parsers.get(name);
    return registration ? registration.parser : null;
  }

  /**
   * 获取启用的解析器数量
   */
  public getEnabledParserCount(): number {
    return Array.from(this.parsers.values()).filter(reg => reg.config.enabled).length;
  }

  /**
   * 获取解析器数量
   */
  public getParserCount(): number {
    return this.parsers.size;
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalParses: 0,
      successfulParses: 0,
      failedParses: 0,
      totalParseTime: 0,
      averageParseTime: 0,
      lastParsed: new Date(),
      parserUsage: new Map(),
    };

    // 重新初始化解析器使用统计
    for (const name of this.parsers.keys()) {
      this.stats.parserUsage.set(name, 0);
    }
  }

  /**
   * 测试解析器
   */
  public async testParser(name: string, input: ParserInput): Promise<ParserOutput> {
    const registration = this.parsers.get(name);
    if (!registration) {
      throw new Error(`Parser '${name}' not found`);
    }

    return await registration.parser.parse(input);
  }

  /**
   * 从工厂创建并注册解析器
   */
  public createAndRegisterParser(type: ParserType, options?: any, config?: Partial<ParserConfig>): void {
    const { parser, config: defaultConfig } = this.parserFactory.createParser({
      type,
      options,
      enabled: true,
      priority: 50,
    }, this.aiEngine);

    const finalConfig: ParserConfig = {
      ...defaultConfig,
      ...config,
    };

    this.registerParser(parser, finalConfig);
  }

  /**
   * 更新统计信息
   */
  private updateStats(parseTime: number, success: boolean): void {
    this.stats.totalParses++;
    if (success) {
      this.stats.successfulParses++;
    } else {
      this.stats.failedParses++;
    }

    this.stats.totalParseTime += parseTime;
    this.stats.averageParseTime = this.stats.totalParseTime / this.stats.totalParses;
    this.stats.lastParsed = new Date();
  }

  /**
   * 获取解析器推荐
   */
  public getParserRecommendations(input: ParserInput): Array<{
    parserName: string;
    confidence: number;
    reason: string;
  }> {
    // 增强的推荐逻辑
    const contentType = input.headers?.['content-type'] || 'unknown';
    const url = input.url || '';
    const content = input.content || '';
    const recommendations = [];

    // 基于内容类型的推荐
    if (contentType.includes('html')) {
      // 检查内容大小和复杂度
      const isLargeContent = content.length > 50000;
      const hasComplexStructure = content.includes('<script') || content.includes('<iframe');
      
      if (hasComplexStructure) {
        recommendations.push({ 
          parserName: 'readability', 
          confidence: 0.85, 
          reason: 'Complex HTML structure detected, readability parser recommended' 
        });
      }
      
      recommendations.push({ 
        parserName: 'cheerio', 
        confidence: isLargeContent ? 0.9 : 0.8, 
        reason: `HTML content detected (${isLargeContent ? 'large' : 'normal'} size)` 
      });
      
    } else if (contentType.includes('json')) {
      recommendations.push({ 
        parserName: 'json', 
        confidence: 0.95, 
        reason: 'JSON content detected' 
      });
      
    } else if (contentType.includes('xml')) {
      recommendations.push({ 
        parserName: 'xml', 
        confidence: 0.9, 
        reason: 'XML content detected' 
      });
    }

    // 基于URL模式的推荐
    if (url.includes('/api/') || url.includes('.json')) {
      recommendations.push({ 
        parserName: 'json', 
        confidence: 0.8, 
        reason: 'API endpoint detected in URL' 
      });
    }

    // 基于历史性能的推荐
    const performanceRanking = this.getPerformanceRanking();
    if (performanceRanking.length > 0) {
      const topPerformer = performanceRanking[0];
      if (topPerformer) {
        recommendations.push({
          parserName: topPerformer.parserName,
          confidence: 0.7,
          reason: `Top performing parser (${topPerformer.successRate.toFixed(2)}% success rate)`
        });
      }
    }

    // 排序推荐结果
    recommendations.sort((a, b) => b.confidence - a.confidence);

    return recommendations.slice(0, 3); // 返回前3个推荐
  }

  /**
   * 获取解析器性能指标
   */
  public getParserMetrics(parserName?: string): any {
    if (parserName) {
      return { parserName, usage: this.stats.parserUsage.get(parserName) || 0 };
    }
    return { stats: this.stats };
  }

  /**
   * 获取性能排名
   */
  public getPerformanceRanking(): Array<{
    parserName: string;
    score: number;
    successRate: number;
    averageTime: number;
    totalUsage: number;
    metrics: any;
  }> {
    // 基于现有统计数据的性能排名计算
    const ranking = [];
    
    for (const [parserName, usage] of this.stats.parserUsage) {
      // 基于全局统计信息计算个别解析器的近似指标
      const totalAttempts = Math.max(usage, 1);
      const estimatedSuccessRate = this.stats.totalParses > 0 
        ? (this.stats.successfulParses / this.stats.totalParses) * 100 
        : 0;
      
      // 估算平均处理时间（基于全局平均值）
      const averageTime = this.stats.averageParseTime;
      
      // 基于使用频率的权重评分：使用频率高的解析器通常表现更好
      const usageScore = Math.min((usage / Math.max(this.stats.totalParses, 1)) * 100, 50);
      
      // 综合评分：成功率权重50%，使用频率权重30%，速度权重20%
      const speedScore = averageTime > 0 ? Math.max(0, 100 - (averageTime / 100)) : 50;
      const score = (estimatedSuccessRate * 0.5) + (usageScore * 0.3) + (speedScore * 0.2);
      
      ranking.push({
        parserName,
        score,
        successRate: estimatedSuccessRate,
        averageTime,
        totalUsage: usage,
        metrics: { 
          usage,
          estimatedSuccess: Math.round(usage * (estimatedSuccessRate / 100)),
          estimatedErrors: Math.round(usage * ((100 - estimatedSuccessRate) / 100)),
          globalAverageTime: averageTime,
          usageFrequency: usage / Math.max(this.stats.totalParses, 1)
        }
      });
    }
    
    return ranking.sort((a, b) => b.score - a.score);
  }

  /**
   * 检测性能异常
   */
  public detectPerformanceAnomalies(parserName?: string): Array<{
    type: string;
    severity: string;
    description: string;
    timestamp: Date;
  }> {
    const anomalies = [];
    const currentTime = new Date();
    
    // 检查整体性能指标
    if (this.stats.totalParses > 0) {
      const successRate = this.stats.successfulParses / this.stats.totalParses;
      const failureRate = this.stats.failedParses / this.stats.totalParses;
      
      // 检测高失败率
      if (failureRate > 0.3) {
        anomalies.push({
          type: 'high_failure_rate',
          severity: failureRate > 0.5 ? 'critical' : 'warning',
          description: `整体失败率过高: ${(failureRate * 100).toFixed(1)}%`,
          timestamp: currentTime
        });
      }
      
      // 检测低成功率
      if (successRate < 0.5 && this.stats.totalParses > 10) {
        anomalies.push({
          type: 'low_success_rate',
          severity: successRate < 0.3 ? 'critical' : 'warning',
          description: `整体成功率过低: ${(successRate * 100).toFixed(1)}%`,
          timestamp: currentTime
        });
      }
    }
    
    // 检测平均处理时间异常
    if (this.stats.averageParseTime > 10000) {
      anomalies.push({
        type: 'slow_processing',
        severity: this.stats.averageParseTime > 30000 ? 'critical' : 'warning',
        description: `平均处理时间过长: ${this.stats.averageParseTime.toFixed(0)}ms`,
        timestamp: currentTime
      });
    }
    
    // 检查特定解析器的异常（如果指定了解析器名称）
    if (parserName) {
      const usage = this.stats.parserUsage.get(parserName) || 0;
      const totalUsage = Array.from(this.stats.parserUsage.values()).reduce((sum, val) => sum + val, 0);
      
      if (totalUsage > 0) {
        const usageRate = usage / totalUsage;
        
        // 检测解析器使用率异常低
        if (usageRate < 0.05 && this.stats.totalParses > 50) {
          anomalies.push({
            type: 'low_usage_rate',
            severity: 'warning',
            description: `解析器 ${parserName} 使用率过低: ${(usageRate * 100).toFixed(1)}%`,
            timestamp: currentTime
          });
        }
      }
      
      // 检测解析器从未被使用
      if (usage === 0 && this.stats.totalParses > 0) {
        anomalies.push({
          type: 'unused_parser',
          severity: 'warning',
          description: `解析器 ${parserName} 从未被使用`,
          timestamp: currentTime
        });
      }
    }
    
    return anomalies;
  }

  /**
   * 获取解析器模板
   */
  public getParserTemplates(): Array<{
    name: string;
    description: string;
    type: ParserType;
    suggestedUseCase: string;
  }> {
    return this.parserFactory.getTemplates();
  }

  /**
   * 从模板创建解析器
   */
  public createFromTemplate(templateName: string, customOptions?: any, config?: Partial<ParserConfig>): void {
    const { parser, config: defaultConfig } = this.parserFactory.createFromTemplate(
      templateName,
      customOptions,
      this.aiEngine
    );

    const finalConfig: ParserConfig = {
      ...defaultConfig,
      ...config,
    };

    this.registerParser(parser, finalConfig);
  }

  /**
   * 智能选择最佳解析器
   */
  public async selectBestParser(input: ParserInput): Promise<IParser | null> {
    // 获取推荐的解析器配置
    const recommendations = this.parserFactory.recommendParser(input);

    if (recommendations.length === 0) {
      return null;
    }

    // 检查是否已有对应的解析器注册
    for (const recommendation of recommendations) {
      const existingParser = this.findParserByType(recommendation.type);
      if (existingParser) {
        return existingParser;
      }
    }

    // 如果没有合适的已注册解析器，创建一个新的
    const bestRecommendation = recommendations[0];
    if (!bestRecommendation) {
      throw new Error('No parser recommendations available');
    }

    const { parser } = this.parserFactory.createParser(bestRecommendation, this.aiEngine);

    // 临时注册这个解析器
    this.registerParser(parser, {
      name: `dynamic-${bestRecommendation.type}`,
      enabled: true,
      priority: bestRecommendation.priority || 50,
      contentTypes: [],
      urlPatterns: [],
    });

    return parser;
  }

  /**
   * 根据类型查找解析器
   */
  private findParserByType(type: ParserType): IParser | null {
    for (const registration of this.parsers.values()) {
      const parser = registration.parser;

      // 简单的类型匹配逻辑
      if (
        (type === ParserType.CHEERIO && parser.name.includes('cheerio')) ||
        (type === ParserType.JSON && parser.name.includes('json')) ||
        (type === ParserType.XML && parser.name.includes('xml')) ||
        (type === ParserType.AI && parser.name.includes('ai'))
      ) {
        return parser;
      }
    }

    return null;
  }

  /**
   * 导出性能报告
   */
  public exportPerformanceReport(): {
    summary: any;
    parserMetrics: any[];
    recommendations: string[];
  } {
    const ranking = this.getPerformanceRanking();
    const anomalies = this.detectPerformanceAnomalies();
    
    // 生成建议
    const recommendations = [];
    
    // 基于性能排名的建议
    if (ranking.length > 0) {
      const topPerformer = ranking[0];
      const worstPerformer = ranking[ranking.length - 1];
      
      if (topPerformer && topPerformer.score > 80) {
        recommendations.push(`${topPerformer.parserName} 表现优秀 (${topPerformer.score.toFixed(1)}分)，建议优先使用`);
      }
      
      if (worstPerformer && worstPerformer.score < 30) {
        recommendations.push(`${worstPerformer.parserName} 表现较差 (${worstPerformer.score.toFixed(1)}分)，建议检查配置或禁用`);
      }
    }
    
    // 基于异常的建议
    const criticalAnomalies = anomalies.filter(a => a.severity === 'critical');
    if (criticalAnomalies.length > 0) {
      recommendations.push(`发现 ${criticalAnomalies.length} 个严重性能问题，建议立即处理`);
    }
    
    // 基于使用率的建议
    const unusedParsers = Array.from(this.parsers.keys()).filter(name => 
      (this.stats.parserUsage.get(name) || 0) === 0
    );
    if (unusedParsers.length > 0) {
      recommendations.push(`${unusedParsers.length} 个解析器未被使用: ${unusedParsers.join(', ')}`);
    }
    
    return {
      summary: {
        totalParsers: this.parsers.size,
        enabledParsers: this.getEnabledParserCount(),
        totalParses: this.stats.totalParses,
        successfulParses: this.stats.successfulParses,
        failedParses: this.stats.failedParses,
        successRate: this.stats.totalParses > 0 ? this.stats.successfulParses / this.stats.totalParses : 0,
        averageParseTime: this.stats.averageParseTime,
        lastParsed: this.stats.lastParsed,
        anomaliesCount: anomalies.length,
        criticalAnomaliesCount: criticalAnomalies.length
      },
      parserMetrics: ranking.map(parser => ({
        name: parser.parserName,
        score: parser.score,
        successRate: parser.successRate,
        averageTime: parser.averageTime,
        totalUsage: parser.totalUsage,
        usageFrequency: parser.metrics.usageFrequency,
        enabled: this.parsers.get(parser.parserName)?.config.enabled || false,
        priority: this.parsers.get(parser.parserName)?.config.priority || 0
      })),
      recommendations
    };
  }

  /**
   * 重置性能分析数据
   */
  public resetPerformanceAnalysis(parserName?: string): void {
    // 简化的重置逻辑
    if (parserName) {
      this.stats.parserUsage.set(parserName, 0);
    } else {
      this.resetStats();
    }
  }

  /**
   * 设置AI引擎
   */
  public setAIEngine(aiEngine: any): void {
    this.aiEngine = aiEngine;

    // 如果还没有AI解析器，创建一个
    if (!this.findParserByType(ParserType.AI)) {
      this.createAndRegisterParser(ParserType.AI, {
        schema: {},
        extractLinks: true,
        model: 'gpt-3.5-turbo',
        maxTokens: 4000,
        temperature: 0.1,
      }, {
        enabled: true,
        priority: 60,
      });
    }
  }

  /**
   * 批量解析
   */
  public async batchParse(inputs: ParserInput[]): Promise<ParserOutput[]> {
    const results: ParserOutput[] = [];

    for (const input of inputs) {
      try {
        const result = await this.parse(input);
        results.push(result);
      } catch (error) {
        results.push({
          extractedData: null,
          newUrls: [],
          error: {
            message: error instanceof Error ? error.message : 'Batch parse error',
            code: 'BATCH_PARSE_ERROR',
            details: error,
          },
        });
      }
    }

    return results;
  }

  /**
   * 获取解析器健康状态
   */
  public getParserHealth(): Record<string, {
    status: 'healthy' | 'warning' | 'critical';
    metrics: any;
    anomalies: any[];
  }> {
    const health: Record<string, {
      status: 'healthy' | 'warning' | 'critical';
      metrics: any;
      anomalies: any[];
    }> = {};

    for (const parserName of this.parsers.keys()) {
      const usage = this.stats.parserUsage.get(parserName) || 0;

      let status: 'healthy' | 'warning' | 'critical' = 'healthy';

      // 简化的健康检查
      if (usage === 0) {
        status = 'warning';
      }

      health[parserName] = {
        status,
        metrics: { usage },
        anomalies: [],
      };
    }

    return health;
  }
}
