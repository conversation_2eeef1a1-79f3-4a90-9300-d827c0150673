/**
 * 默认配置
 */

import { NodexConfig, TraversalStrategy, StorageType, ProxyStrategy, LogLevel } from '@/interfaces';

/**
 * 系统默认配置
 */
export const defaultConfig: NodexConfig = {
  // 1. 任务定义
  task: {
    seedUrls: [],
    traversalStrategy: TraversalStrategy.BFS,
    name: 'default-task',
    description: '默认爬取任务',
    tags: ['default'],
  },

  // 2. 抓取范围与策略
  crawlScope: {
    allowedDomains: [],
    disallowedPatterns: [
      /logout/i,
      /signin/i,
      /login/i,
      /register/i,
      /admin/i,
      /\.pdf$/i,
      /\.zip$/i,
      /\.exe$/i,
    ],
    followSubdomains: false,
    ignoreWWW: true,
    allowedExtensions: ['html', 'htm', 'php', 'asp', 'aspx', 'jsp'],
    disallowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'zip', 'rar', 'exe'],
    maxFileSize: 10 * 1024 * 1024, // 10MB
  },

  // 3. 深度控制
  depthControl: {
    global: 10,
    perDomain: {},
  },

  // 4. 性能与并发
  concurrency: {
    maxConcurrentRequests: 10,
    delayBetweenRequestsMs: 1000,
    domainSpecificDelay: {},
    requestTimeoutMs: 30000,
    maxRetries: 3,
    retryDelayMs: 5000,
  },

  // 5. 代理配置
  proxy: {
    enabled: false,
    proxies: [],
    strategy: ProxyStrategy.ROUND_ROBIN,
    healthCheck: {
      enabled: false,
      intervalMs: 60000,
      testUrl: 'https://httpbin.org/ip',
      timeoutMs: 10000,
    },
    failureThreshold: 3,
    recoveryTimeMs: 300000, // 5分钟
  },

  // 外部代理配置文件路径
  proxyJson: undefined,

  // 6. 解析器配置
  parsers: [
    {
      name: 'cheerio-parser',
      enabled: true,
      priority: 100,
      options: {
        selectors: {},
        extractLinks: true,
        linkSelector: 'a[href]',
        linkAttribute: 'href',
        cleanText: true,
      },
    },
    {
      name: 'ai-parser',
      enabled: false,
      priority: 200,
      options: {
        schema: {},
        model: 'gpt-3.5-turbo',
        maxTokens: 4000,
        temperature: 0.1,
        extractLinks: true,
      },
    },
  ],

  // 7. 存储配置
  storage: {
    type: StorageType.MONGODB,
    connectionString: 'mongodb://localhost:27017/nodex',
    collection: 'crawl_data',
    database: 'nodex',
    batchSize: 100,
    writeIntervalMs: 5000,
    options: {
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
    },
  },

  // 8. 队列配置
  queue: {
    redis: {
      host: 'localhost',
      port: 6379,
      db: 0,
      maxConnections: 10,
      connectTimeoutMs: 10000,
      commandTimeoutMs: 5000,
    },
    queuePrefix: 'nodex',
    defaultJobOptions: {
      delay: 0,
      priority: 0,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    },
    concurrency: 5,
  },

  // 9. AI 配置
  ai: {
    enabled: false,
    defaultProvider: 'openai',
    openai: {
      apiKey: '',
      baseUrl: 'https://api.openai.com/v1',
      model: 'gpt-3.5-turbo',
      maxTokens: 4000,
      temperature: 0.1,
    },
    gemini: {
      apiKey: '',
      model: 'gemini-pro',
      maxTokens: 4000,
      temperature: 0.1,
    },
    timeoutMs: 30000,
    maxRetries: 3,
  },

  // 10. 日志配置
  logging: {
    level: LogLevel.INFO,
    format: 'json',
    console: true,
    filePath: './logs/nodex.log',
    rotation: {
      maxSize: '100m',
      maxFiles: 10,
    },
  },

  // 11. 监控配置
  monitoring: {
    enabled: true,
    port: 3001,
    statsIntervalMs: 10000,
    healthCheck: true,
    metrics: true,
  },

  // 12. 其他配置
  userAgent:
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  respectRobotsTxt: true,
  requestTimeoutMs: 30000,
};
