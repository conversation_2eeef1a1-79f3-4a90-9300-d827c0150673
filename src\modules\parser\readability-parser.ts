/**
 * Mozilla Readability解析器实现
 */

import { Readability } from '@mozilla/readability';
import { JSDOM } from 'jsdom';
import {
  IParser,
  ParserInput,
  ParserOutput,
  ReadabilityParserOptions,
} from '@/interfaces';

/**
 * Readability解析器实现类
 */
export class ReadabilityParser implements IParser {
  public readonly name = 'readability-parser';
  public readonly version = '1.0.0';
  public readonly description = 'HTML parser using Mozilla Readability algorithm';

  private options: ReadabilityParserOptions;

  constructor(options: ReadabilityParserOptions = {}) {
    this.options = {
      extractLinks: true,
      includeRawHtml: true,
      minContentLength: 140,
      minScore: 20,
      ...options,
    };
  }

  /**
   * 判断此解析器是否适用于该页面
   */
  public canParse(input: ParserInput): boolean {
    // 检查内容类型
    const contentType = input.headers?.['content-type'] || '';
    const isHtml = contentType.includes('text/html') || 
                   contentType.includes('application/xhtml+xml');

    // 检查URL扩展名
    const url = new URL(input.url);
    const pathname = url.pathname.toLowerCase();
    const hasHtmlExtension = pathname.endsWith('.html') || 
                            pathname.endsWith('.htm') || 
                            pathname === '/' ||
                            !pathname.includes('.');

    // 检查内容长度，过短的内容可能不适合Readability
    const hasEnoughContent: boolean = Boolean(input.content && input.content.length > 100);

    return (isHtml || hasHtmlExtension) && hasEnoughContent;
  }

  /**
   * 执行解析
   */
  public async parse(input: ParserInput): Promise<ParserOutput> {
    const startTime = Date.now();
    
    try {
      // 创建虚拟DOM
      const dom = new JSDOM(input.content, {
        url: input.url,
        contentType: 'text/html',
        includeNodeLocations: false,
        storageQuota: 10000000
      });

      // 使用Readability解析主要内容
      const reader = new Readability(dom.window.document, {
        debug: false,
        maxElemsToParse: this.options.maxElemsToParse || 0,
        nbTopCandidates: this.options.nbTopCandidates || 5,
        charThreshold: this.options.minContentLength || 140,
        classesToPreserve: this.options.classesToPreserve || [],
        keepClasses: Boolean(this.options.keepClasses),
        serializer: (node: Node) => (node as Element).innerHTML || node.textContent || '',
        disableJSONLD: false,
        allowedVideoRegex: /https?:\/\/(www\.)?(youtube|vimeo)\.com/i
      });

      const article = reader.parse();
      
      if (!article) {
        return {
          extractedData: null,
          newUrls: [],
          error: {
            message: 'Readability failed to parse article content',
            code: 'READABILITY_PARSE_FAILED',
            details: 'The page may not contain enough readable content'
          },
        };
      }

      // 提取基础数据
      const extractedData: Record<string, any> = {
        title: article.title || this.extractTitleFallback(dom.window.document),
        content: article.textContent || '',
        description: article.excerpt || this.extractDescriptionFallback(dom.window.document),
        byline: article.byline || '',
        siteName: article.siteName || this.extractSiteNameFallback(dom.window.document),
        publishedTime: this.extractPublishedTime(dom.window.document),
        readingTime: this.calculateReadingTime(article.textContent || ''),
        wordCount: this.countWords(article.textContent || ''),
        lang: article.lang || dom.window.document.documentElement.lang || 'en',
        dir: article.dir || dom.window.document.documentElement.dir || 'ltr',
      };

      // 如果启用了原始HTML，添加清理后的HTML内容
      if (this.options.includeRawHtml) {
        extractedData.rawHtml = input.content;
        extractedData.cleanHtml = article.content;
      }

      // 提取额外的元数据
      extractedData.metadata = this.extractMetadata(dom.window.document);

      // 提取图片信息
      extractedData.images = this.extractImages(dom.window.document, input.url);

      // 提取链接
      const newUrls = this.options.extractLinks ? 
        this.extractLinks(dom.window.document, input.url) : [];

      const parseTime = Date.now() - startTime;
      
      return {
        extractedData,
        newUrls,
        metadata: {
          parserName: this.name,
          parseTime,
          extractedFieldsCount: Object.keys(extractedData).length,
          discoveredUrlsCount: newUrls.length,
          readabilityScore: this.calculateReadabilityScore(article),
          hasMainContent: !!article.content,
          contentLength: article.textContent?.length || 0,
        } as any,
      };
      
    } catch (error) {
      return {
        extractedData: null,
        newUrls: [],
        error: {
          message: error instanceof Error ? error.message : 'Unknown parsing error',
          code: 'READABILITY_ERROR',
          details: error,
        },
      };
    }
  }

  /**
   * 提取标题的后备方案
   */
  private extractTitleFallback(document: Document): string {
    // 尝试多种方式提取标题
    const selectors = [
      'title',
      'h1',
      '[property="og:title"]',
      '[name="twitter:title"]',
      '.article-title',
      '.post-title',
      '.entry-title'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        const content = element.getAttribute('content') || element.textContent;
        if (content && content.trim()) {
          return content.trim();
        }
      }
    }

    return '';
  }

  /**
   * 提取描述的后备方案
   */
  private extractDescriptionFallback(document: Document): string {
    const selectors = [
      '[name="description"]',
      '[property="og:description"]',
      '[name="twitter:description"]',
      '.article-summary',
      '.post-excerpt',
      '.entry-summary'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        const content = element.getAttribute('content') || element.textContent;
        if (content && content.trim()) {
          return content.trim();
        }
      }
    }

    return '';
  }

  /**
   * 提取站点名称的后备方案
   */
  private extractSiteNameFallback(document: Document): string {
    const selectors = [
      '[property="og:site_name"]',
      '[name="application-name"]',
      '.site-name',
      '.brand'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        const content = element.getAttribute('content') || element.textContent;
        if (content && content.trim()) {
          return content.trim();
        }
      }
    }

    return '';
  }

  /**
   * 提取发布时间
   */
  private extractPublishedTime(document: Document): string | null {
    const selectors = [
      '[property="article:published_time"]',
      '[name="DC.date"]',
      '[name="date"]',
      'time[datetime]',
      '.published',
      '.post-date',
      '.entry-date'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        const datetime = element.getAttribute('datetime') || 
                        element.getAttribute('content') || 
                        element.textContent;
        if (datetime && datetime.trim()) {
          return datetime.trim();
        }
      }
    }

    return null;
  }

  /**
   * 计算阅读时间（分钟）
   */
  private calculateReadingTime(text: string): number {
    const wordsPerMinute = 200; // 平均阅读速度
    const wordCount = this.countWords(text);
    return Math.ceil(wordCount / wordsPerMinute);
  }

  /**
   * 计算字数
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * 提取元数据
   */
  private extractMetadata(document: Document): Record<string, any> {
    const metadata: Record<string, any> = {};

    // 提取所有meta标签
    const metaTags = document.querySelectorAll('meta');
    metaTags.forEach(meta => {
      const name = meta.getAttribute('name') || meta.getAttribute('property');
      const content = meta.getAttribute('content');
      if (name && content) {
        metadata[name] = content;
      }
    });

    // 提取JSON-LD结构化数据
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    const structuredData: any[] = [];
    jsonLdScripts.forEach(script => {
      try {
        const data = JSON.parse(script.textContent || '');
        structuredData.push(data);
      } catch (error) {
        // 忽略无效的JSON-LD
      }
    });

    if (structuredData.length > 0) {
      metadata.structuredData = structuredData;
    }

    return metadata;
  }

  /**
   * 提取图片信息
   */
  private extractImages(document: Document, baseUrl: string): any[] {
    const images: any[] = [];
    const imgElements = document.querySelectorAll('img');
    
    imgElements.forEach(img => {
      const src = img.getAttribute('src');
      if (src) {
        try {
          const absoluteUrl = new URL(src, baseUrl).toString();
          images.push({
            src: absoluteUrl,
            alt: img.getAttribute('alt') || '',
            title: img.getAttribute('title') || '',
            width: img.getAttribute('width') || null,
            height: img.getAttribute('height') || null,
          });
        } catch (error) {
          // 忽略无效的URL
        }
      }
    });

    return images;
  }

  /**
   * 提取链接（仅HTML页面）
   */
  private extractLinks(document: Document, baseUrl: string): string[] {
    const links: string[] = [];
    
    // 1. 提取 <a> 标签的链接
    const linkElements = document.querySelectorAll('a[href]');
    linkElements.forEach(link => {
      this.processHtmlLink(link, baseUrl, links);
    });
    
    // 2. 提取 <link> 标签的链接（仅canonical、alternate、next、prev等导航链接）
    const linkTags = document.querySelectorAll('link[href]');
    linkTags.forEach(link => {
      const rel = link.getAttribute('rel');
      if (rel && ['canonical', 'alternate', 'next', 'prev'].includes(rel)) {
        this.processHtmlLink(link, baseUrl, links);
      }
    });
    
    // 3. 提取 <area> 标签的链接（图片地图中的HTML链接）
    const areaElements = document.querySelectorAll('area[href]');
    areaElements.forEach(area => {
      this.processHtmlLink(area, baseUrl, links);
    });
    
    // 4. 提取表单action链接（如果指向HTML页面）
    const forms = document.querySelectorAll('form[action]');
    forms.forEach(form => {
      this.processHtmlLink(form, baseUrl, links);
    });
    
    // 5. 提取meta标签中的HTML页面链接
    this.extractMetaHtmlUrls(document, baseUrl, links);
    
    // 去重并过滤
    return [...new Set(links)].filter(link => this.isHtmlLink(link));
  }

  /**
   * 处理链接元素
   */
  private processLinkElement(element: Element, baseUrl: string, links: string[]): void {
    this.processHtmlLink(element, baseUrl, links);
  }

  /**
   * 处理HTML链接元素
   */
  private processHtmlLink(element: Element, baseUrl: string, links: string[]): void {
    try {
      const href = element.getAttribute('href') || element.getAttribute('action');
      if (href && href.trim()) {
        const absoluteUrl = new URL(href.trim(), baseUrl).toString();
        if (this.isHtmlLink(absoluteUrl)) {
          links.push(absoluteUrl);
        }
      }
    } catch (error) {
      // 忽略无效的URL
    }
  }

  /**
   * 提取meta标签中的HTML页面链接
   */
  private extractMetaHtmlUrls(document: Document, baseUrl: string, links: string[]): void {
    const metaSelectors = [
      'meta[property="og:url"]',
      'meta[name="twitter:url"]',
      'meta[http-equiv="refresh"]',
    ];
    
    metaSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        try {
          let url = element.getAttribute('content') || '';
          
          // 处理 refresh meta 标签
          if (element.getAttribute('http-equiv') === 'refresh') {
            const match = url.match(/url\s*=\s*(.+)/i);
            if (match && match[1]) {
              url = match[1].trim();
            }
          }
          
          if (url && url.trim()) {
            const absoluteUrl = new URL(url.trim(), baseUrl).toString();
            if (this.isHtmlLink(absoluteUrl)) {
              links.push(absoluteUrl);
            }
          }
        } catch (error) {
          // 忽略无效的URL
        }
      });
    });
  }

  /**
   * 验证链接是否为HTML页面
   */
  private isHtmlLink(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      
      // 只接受 HTTP 和 HTTPS 协议
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return false;
      }
      
      // 检查域名是否有效
      if (!parsedUrl.hostname || parsedUrl.hostname.length < 3) {
        return false;
      }
      
      const pathname = parsedUrl.pathname.toLowerCase();
      const fullUrl = url.toLowerCase();
      
      // 明确排除的资源文件扩展名
      const excludeExtensions = [
        // 样式和脚本
        '.css', '.js', '.jsx', '.ts', '.tsx', '.less', '.scss', '.sass', '.styl',
        // 图片
        '.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.bmp', '.tiff', '.ico',
        '.avif', '.jfif', '.pjpeg', '.pjp',
        // 视频
        '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp',
        '.ogv', '.f4v', '.asf', '.rm', '.rmvb',
        // 音频
        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', '.opus',
        // 文档
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.odt', '.ods',
        '.odp', '.rtf', '.txt', '.csv',
        // 压缩文件
        '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.tgz',
        // 执行文件
        '.exe', '.msi', '.dmg', '.pkg', '.deb', '.rpm', '.app',
        // 字体
        '.woff', '.woff2', '.ttf', '.eot', '.otf',
        // 其他资源
        '.xml', '.json', '.rss', '.atom', '.feed', '.sitemap'
      ];
      
      // 检查文件扩展名
      for (const ext of excludeExtensions) {
        if (pathname.endsWith(ext)) {
          return false;
        }
      }
      
      // 排除特定协议
      const excludeProtocols = ['mailto:', 'tel:', 'javascript:', 'ftp:', 'file:'];
      for (const protocol of excludeProtocols) {
        if (fullUrl.startsWith(protocol)) {
          return false;
        }
      }
      
      // 排除锚点链接（但保留查询参数链接）
      if (parsedUrl.hash && !parsedUrl.search && parsedUrl.pathname === '/') {
        return false;
      }
      
      // 排除常见的垃圾链接模式
      const excludePatterns = [
        /^javascript:/i,
        /^#/,
        /^\s*$/,
        /^void\(0\)/i,
        /^about:blank/i,
        /^data:/i,
        /^blob:/i,
        /logout|signout|sign-out/i,
        /unsubscribe/i,
        /privacy-policy|terms-of-service|cookie-policy/i,
        /\/api\//i,
        /\/ajax\//i,
        /\/download\//i,
        /\/uploads?\//i,
        /\/assets?\//i,
        /\/static\//i,
        /\/public\//i,
        /\/resources?\//i,
        /\/media\//i,
        /\/images?\//i,
        /\/css\//i,
        /\/js\//i,
        /\/fonts?\//i,
        /\/files?\//i,
      ];
      
      for (const pattern of excludePatterns) {
        if (pattern.test(fullUrl) || pattern.test(pathname)) {
          return false;
        }
      }
      
      // 检查域名是否为IP地址（通常不是有效的内容页面）
      const ipPattern = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
      if (ipPattern.test(parsedUrl.hostname)) {
        return false;
      }
      
      // 检查路径长度（过长的路径可能是垃圾链接）
      if (parsedUrl.pathname.length > 200) {
        return false;
      }
      
      // 检查查询参数长度（过长的查询参数可能是垃圾链接）
      if (parsedUrl.search && parsedUrl.search.length > 500) {
        return false;
      }
      
      return true;
    } catch {
      return false;
    }
  }


  /**
   * 计算可读性评分
   */
  private calculateReadabilityScore(article: any): number {
    // 基于多个因素计算可读性评分
    let score = 0;
    
    if (article.title) score += 20;
    if (article.byline) score += 10;
    if (article.excerpt) score += 15;
    
    const contentLength = article.textContent?.length || 0;
    if (contentLength > 500) score += 20;
    if (contentLength > 1000) score += 10;
    if (contentLength > 2000) score += 10;
    
    // 限制最大分数
    return Math.min(score, 100);
  }

  /**
   * 更新配置
   */
  public setOptions(options: Partial<ReadabilityParserOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * 获取当前配置
   */
  public getOptions(): ReadabilityParserOptions {
    return { ...this.options };
  }
}