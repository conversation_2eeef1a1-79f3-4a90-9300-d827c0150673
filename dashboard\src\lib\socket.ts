import { io, Socket } from 'socket.io-client';
import type { SocketEvents } from '@/types/monitoring';
import { SocketErrorHandler } from '@/lib/errors';

/**
 * 简化的 Socket.IO 管理器
 * 只负责基础的连接管理和事件处理
 */
class SocketManager {
  private socket: Socket | null = null;
  private connectionCount = 0;
  private errorHandler = new SocketErrorHandler();
  private isConnecting = false;
  private connectionPromise: Promise<Socket> | null = null;
  public connected = false
  
  /**
   * 连接到 Socket.IO 服务器 - 确保单例
   */
  connect(): Socket {
    // 如果已经有连接且状态正常，直接返回
    if (this.socket && this.socket.connected) {
      return this.socket;
    }

    // 如果正在连接中，等待连接完成
    if (this.isConnecting && this.socket) {
      return this.socket;
    }

    // 如果有旧连接但已断开，先清理
    if (this.socket && !this.socket.connected) {
      this.socket.removeAllListeners();
      this.socket = null;
    }

    this.isConnecting = true;
    const socketUrl =
      import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';

    this.connectionCount++;
    console.log(
      `🔌 创建新的 Socket.IO 连接 #${this.connectionCount}:`,
      socketUrl
    );

    this.socket = io(socketUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      forceNew: false, // 重要：不强制创建新连接
    });
    
    this.setupEventHandlers();
    
    // 连接完成后重置标志
    this.socket.on('connect', () => {
      this.isConnecting = false;
      this.connected = true;
    });
    
    return this.socket;
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.connectionCount--;
      this.connected = false
      this.isConnecting = false;
    }
  }

  /**
   * 获取当前 socket 实例
   */
  getSocket(): Socket | null {
    return this.socket;
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * 设置基础事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('✅ Socket.IO 连接成功');
      // 连接成功时重置错误计数
      this.errorHandler.resetErrorCount('connection');
    });

    this.socket.on('disconnect', reason => {
      console.log('❌ Socket.IO 连接断开:', reason);
      this.connected = false;

      // 处理断开连接错误
      const errorResult = this.errorHandler.handleError(
        { type: 'disconnect', reason },
        'connection'
      );

      if (errorResult.shouldHandle && errorResult.strategy.shouldNotifyUser) {
        console.warn('Socket.IO连接断开，可能影响实时数据更新');
      }
    });

    this.socket.on('connect_error', error => {
      console.error('🔥 Socket.IO 连接错误:', error);

      // 处理连接错误
      const errorResult = this.errorHandler.handleError(error, 'connection');

      if (errorResult.shouldHandle) {
        const { parsedError, strategy } = errorResult;

        console.error('Socket.IO连接错误详情:', {
          type: parsedError.type,
          message: parsedError.message,
          severity: parsedError.severity,
          strategy,
        });

        if (strategy.shouldNotifyUser) {
          // 这里可以触发全局错误通知
          console.warn('Socket.IO连接出现问题，正在尝试重新连接...');
        }
      }
    });

    // 添加通用错误处理
    this.socket.on('error', error => {
      const errorResult = this.errorHandler.handleError(error, 'general');

      if (errorResult.shouldHandle) {
        console.error('Socket.IO通用错误:', errorResult.parsedError);
      }
    });
  }

  /**
   * 监听事件
   */
  on<K extends keyof SocketEvents>(event: K, handler: SocketEvents[K]): void {
    this.socket?.on(event as string, handler);
  }

  /**
   * 移除事件监听器
   */
  off<K extends keyof SocketEvents>(event: K, handler?: SocketEvents[K]): void {
    this.socket?.off(event as string, handler);
  }

  /**
   * 发送事件
   */
  emit(event: string, data?: any): void {
    this.socket?.emit(event, data);
  }

  /**
   * 获取连接统计信息
   */
  getConnectionStats() {
    return {
      totalConnections: this.connectionCount,
      isConnected: this.isConnected(),
      socketId: this.socket?.id || null,
      errorStats: this.errorHandler.getErrorStats(),
    };
  }

  /**
   * 重置错误统计
   */
  resetErrorStats(): void {
    this.errorHandler.resetErrorCount('connection');
    this.errorHandler.resetErrorCount('general');
  }
}

// 创建单例实例
const socketManager = new SocketManager();

export default socketManager;
