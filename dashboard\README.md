# Nodex 监控面板前端

基于 Next.js 14 的现代化监控面板，为 Nodex 高性能智能爬虫系统提供实时监控界面。

## 技术栈

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **图表**: Chart.js + react-chartjs-2
- **实时通信**: Socket.IO Client
- **图标**: Lucide React
- **代码规范**: ESLint + Prettier

## 功能特性

- 🔄 **实时监控**: 通过 Socket.IO 实现实时数据更新
- 📊 **数据可视化**: 丰富的图表和指标展示
- 🎛️ **系统控制**: 支持系统暂停、恢复、重启等操作
- 📱 **响应式设计**: 适配各种屏幕尺寸
- 🎨 **现代化UI**: 基于 Tailwind CSS 的美观界面
- ⚡ **高性能**: 优化的组件和数据更新策略

## 快速开始

### 环境要求

- Node.js 18.0+
- npm 9.0+

### 安装依赖

```bash
npm install
```

### 环境配置

复制环境变量配置文件：

```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件，配置后端服务地址：

```env
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:3002 查看监控面板。

### 生产构建

```bash
npm run build
npm start
```

## 项目结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   ├── layout/           # 布局组件
│   └── monitoring/       # 监控相关组件
├── hooks/                # React Hooks
├── lib/                  # 工具库
├── types/                # TypeScript 类型定义
└── utils/                # 工具函数
```

## 主要组件

### 监控组件

- **SystemOverview**: 系统状态概览
- **QueueMonitor**: 队列状态监控
- **PerformanceCharts**: 性能图表
- **ModuleStatus**: 模块状态监控
- **SystemControl**: 系统控制面板
- **SessionManager**: 会话管理

### UI 组件

- **Card**: 卡片容器
- **Button**: 按钮组件
- **Badge**: 状态徽章
- **LoadingSpinner**: 加载指示器

## API 集成

### Socket.IO 事件

- `status-update`: 系统状态更新
- `queues-update`: 队列状态更新
- `modules-update`: 模块状态更新
- `sessions-update`: 会话状态更新
- `system-event`: 系统事件
- `session-event`: 会话事件

### REST API

- `GET /api/status`: 获取系统状态
- `GET /api/queues`: 获取队列状态
- `GET /api/modules`: 获取模块状态
- `GET /api/sessions`: 获取会话列表
- `POST /api/control/*`: 系统控制操作

## 开发指南

### 代码规范

项目使用 ESLint 和 Prettier 进行代码规范检查：

```bash
# 检查代码规范
npm run lint

# 自动修复
npm run lint:fix

# 格式化代码
npm run format
```

### 类型检查

```bash
npm run type-check
```

### 添加新组件

1. 在 `src/components/` 下创建组件文件
2. 使用 TypeScript 定义组件接口
3. 遵循现有的命名和结构约定
4. 添加适当的样式和响应式设计

### 状态管理

项目使用 React Hooks 进行状态管理：

- `useMonitoring`: 监控数据管理
- `useSocket`: Socket.IO 连接管理
- `useSystemControl`: 系统控制操作

## 部署

### Docker 部署

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3002

CMD ["npm", "start"]
```

### 环境变量

生产环境需要配置以下环境变量：

- `NEXT_PUBLIC_API_URL`: 后端 API 地址
- `NEXT_PUBLIC_SOCKET_URL`: Socket.IO 服务器地址
- `NODE_ENV`: 环境模式

## 故障排除

### 常见问题

1. **连接失败**: 检查后端服务是否启动，确认 API 地址配置正确
2. **实时数据不更新**: 检查 Socket.IO 连接状态和网络连接
3. **图表不显示**: 确认 Chart.js 依赖已正确安装
4. **样式问题**: 检查 Tailwind CSS 配置和构建过程

### 调试

开启开发者工具查看控制台输出，检查网络请求和 Socket.IO 连接状态。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 许可证

MIT License
