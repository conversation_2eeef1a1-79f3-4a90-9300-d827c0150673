/**
 * 内存存储实现
 */

import {
  IStorage,
  StorageItem,
  QueryOptions,
  QueryResult,
  BulkOperationResult,
  StorageStats,
} from '@/interfaces';
import { StorageType } from '@/types/common';
import { info } from '@/utils';

/**
 * 内存存储类
 */
export class MemoryStorage implements IStorage {
  public readonly type = StorageType.MEMORY;
  public readonly name = 'memory-storage';

  private items: Map<string, StorageItem> = new Map();
  private urlIndex: Map<string, string> = new Map(); // URL -> ID 映射
  private initialized = false;

  /**
   * 初始化存储
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    info('Initializing memory storage...');
    this.items.clear();
    this.urlIndex.clear();
    this.initialized = true;
    info('Memory storage initialized successfully');
  }

  /**
   * 关闭存储连接
   */
  public async close(): Promise<void> {
    info('Closing memory storage...');
    this.items.clear();
    this.urlIndex.clear();
    this.initialized = false;
  }

  /**
   * 保存单个项目
   */
  public async save(item: StorageItem): Promise<string> {
    const id = item.id || this.generateId();
    const itemWithId = { ...item, id };
    
    this.items.set(id, itemWithId);
    this.urlIndex.set(item.url, id);
    
    return id;
  }

  /**
   * 批量保存项目
   */
  public async saveBatch(items: StorageItem[]): Promise<BulkOperationResult> {
    const startTime = Date.now();
    let successCount = 0;
    const failures: Array<{ item: StorageItem; error: string }> = [];

    for (const item of items) {
      try {
        await this.save(item);
        successCount++;
      } catch (error) {
        failures.push({
          item,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    return {
      successCount,
      failureCount: failures.length,
      failures,
      operationTime: Date.now() - startTime,
    };
  }

  /**
   * 根据ID获取项目
   */
  public async getById(id: string): Promise<StorageItem | null> {
    return this.items.get(id) || null;
  }

  /**
   * 根据URL获取项目
   */
  public async getByUrl(url: string): Promise<StorageItem | null> {
    const id = this.urlIndex.get(url);
    if (!id) {
      return null;
    }
    return this.getById(id);
  }

  /**
   * 查询项目
   */
  public async query(options: QueryOptions): Promise<QueryResult> {
    const startTime = Date.now();
    let results = Array.from(this.items.values());

    // 应用查询条件
    if (options.conditions) {
      results = results.filter(item => {
        return options.conditions!.every(condition => {
          const itemValue = this.getNestedValue(item, condition.field);
          return this.evaluateCondition(itemValue, condition.operator, condition.value);
        });
      });
    }

    // 应用排序
    if (options.sort) {
      results.sort((a, b) => {
        for (const sortField of options.sort!) {
          const aValue = this.getNestedValue(a, sortField.field);
          const bValue = this.getNestedValue(b, sortField.field);

          let comparison = 0;
          if (aValue < bValue) comparison = -1;
          else if (aValue > bValue) comparison = 1;

          if (comparison !== 0) {
            return sortField.direction === 'desc' ? -comparison : comparison;
          }
        }
        return 0;
      });
    }

    const total = results.length;

    // 应用分页
    const skip = options.skip || 0;
    const limit = options.limit || total;
    const paginatedResults = results.slice(skip, skip + limit);

    return {
      data: paginatedResults,
      total,
      queryTime: Date.now() - startTime,
    };
  }

  /**
   * 更新项目
   */
  public async update(id: string, updates: Partial<StorageItem>): Promise<boolean> {
    const existingItem = this.items.get(id);
    if (!existingItem) {
      return false;
    }

    const updatedItem = { ...existingItem, ...updates, id };
    this.items.set(id, updatedItem);

    // 更新URL索引
    if (updates.url && updates.url !== existingItem.url) {
      this.urlIndex.delete(existingItem.url);
      this.urlIndex.set(updates.url, id);
    }

    return true;
  }

  /**
   * 删除项目
   */
  public async delete(id: string): Promise<boolean> {
    const item = this.items.get(id);
    if (!item) {
      return false;
    }

    this.items.delete(id);
    this.urlIndex.delete(item.url);
    return true;
  }

  /**
   * 批量删除项目
   */
  public async deleteBatch(ids: string[]): Promise<BulkOperationResult> {
    const startTime = Date.now();
    let successCount = 0;
    const failures: Array<{ item: StorageItem; error: string }> = [];

    for (const id of ids) {
      try {
        const success = await this.delete(id);
        if (success) {
          successCount++;
        } else {
          const item = { id } as StorageItem;
          failures.push({ item, error: 'Item not found' });
        }
      } catch (error) {
        const item = { id } as StorageItem;
        failures.push({
          item,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    return {
      successCount,
      failureCount: failures.length,
      failures,
      operationTime: Date.now() - startTime,
    };
  }

  /**
   * 检查项目是否存在
   */
  public async exists(url: string): Promise<boolean> {
    return this.urlIndex.has(url);
  }

  /**
   * 获取存储统计信息
   */
  public async getStats(): Promise<StorageStats> {
    const items = Array.from(this.items.values());
    const totalRecords = items.length;
    const totalSize = JSON.stringify(items).length;

    return {
      totalRecords,
      storageSize: totalSize,
      averageDocumentSize: totalRecords > 0 ? totalSize / totalRecords : 0,
      lastUpdated: new Date(),
    };
  }

  /**
   * 创建索引（内存存储中无需实际创建索引）
   */
  public async createIndex(field: string, options?: { unique?: boolean; sparse?: boolean }): Promise<void> {
    // 内存存储中无需实际创建索引
    info(`Index created for field: ${field}`);
  }

  /**
   * 清空存储
   */
  public async clear(): Promise<void> {
    this.items.clear();
    this.urlIndex.clear();
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<boolean> {
    return this.initialized;
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取嵌套属性值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 评估查询条件
   */
  private evaluateCondition(itemValue: any, operator: string, conditionValue: any): boolean {
    switch (operator) {
      case 'eq':
        return itemValue === conditionValue;
      case 'ne':
        return itemValue !== conditionValue;
      case 'gt':
        return itemValue > conditionValue;
      case 'gte':
        return itemValue >= conditionValue;
      case 'lt':
        return itemValue < conditionValue;
      case 'lte':
        return itemValue <= conditionValue;
      case 'in':
        return Array.isArray(conditionValue) && conditionValue.includes(itemValue);
      case 'nin':
        return Array.isArray(conditionValue) && !conditionValue.includes(itemValue);
      case 'regex':
        if (typeof itemValue === 'string' && conditionValue instanceof RegExp) {
          return conditionValue.test(itemValue);
        }
        return false;
      case 'exists':
        return conditionValue ? itemValue !== undefined && itemValue !== null : itemValue === undefined || itemValue === null;
      default:
        return false;
    }
  }
}
