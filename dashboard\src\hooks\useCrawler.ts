import { useState, useCallback, useEffect } from 'react';
import apiClient from '@/lib/api';
import { Task, CrawlerStats, CreateTaskRequest, TaskOperationResult } from '@/types/task';
import { useSocket } from './useSocket';

export const useCrawler = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [stats, setStats] = useState<CrawlerStats>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // WebSocket 事件处理 - 使用useSocket处理任务更新
  useSocket({
    task_created: (event) => {
      if (event.task) {
        setTasks(prevTasks => [...prevTasks, event.task as Task]);
        refreshStats();
      }
    },
    task_status_changed: (event) => {
      setTasks(prevTasks => 
        prevTasks.map(task => 
          task.id === event.taskId 
            ? { ...task, status: event.task?.status || task.status }
            : task
        )
      );
      refreshStats();
    },
    task_stats_updated: (event) => {
      setTasks(prevTasks => 
        prevTasks.map(task => 
          task.id === event.taskId 
            ? { ...task, stats: { ...task.stats, ...event.stats } }
            : task
        )
      );
    },
    task_deleted: (event) => {
      setTasks(prevTasks => prevTasks.filter(task => task.id !== event.taskId));
      refreshStats();
    },
    task_completed: () => {
      refreshStats();
    },
    task_failed: () => {
      refreshStats();
    }
  });

  const refreshStats = useCallback(async () => {
    try {
      const statsResponse = await apiClient.getCrawlerStats();
      setStats(statsResponse);
    } catch (err) {
      console.error('Failed to refresh stats:', err);
    }
  }, []);

  const refreshData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 并行获取任务列表和统计数据，提高加载速度
      const [tasksResponse, statsResponse] = await Promise.all([
        apiClient.getTasks(),
        apiClient.getCrawlerStats()
      ]);
      
      setTasks(tasksResponse);
      setStats(statsResponse);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取数据失败';
      setError(errorMessage);
      console.error('Failed to refresh crawler data:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const createTask = useCallback(async (taskData: CreateTaskRequest) => {
    try {
      const response = await apiClient.createTask(taskData);
      if (response.success) {
        // 任务创建成功，等待WebSocket事件更新UI，不需要手动刷新
        console.log('Task created successfully:', response.taskId);
      } else {
        throw new Error(response.error || 'Failed to create task');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建任务失败';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const startTask = useCallback(async (taskId: string) => {
    try {
      const result = await apiClient.startTask(taskId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to start task');
      }
      // 不需要手动刷新，WebSocket会处理状态更新
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '启动任务失败';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const pauseTask = useCallback(async (taskId: string) => {
    try {
      const result = await apiClient.pauseTask(taskId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to pause task');
      }
      // 不需要手动刷新，WebSocket会处理状态更新
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '暂停任务失败';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const stopTask = useCallback(async (taskId: string) => {
    try {
      const result = await apiClient.stopTask(taskId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to stop task');
      }
      // 不需要手动刷新，WebSocket会处理状态更新
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '停止任务失败';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const deleteTask = useCallback(async (taskId: string) => {
    try {
      const result = await apiClient.deleteTask(taskId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete task');
      }
      // 不需要手动刷新，WebSocket会处理删除事件
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除任务失败';
      setError(errorMessage);
      throw err;
    }
  }, []);

  return {
    tasks,
    stats,
    loading,
    error,
    refreshData,
    createTask,
    startTask,
    pauseTask,
    stopTask,
    deleteTask,
  };
};