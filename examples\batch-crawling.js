/**
 * Nodex API 批量爬取示例
 * 
 * 演示如何创建和管理多个爬取任务
 * 
 * 运行方法: node examples/batch-crawling.js
 */

const API_BASE = 'http://localhost:3000';

async function main() {
  console.log('🔄 Nodex API 批量爬取示例');
  console.log('=========================');

  try {
    // 1. 创建多个任务
    console.log('\n1. 创建多个爬取任务...');
    
    const taskConfigs = [
      {
        name: '任务1 - HTML页面',
        config: {
          startUrls: ['https://httpbin.org/html'],
          maxDepth: 1,
          concurrency: 2,
          parser: { type: 'cheerio', selectors: { title: 'title' } }
        }
      },
      {
        name: '任务2 - JSON数据',
        config: {
          startUrls: ['https://httpbin.org/json'],
          maxDepth: 1,
          concurrency: 2,
          parser: { type: 'json' }
        }
      },
      {
        name: '任务3 - XML数据',
        config: {
          startUrls: ['https://httpbin.org/xml'],
          maxDepth: 1,
          concurrency: 2,
          parser: { type: 'xml' }
        }
      }
    ];

    const taskIds = [];
    
    for (let i = 0; i < taskConfigs.length; i++) {
      const response = await fetch(`${API_BASE}/api/v1/tasks`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(taskConfigs[i])
      });
      
      const result = await response.json();
      const taskId = result.data.id;
      taskIds.push(taskId);
      
      console.log(`任务 ${i + 1} 已创建，ID: ${taskId}`);
    }

    // 2. 批量启动任务
    console.log('\n2. 批量启动任务...');
    
    for (let i = 0; i < taskIds.length; i++) {
      await fetch(`${API_BASE}/api/v1/tasks/${taskIds[i]}/start`, {
        method: 'POST'
      });
      console.log(`任务 ${i + 1} 已启动`);
    }

    // 3. 监控所有任务状态
    console.log('\n3. 监控任务进度...');
    
    let allCompleted = false;
    let attempts = 0;
    const maxAttempts = 30;

    while (!allCompleted && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒
      
      console.log(`\n--- 状态检查 ${attempts + 1} ---`);
      
      const statuses = [];
      for (let i = 0; i < taskIds.length; i++) {
        const response = await fetch(`${API_BASE}/api/v1/tasks/${taskIds[i]}`);
        const result = await response.json();
        const status = result.data.status;
        statuses.push(status);
        
        console.log(`任务 ${i + 1}: ${status}`);
      }
      
      // 检查是否所有任务都完成
      allCompleted = statuses.every(status => 
        status === 'completed' || status === 'failed'
      );
      
      attempts++;
    }

    // 4. 获取所有任务的结果
    console.log('\n4. 获取所有任务结果...');
    
    for (let i = 0; i < taskIds.length; i++) {
      console.log(`\n--- 任务 ${i + 1} 结果 ---`);
      
      try {
        const response = await fetch(`${API_BASE}/api/v1/results/${taskIds[i]}?limit=3`);
        const results = await response.json();
        
        if (results.data && results.data.length > 0) {
          console.log(`获取到 ${results.data.length} 条结果:`);
          results.data.forEach((item, index) => {
            console.log(`  ${index + 1}. URL: ${item.url}`);
            console.log(`     数据: ${JSON.stringify(item.data).substring(0, 100)}...`);
          });
        } else {
          console.log('暂无结果数据');
        }
      } catch (error) {
        console.log(`获取结果失败: ${error.message}`);
      }
    }

    // 5. 获取任务列表
    console.log('\n5. 获取任务列表...');
    const listResponse = await fetch(`${API_BASE}/api/v1/tasks?limit=10`);
    const taskList = await listResponse.json();
    
    console.log(`共有 ${taskList.data.length} 个任务:`);
    taskList.data.forEach((task, index) => {
      console.log(`  ${index + 1}. ${task.name} - ${task.status}`);
    });

    console.log('\n✅ 批量爬取示例完成！');

  } catch (error) {
    console.error('❌ 发生错误:', error.message);
  }
}

// 运行示例
if (require.main === module) {
  main();
}
