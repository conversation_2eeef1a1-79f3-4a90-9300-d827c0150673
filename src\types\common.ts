/**
 * 通用类型定义
 */

// 重新导出 shared-types 中的 TraversalStrategy
export { TraversalStrategy } from '@/shared-types';

// 重新导出 shared-types 中的 FetcherType
export { FetcherType } from '@/shared-types';

/**
 * 存储类型枚举
 */
export enum StorageType {
  MONGODB = 'mongodb',
  POSTGRESQL = 'postgresql',
  JSON = 'json',
  CONSOLE = 'console',
  MEMORY = 'memory',
  FILE = 'file',
}

/**
 * 代理策略枚举
 */
export enum ProxyStrategy {
  ROUND_ROBIN = 'round-robin',
  RANDOM = 'random',
  STICKY = 'sticky',
  WEIGHTED_ROUND_ROBIN = 'weighted-round-robin',
  LEAST_CONNECTIONS = 'least-connections',
  FASTEST_RESPONSE = 'fastest-response',
}

/**
 * 旧版任务状态枚举（保持兼容性）
 * @deprecated 使用 @/types/task 中的 TaskStatus
 */
export enum LegacyTaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  RETRYING = 'retrying',
}

/**
 * 日志级别枚举
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

/**
 * HTTP 方法枚举
 */
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
  HEAD = 'HEAD',
  OPTIONS = 'OPTIONS',
}

/**
 * 基础代理配置接口 (简化版)
 */
export interface BaseProxyConfig {
  host: string;
  port: number;
  auth?: {
    username: string;
    password: string;
  };
  protocol?: 'http' | 'https' | 'socks4' | 'socks5';
}

// 重新导出 shared-types 中的完整 ProxyConfig
export { ProxyConfig } from '@/shared-types';

/**
 * URL 元数据接口
 */
export interface UrlMetadata {
  url: string;
  depth: number;
  parentUrl?: string;
  domain: string;
  timestamp: Date;
  retryCount: number;
  priority: number;
  headers?: Record<string, string>;
  method?: HttpMethod;
  body?: string | Record<string, any>;
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  code: string;
  message: string;
  stack?: string;
  timestamp: Date;
  context?: Record<string, any>;
  details?: Record<string, any>;
}

/**
 * 统计信息接口
 */
export interface Statistics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  requestsPerSecond: number;
  bytesDownloaded: number;
  startTime: Date;
  endTime?: Date;
}

/**
 * 健康检查结果接口 (从 shared-types 重新导出)
 */
export type { HealthCheckResult } from '@/shared-types';
