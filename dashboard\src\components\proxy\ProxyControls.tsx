import React, { useState, useRef } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface ProxyControlsProps {
  selectedCount: number;
  totalCount: number;
  loading: boolean;
  testingAll?: boolean;
  onAddProxy: () => void;
  onRemoveSelected: () => void;
  onTestAll: () => void;
  onClearAll: () => void;
  onLoadFromFile: (file: File) => void;
  onRefresh: () => void;
}

export const ProxyControls: React.FC<ProxyControlsProps> = ({
  selectedCount,
  totalCount,
  loading,
  testingAll = false,
  onAddProxy,
  onRemoveSelected,
  onTestAll,
  onClearAll,
  onLoadFromFile,
  onRefresh,
}) => {
  const [showFileInput, setShowFileInput] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onLoadFromFile(file);
      setShowFileInput(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleFileButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">代理控制</h3>
        <div className="text-sm text-gray-500">
          {selectedCount > 0 ? `已选择 ${selectedCount} 个代理` : `共 ${totalCount} 个代理`}
        </div>
      </div>

      <div className="space-y-4">
        {/* 主要操作按钮 */}
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={onAddProxy}
            variant="primary"
            disabled={loading}
            className="flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            添加代理
          </Button>

          <Button
            onClick={handleFileButtonClick}
            variant="secondary"
            disabled={loading}
            className="flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
            </svg>
            从文件导入
          </Button>

          {/* 隐藏的文件输入 */}
          <input
            ref={fileInputRef}
            type="file"
            accept=".json,.txt"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
          />

          <Button
            onClick={onTestAll}
            variant="secondary"
            disabled={loading || totalCount === 0 || testingAll}
            loading={testingAll}
            className="flex items-center"
          >
            {testingAll ?null:<svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>}
            {testingAll ? '测试中...' : '测试所有代理'}
          </Button>

          <Button
            onClick={onRefresh}
            variant="secondary"
            disabled={loading}
            className="flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            刷新
          </Button>
        </div>

        {/* 批量操作 */}
        {selectedCount > 0 && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-blue-800">
                已选择 {selectedCount} 个代理
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={onRemoveSelected}
                  variant="danger"
                  size="sm"
                  disabled={loading}
                  className="flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  删除选中
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 危险操作 */}
        <div className="border-t pt-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">危险操作</h4>
              <p className="text-xs text-gray-500">这些操作不可撤销，请谨慎使用</p>
            </div>
            <Button
              onClick={onClearAll}
              variant="danger"
              size="sm"
              disabled={loading || totalCount === 0}
              className="flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              清空所有代理
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};
