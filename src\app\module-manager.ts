/**
 * 模块管理器
 *
 * 管理系统中各个模块的生命周期和依赖关系
 */

import { EventEmitter } from 'events';
import { getLogger } from '@/utils';

/**
 * 模块状态枚举
 */
export enum ModuleState {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error',
}

/**
 * 模块接口
 */
export interface IModule {
  name: string;
  version: string;
  dependencies?: string[];
  initialize(): Promise<void>;
  start(): Promise<void>;
  stop(): Promise<void>;
  getState(): ModuleState;
  getStats?(): any;
  healthCheck?(): Promise<boolean>;
}

/**
 * 模块元数据
 */
export interface ModuleMetadata {
  name: string;
  version: string;
  dependencies: string[];
  state: ModuleState;
  instance: IModule;
  startedAt?: Date;
  stoppedAt?: Date;
  errorCount: number;
  lastError?: Error;
}

/**
 * 模块管理器接口
 */
export interface IModuleManager {
  registerModule(module: IModule): void;
  unregisterModule(name: string): void;
  getModule<T extends IModule>(name: string): T | null;
  getAllModules(): ModuleMetadata[];
  startModule(name: string): Promise<void>;
  stopModule(name: string): Promise<void>;
  restartModule(name: string): Promise<void>;
  startAllModules(): Promise<void>;
  stopAllModules(): Promise<void>;
  getModuleState(name: string): ModuleState | null;
  isModuleRunning(name: string): boolean;
  healthCheck(): Promise<{ [moduleName: string]: boolean }>;
}

/**
 * 模块管理器实现
 */
export class ModuleManager extends EventEmitter implements IModuleManager {
  private modules: Map<string, ModuleMetadata> = new Map();
  private logger = getLogger('ModuleManager');

  constructor() {
    super();
    this.logger.info('模块管理器初始化');
  }

  /**
   * 注册模块
   */
  registerModule(module: IModule): void {
    if (this.modules.has(module.name)) {
      throw new Error(`Module ${module.name} is already registered`);
    }

    const metadata: ModuleMetadata = {
      name: module.name,
      version: module.version,
      dependencies: module.dependencies || [],
      state: ModuleState.IDLE,
      instance: module,
      errorCount: 0,
    };

    this.modules.set(module.name, metadata);
    this.logger.info('模块已注册', { name: module.name, version: module.version });
    this.emit('moduleRegistered', { module: metadata });
  }

  /**
   * 注销模块
   */
  unregisterModule(name: string): void {
    const metadata = this.modules.get(name);
    if (!metadata) {
      throw new Error(`Module ${name} not found`);
    }

    if (metadata.state === ModuleState.RUNNING) {
      throw new Error(`Cannot unregister running module ${name}. Stop it first.`);
    }

    this.modules.delete(name);
    this.logger.info('模块已注销', { name });
    this.emit('moduleUnregistered', { name });
  }

  /**
   * 获取模块
   */
  getModule<T extends IModule>(name: string): T | null {
    const metadata = this.modules.get(name);
    return metadata ? (metadata.instance as T) : null;
  }

  /**
   * 获取所有模块
   */
  getAllModules(): ModuleMetadata[] {
    return Array.from(this.modules.values());
  }

  /**
   * 启动模块
   */
  async startModule(name: string): Promise<void> {
    const metadata = this.modules.get(name);
    if (!metadata) {
      throw new Error(`Module ${name} not found`);
    }

    if (metadata.state === ModuleState.RUNNING) {
      this.logger.warn('模块已在运行', { name });
      return;
    }

    try {
      // 检查依赖
      await this.checkAndStartDependencies(metadata);

      // 初始化模块
      if (metadata.state === ModuleState.IDLE) {
        await this.initializeModule(metadata);
      }

      // 启动模块
      this.updateModuleState(metadata, ModuleState.STARTING);
      this.logger.info('正在启动模块', { name });

      await metadata.instance.start();

      this.updateModuleState(metadata, ModuleState.RUNNING);
      metadata.startedAt = new Date();

      this.logger.info('模块启动成功', { name });
      this.emit('moduleStarted', { module: metadata });
    } catch (error) {
      this.handleModuleError(metadata, error as Error);
      throw error;
    }
  }

  /**
   * 停止模块
   */
  async stopModule(name: string): Promise<void> {
    const metadata = this.modules.get(name);
    if (!metadata) {
      throw new Error(`Module ${name} not found`);
    }

    if (metadata.state !== ModuleState.RUNNING) {
      this.logger.warn('模块未在运行', { name, state: metadata.state });
      return;
    }

    try {
      this.updateModuleState(metadata, ModuleState.STOPPING);
      this.logger.info('正在停止模块', { name });

      await metadata.instance.stop();

      this.updateModuleState(metadata, ModuleState.STOPPED);
      metadata.stoppedAt = new Date();

      this.logger.info('模块停止成功', { name });
      this.emit('moduleStopped', { module: metadata });
    } catch (error) {
      this.handleModuleError(metadata, error as Error);
      throw error;
    }
  }

  /**
   * 重启模块
   */
  async restartModule(name: string): Promise<void> {
    await this.stopModule(name);
    await this.startModule(name);
  }

  /**
   * 启动所有模块
   */
  async startAllModules(): Promise<void> {
    const modules = Array.from(this.modules.values());

    // 按照依赖关系排序
    const sortedModules = this.topologicalSort(modules);

    this.logger.info('正在启动所有模块', { count: sortedModules.length });

    for (const metadata of sortedModules) {
      try {
        await this.startModule(metadata.name);
      } catch (error) {
        this.logger.error('启动模块失败', { name: metadata.name, error });
        // 继续启动其他模块
      }
    }
  }

  /**
   * 停止所有模块
   */
  async stopAllModules(): Promise<void> {
    const modules = Array.from(this.modules.values());

    // 按照依赖关系逆序排序
    const sortedModules = this.topologicalSort(modules).reverse();

    this.logger.info('正在停止所有模块', { count: sortedModules.length });

    for (const metadata of sortedModules) {
      try {
        await this.stopModule(metadata.name);
      } catch (error) {
        this.logger.error('停止模块失败', { name: metadata.name, error });
        // 继续停止其他模块
      }
    }
  }

  /**
   * 获取模块状态
   */
  getModuleState(name: string): ModuleState | null {
    const metadata = this.modules.get(name);
    return metadata ? metadata.state : null;
  }

  /**
   * 检查模块是否在运行
   */
  isModuleRunning(name: string): boolean {
    return this.getModuleState(name) === ModuleState.RUNNING;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{ [moduleName: string]: boolean }> {
    const results: { [moduleName: string]: boolean } = {};

    for (const metadata of this.modules.values()) {
      try {
        if (metadata.state === ModuleState.RUNNING && metadata.instance.healthCheck) {
          results[metadata.name] = await metadata.instance.healthCheck();
        } else {
          results[metadata.name] = metadata.state === ModuleState.RUNNING;
        }
      } catch (error) {
        this.logger.warn('健康检查失败', { name: metadata.name, error });
        results[metadata.name] = false;
      }
    }

    return results;
  }

  /**
   * 初始化模块
   */
  private async initializeModule(metadata: ModuleMetadata): Promise<void> {
    this.updateModuleState(metadata, ModuleState.INITIALIZING);
    this.logger.info('正在初始化模块', { name: metadata.name });

    await metadata.instance.initialize();

    this.updateModuleState(metadata, ModuleState.INITIALIZED);
    this.logger.info('模块初始化成功', { name: metadata.name });
    this.emit('moduleInitialized', { module: metadata });
  }

  /**
   * 检查并启动依赖模块
   */
  private async checkAndStartDependencies(metadata: ModuleMetadata): Promise<void> {
    for (const dependencyName of metadata.dependencies) {
      const dependency = this.modules.get(dependencyName);
      if (!dependency) {
        throw new Error(`Dependency ${dependencyName} not found for module ${metadata.name}`);
      }

      if (dependency.state !== ModuleState.RUNNING) {
        this.logger.info('启动依赖模块', { module: metadata.name, dependency: dependencyName });
        await this.startModule(dependencyName);
      }
    }
  }

  /**
   * 拓扑排序（处理依赖关系）
   */
  private topologicalSort(modules: ModuleMetadata[]): ModuleMetadata[] {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const result: ModuleMetadata[] = [];
    const moduleMap = new Map<string, ModuleMetadata>();

    modules.forEach(module => moduleMap.set(module.name, module));

    const visit = (moduleName: string): void => {
      if (visited.has(moduleName)) return;
      if (visiting.has(moduleName)) {
        throw new Error(`Circular dependency detected involving module ${moduleName}`);
      }

      const module = moduleMap.get(moduleName);
      if (!module) return;

      visiting.add(moduleName);

      // 先访问依赖
      module.dependencies.forEach(dep => visit(dep));

      visiting.delete(moduleName);
      visited.add(moduleName);
      result.push(module);
    };

    modules.forEach(module => visit(module.name));

    return result;
  }

  /**
   * 更新模块状态
   */
  private updateModuleState(metadata: ModuleMetadata, state: ModuleState): void {
    const oldState = metadata.state;
    metadata.state = state;

    this.emit('moduleStateChanged', {
      module: metadata,
      oldState,
      newState: state,
    });
  }

  /**
   * 处理模块错误
   */
  private handleModuleError(metadata: ModuleMetadata, error: Error): void {
    metadata.errorCount++;
    metadata.lastError = error;
    this.updateModuleState(metadata, ModuleState.ERROR);

    this.logger.error('模块错误', {
      name: metadata.name,
      error: error.message,
      errorCount: metadata.errorCount,
    });

    this.emit('moduleError', { module: metadata, error });
  }

  /**
   * 获取统计信息
   */
  getStats(): any {
    const modules = Array.from(this.modules.values());
    const stateCount = modules.reduce(
      (acc, module) => {
        acc[module.state] = (acc[module.state] || 0) + 1;
        return acc;
      },
      {} as { [state: string]: number }
    );

    return {
      totalModules: modules.length,
      stateDistribution: stateCount,
      runningModules: modules.filter(m => m.state === ModuleState.RUNNING).map(m => m.name),
      errorModules: modules
        .filter(m => m.state === ModuleState.ERROR)
        .map(m => ({
          name: m.name,
          errorCount: m.errorCount,
          lastError: m.lastError?.message,
        })),
    };
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    this.logger.info('正在清理模块管理器');

    await this.stopAllModules();

    this.modules.clear();
    this.removeAllListeners();

    this.logger.info('模块管理器已清理');
  }
}
