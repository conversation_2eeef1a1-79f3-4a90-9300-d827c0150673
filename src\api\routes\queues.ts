/**
 * 标准化队列控制 API v1 路由
 * 
 * 为前端提供标准化的队列控制接口
 */

import { Router } from 'express';
import { TaskManager } from '@/app/task-manager';
import { Server as SocketIOServer } from 'socket.io';
import { error } from '@/utils';

/**
 * 创建标准化队列控制路由
 */
export function createQueuesRoutes(
  taskManager: TaskManager,
  io: SocketIOServer
): Router {
  const router = Router();

  /**
   * GET /api/v1/queues/:queueName/stats - 获取队列统计信息
   */
  router.get('/:queueName/stats', async (req, res) => {
    try {
      const { queueName } = req.params;
      const stats = taskManager.getCrawlerStats();
      const tasks = taskManager.getAllTasks();

      // 根据队列名称获取相应统计
      const queueStats = {
        queueName,
        waiting: stats.queueSize || 0,
        active: tasks.filter(t => t.status === 'running').length,
        completed: stats.processedUrls || 0,
        failed: stats.failedUrls || 0,
        delayed: 0,
        paused: false,
        totalJobs: stats.totalUrls || 0,
        successRate: stats.totalUrls > 0 ? 
          ((stats.processedUrls - stats.failedUrls) / stats.totalUrls) * 100 : 0,
        health: stats.failedUrls > stats.processedUrls * 0.1 ? 'critical' : 'healthy',
        timestamp: new Date(),
      };

      // 推送更新到WebSocket客户端
      io.emit('queue-stats-updated', {
        queueName,
        stats: queueStats,
        timestamp: new Date(),
      });

      res.json({
        success: true,
        data: queueStats,
        message: `Queue ${queueName} stats retrieved successfully`,
      });
    } catch (err) {
      error('获取队列统计失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get queue stats',
      });
    }
  });

  /**
   * POST /api/v1/queues/:queueName/pause - 暂停队列
   */
  router.post('/:queueName/pause', async (req, res) => {
    try {
      const { queueName } = req.params;
      const tasks = taskManager.getAllTasks();
      const runningTasks = tasks.filter(t => t.status === 'running');

      // 暂停所有运行中的任务
      for (const task of runningTasks) {
        await taskManager.pauseTask(task.id);
      }

      // 推送队列暂停事件
      io.emit('queue-paused', {
        queueName,
        pausedTasks: runningTasks.length,
        timestamp: new Date(),
      });

      res.json({
        success: true,
        message: `Queue ${queueName} paused successfully`,
        pausedTasks: runningTasks.length,
      });
    } catch (err) {
      error('暂停队列失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to pause queue',
      });
    }
  });

  /**
   * POST /api/v1/queues/:queueName/resume - 恢复队列
   */
  router.post('/:queueName/resume', async (req, res) => {
    try {
      const { queueName } = req.params;
      const tasks = taskManager.getAllTasks();
      const pausedTasks = tasks.filter(t => t.status === 'paused');

      // 恢复所有暂停的任务
      for (const task of pausedTasks) {
        await taskManager.resumeTask(task.id);
      }

      // 推送队列恢复事件
      io.emit('queue-resumed', {
        queueName,
        resumedTasks: pausedTasks.length,
        timestamp: new Date(),
      });

      res.json({
        success: true,
        message: `Queue ${queueName} resumed successfully`,
        resumedTasks: pausedTasks.length,
      });
    } catch (err) {
      error('恢复队列失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to resume queue',
      });
    }
  });

  /**
   * POST /api/v1/queues/:queueName/clear - 清空队列
   */
  router.post('/:queueName/clear', async (req, res) => {
    try {
      const { queueName } = req.params;
      const tasks = taskManager.getAllTasks();
      const allTasks = tasks.filter(t => t.status !== 'completed');

      let clearedCount = 0;
      
      // 删除所有非完成状态的任务
      for (const task of allTasks) {
        try {
          await taskManager.deleteTask(task.id);
          clearedCount++;
        } catch (clearError) {
          error(`Failed to clear task ${task.id}:`, clearError);
        }
      }

      // 推送清理事件
      io.emit('queue-cleared', {
        queueName,
        clearedCount,
        timestamp: new Date(),
      });

      res.json({
        success: true,
        message: `Cleared ${clearedCount} tasks from queue ${queueName}`,
        clearedCount,
      });
    } catch (err) {
      error('清空队列失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to clear queue',
      });
    }
  });

  /**
   * POST /api/v1/queues/:queueName/clear/failed - 清理失败的任务
   */
  router.post('/:queueName/clear/failed', async (req, res) => {
    try {
      const { queueName } = req.params;
      const tasks = taskManager.getAllTasks();
      const failedTasks = tasks.filter(t => t.status === 'failed');

      let clearedCount = 0;
      
      // 删除失败的任务
      for (const task of failedTasks) {
        try {
          await taskManager.deleteTask(task.id);
          clearedCount++;
        } catch (clearError) {
          error(`Failed to clear task ${task.id}:`, clearError);
        }
      }

      // 推送清理事件
      io.emit('queue-failed-cleared', {
        queueName,
        clearedCount,
        timestamp: new Date(),
      });

      res.json({
        success: true,
        message: `Cleared ${clearedCount} failed tasks from queue ${queueName}`,
        clearedCount,
      });
    } catch (err) {
      error('清理失败任务失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to clear failed tasks',
      });
    }
  });

  /**
   * POST /api/v1/queues/:queueName/retry/failed - 重试失败的任务
   */
  router.post('/:queueName/retry/failed', async (req, res) => {
    try {
      const { queueName } = req.params;
      const tasks = taskManager.getAllTasks();
      const failedTasks = tasks.filter(t => t.status === 'failed');

      let retriedCount = 0;
      
      // 重试失败的任务
      for (const task of failedTasks) {
        try {
          await taskManager.startTask(task.id);
          retriedCount++;
        } catch (retryError) {
          error(`Failed to retry task ${task.id}:`, retryError);
        }
      }

      // 推送重试事件
      io.emit('queue-failed-retried', {
        queueName,
        retriedCount,
        timestamp: new Date(),
      });

      res.json({
        success: true,
        message: `Retried ${retriedCount} failed tasks in queue ${queueName}`,
        retriedCount,
      });
    } catch (err) {
      error('重试失败任务失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to retry failed tasks',
      });
    }
  });

  return router;
}