/**
 * 全局日志管理器
 * 统一管理整个应用的日志配置和实例
 */

import { Logger, LogLevel, LoggerConfig } from './logger';
import { NodexConfig } from '@/interfaces/config';

/**
 * 日志管理器类
 */
export class LoggerManager {
  private static instance: LoggerManager | null = null;
  private loggers: Map<string, Logger> = new Map();
  private globalConfig: LoggerConfig;

  private constructor() {
    // 默认配置
    this.globalConfig = {
      level: LogLevel.INFO,
      console: true,
      format: 'text',
      enableColors: process.env.NO_COLOR !== '1' && process.env.NODE_ENV !== 'test',
      enableTimestamp: true,
      enableSource: true,
    };
  }

  /**
   * 获取日志管理器单例
   */
  public static getInstance(): LoggerManager {
    if (!LoggerManager.instance) {
      LoggerManager.instance = new LoggerManager();
    }
    return LoggerManager.instance;
  }

  /**
   * 从 Nodex 配置初始化日志管理器
   */
  public initializeFromConfig(config: NodexConfig): void {
    if (!config.logging) {
      return;
    }

    // 转换配置格式
    const loggerConfig: LoggerConfig = {
      level: this.parseLogLevel(config.logging.level),
      console: config.logging.console ?? true,
      format: config.logging.format === 'json' ? 'json' : 'text',
      file: config.logging.filePath,
      maxFileSize: this.parseFileSize(config.logging.rotation?.maxSize),
      maxFiles: config.logging.rotation?.maxFiles,
      enableColors: process.env.NO_COLOR !== '1' && process.env.NODE_ENV !== 'test',
      enableTimestamp: true,
      enableSource: true,
    };

    this.globalConfig = loggerConfig;

    // 更新全局日志器
    const globalLogger = new Logger(loggerConfig);
    Logger.setGlobalLogger(globalLogger);

    // 清除现有的子日志器，让它们重新创建
    this.loggers.clear();
  }

  /**
   * 获取或创建指定模块的日志器
   */
  public getLogger(moduleName: string): Logger {
    if (!this.loggers.has(moduleName)) {
      const globalLogger = Logger.getGlobalLogger();
      const moduleLogger = globalLogger.createChild(moduleName);
      this.loggers.set(moduleName, moduleLogger);
    }
    return this.loggers.get(moduleName)!;
  }

  /**
   * 获取全局日志器
   */
  public getGlobalLogger(): Logger {
    return Logger.getGlobalLogger();
  }

  /**
   * 设置日志级别
   */
  public setLogLevel(level: LogLevel): void {
    this.globalConfig.level = level;

    // 更新全局日志器
    const globalLogger = new Logger(this.globalConfig);
    Logger.setGlobalLogger(globalLogger);

    // 清除现有的子日志器
    this.loggers.clear();
  }

  /**
   * 启用或禁用文件日志
   */
  public setFileLogging(enabled: boolean, filePath?: string): void {
    if (enabled && filePath) {
      this.globalConfig.file = filePath;
    } else {
      this.globalConfig.file = undefined;
    }

    // 更新全局日志器
    const globalLogger = new Logger(this.globalConfig);
    Logger.setGlobalLogger(globalLogger);

    // 清除现有的子日志器
    this.loggers.clear();
  }

  /**
   * 获取所有活动的日志器统计信息
   */
  public getStats(): {
    totalLoggers: number;
    globalConfig: LoggerConfig;
    moduleLoggers: string[];
  } {
    return {
      totalLoggers: this.loggers.size + 1, // +1 for global logger
      globalConfig: { ...this.globalConfig },
      moduleLoggers: Array.from(this.loggers.keys()),
    };
  }

  /**
   * 解析日志级别字符串
   */
  private parseLogLevel(level: string): LogLevel {
    const levelMap: Record<string, LogLevel> = {
      debug: LogLevel.DEBUG,
      info: LogLevel.INFO,
      warn: LogLevel.WARN,
      error: LogLevel.ERROR,
      DEBUG: LogLevel.DEBUG,
      INFO: LogLevel.INFO,
      WARN: LogLevel.WARN,
      ERROR: LogLevel.ERROR,
    };
    return levelMap[level] ?? LogLevel.INFO;
  }

  /**
   * 解析文件大小字符串
   */
  private parseFileSize(sizeStr?: string): number | undefined {
    if (!sizeStr) return undefined;

    const match = sizeStr.match(/^(\d+)([kmg]?)b?$/i);
    if (!match?.[1]) return undefined;

    const size = parseInt(match[1], 10);
    const unit = match[2]?.toLowerCase();

    switch (unit) {
      case 'k':
        return size * 1024;
      case 'm':
        return size * 1024 * 1024;
      case 'g':
        return size * 1024 * 1024 * 1024;
      default:
        return size;
    }
  }

  /**
   * 关闭所有日志器
   */
  public async close(): Promise<void> {
    const globalLogger = Logger.getGlobalLogger();
    await globalLogger.close();

    for (const logger of this.loggers.values()) {
      await logger.close();
    }

    this.loggers.clear();
  }
}

// 导出单例实例
export const loggerManager = LoggerManager.getInstance();

// 导出便捷方法
export const getLogger = (moduleName: string) => loggerManager.getLogger(moduleName);
export const getGlobalLogger = () => loggerManager.getGlobalLogger();
