/**
 * 主控调度器相关接口定义
 */

import { TraversalStrategy, Statistics, HealthCheckResult } from '@/types/common';
import { NodexConfig } from '@/interfaces/config';
import { CrawlTask, QueueStats } from '@/interfaces/task';
import { StorageStats } from '@/interfaces/storage';

/**
 * 系统状态枚举
 */
export enum SystemStatus {
  IDLE = 'idle',
  STARTING = 'starting',
  RUNNING = 'running',
  PAUSING = 'pausing',
  PAUSED = 'paused',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error',
}

/**
 * 模块状态接口
 */
export interface ModuleStatus {
  /** 模块名称 */
  name: string;
  /** 模块状态 */
  status: 'healthy' | 'unhealthy' | 'degraded' | 'unknown';
  /** 最后检查时间 */
  lastChecked: Date;
  /** 详细信息 */
  details?: Record<string, any>;
  /** 错误信息 */
  error?: string;
}

/**
 * 系统统计信息接口
 */
export interface SystemStats {
  /** 基础统计 */
  basic: Statistics;
  /** 队列统计 */
  queues: QueueStats[];
  /** 存储统计 */
  storage: StorageStats;
  /** 模块状态 */
  modules: ModuleStatus[];
  /** 系统资源使用 */
  resources: {
    /** CPU 使用率 (%) */
    cpuUsage: number;
    /** 内存使用 (字节) */
    memoryUsage: number;
    /** 磁盘使用 (字节) */
    diskUsage?: number;
    /** 网络流量 */
    networkTraffic?: {
      bytesReceived: number;
      bytesSent: number;
    };
  };
  /** 统计时间 */
  timestamp: Date;
}

/**
 * 爬取会话接口
 */
export interface CrawlSession {
  /** 会话ID */
  id: string;
  /** 会话名称 */
  name: string;
  /** 配置 */
  config: NodexConfig;
  /** 开始时间 */
  startTime: Date;
  /** 结束时间 */
  endTime?: Date;
  /** 会话状态 */
  status: SystemStatus;
  /** 统计信息 */
  stats: Statistics;
  /** 错误信息 */
  error?: string;
  /** 标签 */
  tags?: string[];
}

/**
 * 事件类型枚举
 */
export enum EventType {
  SYSTEM_STARTED = 'system_started',
  SYSTEM_STOPPED = 'system_stopped',
  SYSTEM_PAUSED = 'system_paused',
  SYSTEM_RESUMED = 'system_resumed',
  TASK_COMPLETED = 'task_completed',
  TASK_FAILED = 'task_failed',
  MODULE_STATUS_CHANGED = 'module_status_changed',
  STATS_UPDATED = 'stats_updated',
  ERROR_OCCURRED = 'error_occurred',
}

/**
 * 系统事件接口
 */
export interface SystemEvent {
  /** 事件类型 */
  type: EventType;
  /** 事件时间 */
  timestamp: Date;
  /** 事件数据 */
  data: any;
  /** 事件来源 */
  source: string;
  /** 事件ID */
  id: string;
}

/**
 * 事件监听器接口
 */
export interface IEventListener {
  /**
   * 处理事件
   * @param event 系统事件
   */
  handleEvent(event: SystemEvent): void | Promise<void>;

  /**
   * 获取监听的事件类型
   */
  getEventTypes(): EventType[];
}

/**
 * 主控调度器接口
 */
export interface IMasterScheduler {
  /**
   * 启动系统
   * @param config 配置
   */
  start(config: NodexConfig): Promise<void>;

  /**
   * 停止系统
   */
  stop(): Promise<void>;

  /**
   * 暂停系统
   */
  pause(): Promise<void>;

  /**
   * 恢复系统
   */
  resume(): Promise<void>;

  /**
   * 重启系统
   * @param config 新配置 (可选)
   */
  restart(config?: NodexConfig): Promise<void>;

  /**
   * 获取系统状态
   */
  getSystemStatus(): SystemStatus;

  /**
   * 获取系统统计信息
   */
  getSystemStats(): Promise<SystemStats>;

  /**
   * 获取模块状态
   */
  getModuleStatus(): Promise<ModuleStatus[]>;

  /**
   * 健康检查
   */
  healthCheck(): Promise<HealthCheckResult>;

  /**
   * 添加种子URL
   * @param urls 种子URL列表
   */
  addSeedUrls(urls: string[]): Promise<void>;

  /**
   * 更新配置
   * @param updates 配置更新
   */
  updateConfig(updates: Partial<NodexConfig>): Promise<void>;

  /**
   * 获取模块管理器
   */
  getModuleManager(): IModuleManager;

  /**
   * 添加事件监听器
   */
  on(event: string, listener: (...args: any[]) => void): this;

  /**
   * 移除事件监听器
   */
  off(event: string, listener: (...args: any[]) => void): this;

  /**
   * 获取当前配置
   */
  getCurrentConfig(): NodexConfig;

  /**
   * 创建爬取会话
   * @param name 会话名称
   * @param config 配置
   */
  createSession(name: string, config: NodexConfig): Promise<CrawlSession>;

  /**
   * 获取爬取会话
   * @param sessionId 会话ID
   */
  getSession(sessionId: string): Promise<CrawlSession | null>;

  /**
   * 获取所有会话
   */
  getAllSessions(): Promise<CrawlSession[]>;

  /**
   * 删除会话
   * @param sessionId 会话ID
   */
  deleteSession(sessionId: string): Promise<boolean>;

  /**
   * 注册事件监听器
   * @param listener 事件监听器
   */
  addEventListener(listener: IEventListener): void;

  /**
   * 移除事件监听器
   * @param listener 事件监听器
   */
  removeEventListener(listener: IEventListener): void;

  /**
   * 发送事件
   * @param event 系统事件
   */
  emitEvent(event: SystemEvent): void;
}

/**
 * 模块管理器接口
 */
export interface IModuleManager {
  /**
   * 注册模块
   * @param name 模块名称
   * @param module 模块实例
   */
  registerModule(name: string, module: IModule): void;

  /**
   * 注销模块
   * @param name 模块名称
   */
  unregisterModule(name: string): void;

  /**
   * 获取模块
   * @param name 模块名称
   */
  getModule(name: string): IModule | null;

  /**
   * 获取所有模块
   */
  getAllModules(): Map<string, IModule>;

  /**
   * 初始化所有模块
   * @param config 配置
   */
  initializeModules(config: NodexConfig): Promise<void>;

  /**
   * 启动所有模块
   */
  startModules(): Promise<void>;

  /**
   * 停止所有模块
   */
  stopModules(): Promise<void>;

  /**
   * 获取模块状态
   */
  getModuleStatus(): Promise<ModuleStatus[]>;

  /**
   * 检查模块健康状态
   * @param name 模块名称
   */
  checkModuleHealth(name: string): Promise<HealthCheckResult>;

  /**
   * 添加事件监听器
   */
  on(event: string, listener: (...args: any[]) => void): this;

  /**
   * 移除事件监听器
   */
  off(event: string, listener: (...args: any[]) => void): this;

  /**
   * 清理资源
   */
  cleanup(): Promise<void>;
}

/**
 * 模块接口
 */
export interface IModule {
  /** 模块名称 */
  readonly name: string;
  /** 模块版本 */
  readonly version: string;
  /** 模块描述 */
  readonly description?: string;

  /**
   * 初始化模块
   * @param config 配置
   */
  initialize(config: NodexConfig): Promise<void>;

  /**
   * 启动模块
   */
  start(): Promise<void>;

  /**
   * 停止模块
   */
  stop(): Promise<void>;

  /**
   * 健康检查
   */
  healthCheck(): Promise<HealthCheckResult>;

  /**
   * 获取模块状态
   */
  getStatus(): ModuleStatus;

  /**
   * 获取模块统计信息
   */
  getStats?(): Promise<Record<string, any>>;

  /**
   * 处理配置更新
   * @param updates 配置更新
   */
  handleConfigUpdate?(updates: Partial<NodexConfig>): Promise<void>;
}

/**
 * 策略管理器接口
 */
export interface IStrategyManager {
  /**
   * 设置遍历策略
   * @param strategy 遍历策略
   */
  setTraversalStrategy(strategy: TraversalStrategy): void;

  /**
   * 获取遍历策略
   */
  getTraversalStrategy(): TraversalStrategy;

  /**
   * 计算任务优先级
   * @param task 爬取任务
   */
  calculateTaskPriority(task: CrawlTask): number;

  /**
   * 应用深度控制
   * @param url URL
   * @param depth 当前深度
   */
  shouldCrawlByDepth(url: string, depth: number): boolean;

  /**
   * 应用域名控制
   * @param url URL
   */
  shouldCrawlByDomain(url: string): boolean;

  /**
   * 应用速率控制
   * @param domain 域名
   */
  getRateLimitDelay(domain: string): number;

  /**
   * 获取策略统计信息
   */
  getStrategyStats(): {
    totalUrlsProcessed: number;
    urlsFilteredByDepth: number;
    urlsFilteredByDomain: number;
    averageProcessingTime: number;
  };

  /**
   * 获取策略统计信息 (别名方法，保持兼容性)
   */
  getStats(): {
    totalUrlsProcessed: number;
    urlsFilteredByDepth: number;
    urlsFilteredByDomain: number;
    averageProcessingTime: number;
  };

  /**
   * 清理资源
   */
  cleanup(): Promise<void>;
}
