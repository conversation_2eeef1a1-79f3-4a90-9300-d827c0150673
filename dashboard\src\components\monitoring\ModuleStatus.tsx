import { <PERSON><PERSON>, AlertTriangle, Clock, Zap, Server, Cpu, Database, Brain, Eye } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { StatusBadge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { formatUptime, formatRelativeTime, getHealthIndicatorColor } from '@/lib/utils';
import type { ModuleStatus as ModuleStatusType } from '@/types/monitoring';

interface ModuleStatusProps {
  modules: ModuleStatusType[];
  loading?: boolean;
}

interface ModuleCardProps {
  module: ModuleStatusType;
}

// 模块图标映射和颜色映射
const getModuleIcon = (moduleName: string) => {
  const icons = {
    crawler: <Zap className="h-5 w-5" />,
    storage: <Database className="h-5 w-5" />,
    fetcher: <Eye className="h-5 w-5" />,
    parser: <Cpu className="h-5 w-5" />,
    ai: <Brain className="h-5 w-5" />,
  };
  return icons[moduleName as keyof typeof icons] || <Server className="h-5 w-5" />;
};

// 模块颜色主题映射
const getModuleTheme = (moduleName: string) => {
  const themes = {
    crawler: { bg: 'bg-blue-100', text: 'text-blue-600', border: 'border-blue-200' },
    storage: { bg: 'bg-purple-100', text: 'text-purple-600', border: 'border-purple-200' },
    fetcher: { bg: 'bg-indigo-100', text: 'text-indigo-600', border: 'border-indigo-200' },
    parser: { bg: 'bg-orange-100', text: 'text-orange-600', border: 'border-orange-200' },
    ai: { bg: 'bg-pink-100', text: 'text-pink-600', border: 'border-pink-200' },
  };
  return themes[moduleName as keyof typeof themes] || { bg: 'bg-gray-100', text: 'text-gray-600', border: 'border-gray-200' };
};

// 模块名称映射
const getModuleName = (moduleName: string) => {
  const names = {
    crawler: '爬虫引擎',
    storage: '存储管理',
    fetcher: '数据获取',
    parser: '内容解析',
    ai: 'AI引擎',
  };
  return names[moduleName as keyof typeof names] || moduleName;
};

function ModuleCard({ module }: ModuleCardProps) {
  const healthColor = getHealthIndicatorColor(module.status);
  const isHealthy = module.healthy;
  const isInitialized = module.initialized;
  const hasError = module.error;
  const hasMetrics = module.metrics && Object.keys(module.metrics).length > 0;
  const moduleTheme = getModuleTheme(module.name);

  return (
    <div className="relative bg-white border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow mb-2">
      {/* 健康状态指示器 */}
      <div className={`absolute top-0 left-0 w-1 h-full rounded-l-lg ${healthColor}`} />
      
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${moduleTheme.bg} ${moduleTheme.border} border`}>
            <div className={moduleTheme.text}>
              {getModuleIcon(module.name)}
            </div>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              {getModuleName(module.name)}
              {!isInitialized && (
                <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">
                  未初始化
                </span>
              )}
            </h4>
            <p className="text-sm text-gray-500 flex items-center gap-1" title="检查时间">
              <Clock className="h-3 w-3" />
              : {formatRelativeTime(module.lastChecked)}
            </p>
          </div>
        </div>
        <StatusBadge status={module.status} />
      </div>

      {/* 模块详情 - 简化版本 */}
      <div className="space-y-2">
        {/* 基本状态信息 - 更紧凑的布局 */}
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">状态:</span>
          <div className="flex items-center gap-3">
            <span className={`font-medium ${isHealthy ? 'text-green-600' : 'text-red-600'}`}>
              {isHealthy ? '健康' : '异常'}
            </span>
            <span className="text-gray-400">|</span>
            <span className={`text-xs px-2 py-1 rounded ${isInitialized ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'}`}>
              {isInitialized ? '已初始化' : '未初始化'}
            </span>
          </div>
        </div>

        {module.details?.uptime && (
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>运行时间:</span>
            <span className="font-mono">{formatUptime(module.details.uptime)}</span>
          </div>
        )}

        {/* 模块指标 - 更紧凑的显示 */}
        {hasMetrics && (
          <div className="border-t pt-2">
            <div className="grid grid-cols-2 gap-2 text-xs">
              {/* 根据不同模块显示不同指标 */}
              {module.name === 'crawler' && module.metrics && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-500">任务:</span>
                    <span className="font-medium">{module.metrics.activeTasks || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">成功率:</span>
                    <span className={`font-medium ${(module.metrics.successRate || 0) > 80 ? 'text-green-600' : 'text-yellow-600'}`}>
                      {module.metrics.successRate || 0}%
                    </span>
                  </div>
                </>
              )}
              
              {module.name === 'fetcher' && module.metrics && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-500">成功率:</span>
                    <span className={`font-medium ${(module.metrics.successRate || 0) > 80 ? 'text-green-600' : 'text-red-600'}`}>
                      {(module.metrics.successRate || 0).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">代理:</span>
                    <span className="font-medium">{module.metrics.healthyProxies || 0}</span>
                  </div>
                </>
              )}

              {module.name === 'storage' && module.metrics && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-500">项目:</span>
                    <span className="font-medium">{module.metrics.extractedItems || 0}</span>
                  </div>
                  {module.metrics.storageHealth && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">存储:</span>
                      <span className="font-medium text-green-600">正常</span>
                    </div>
                  )}
                </>
              )}

              {module.name === 'parser' && module.metrics && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-500">成功率:</span>
                    <span className={`font-medium ${(module.metrics.successRate || 0) >= 80 ? 'text-green-600' : 
                      (module.metrics.successRate || 0) >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>
                      {(module.metrics.successRate || 0).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">解析时间:</span>
                    <span className="font-medium">{(module.metrics.averageParseTime || 0).toFixed(0)}ms</span>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {hasError && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-red-800">错误信息</p>
                <p className="text-sm text-red-700 mt-1">{module.error}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export function ModuleStatus({ modules, loading }: ModuleStatusProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Boxes className="h-5 w-5 mr-2 text-purple-500" />
            模块状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!modules || modules.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Boxes className="h-5 w-5 mr-2 text-purple-500" />
            模块状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Boxes className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">暂无模块数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 统计模块状态 - 使用新的健康字段
  const statusCounts = modules.reduce(
    (acc, module) => {
      acc[module.status] = (acc[module.status] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  // 使用 healthy 字段计算健康模块数量
  const healthyCount = modules.filter(module => module.healthy).length;
  const initializedCount = modules.filter(module => module.initialized).length;
  const totalCount = modules.length;
  const healthPercentage = totalCount > 0 ? (healthyCount / totalCount) * 100 : 0;
  const initPercentage = totalCount > 0 ? (initializedCount / totalCount) * 100 : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Boxes className="h-5 w-5 mr-2 text-purple-500" />
          模块状态概览
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 总体状态概览 */}
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg p-4">
          <div className="grid grid-cols-2 gap-6 mb-4">
            {/* 健康状况 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">整体健康状况</h4>
                <span className={`text-2xl font-bold ${healthPercentage >= 80 ? 'text-green-600' : 
                  healthPercentage >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                  {healthPercentage.toFixed(0)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    healthPercentage >= 80 ? 'bg-green-500' : 
                    healthPercentage >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${healthPercentage}%` }}
                />
              </div>
              <p className="text-xs text-gray-600">
                {healthyCount} / {totalCount} 模块健康
              </p>
            </div>

            {/* 初始化状况 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">初始化状况</h4>
                <span className={`text-2xl font-bold ${initPercentage >= 80 ? 'text-green-600' : 'text-orange-600'}`}>
                  {initPercentage.toFixed(0)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    initPercentage >= 80 ? 'bg-green-500' : 'bg-orange-500'
                  }`}
                  style={{ width: `${initPercentage}%` }}
                />
              </div>
              <p className="text-xs text-gray-600">
                {initializedCount} / {totalCount} 模块已初始化
              </p>
            </div>
          </div>
          
          {/* 状态统计 */}
          <div className="grid grid-cols-2 lg:grid-cols-5 gap-3 text-sm">
            <div className="text-center bg-white rounded p-2">
              <div className="font-semibold text-green-600">
                {statusCounts.healthy || 0}
              </div>
              <div className="text-gray-500 text-xs">健康</div>
            </div>
            <div className="text-center bg-white rounded p-2">
              <div className="font-semibold text-yellow-600">
                {statusCounts.warning || 0}
              </div>
              <div className="text-gray-500 text-xs">警告</div>
            </div>
            <div className="text-center bg-white rounded p-2">
              <div className="font-semibold text-red-600">
                {statusCounts.critical || 0}
              </div>
              <div className="text-gray-500 text-xs">严重</div>
            </div>
            <div className="text-center bg-white rounded p-2">
              <div className="font-semibold text-gray-600">
                {statusCounts.stopped || 0}
              </div>
              <div className="text-gray-500 text-xs">停止</div>
            </div>
            <div className="text-center bg-white rounded p-2">
              <div className="font-semibold text-orange-600">
                {statusCounts.initializing || 0}
              </div>
              <div className="text-gray-500 text-xs">初始化中</div>
            </div>
          </div>
        </div>

        {/* 模块列表 - 网格布局适应更多模块 */}
        <div className="grid grid-cols-1 gap-3">
          {modules
            .sort((a, b) => {
              // 按健康状态排序：健康的在前，然后按状态排序
              if (a.healthy !== b.healthy) return a.healthy ? -1 : 1;
              const statusOrder = { healthy: 0, warning: 1, stopped: 2, critical: 3, initializing: 4 };
              return (statusOrder[a.status as keyof typeof statusOrder] || 5) - 
                     (statusOrder[b.status as keyof typeof statusOrder] || 5);
            })
            .map((module) => (
              <ModuleCard key={module.name} module={module} />
            ))}
        </div>
      </CardContent>
    </Card>
  );
}
