import React, { useState, useEffect, useRef } from 'react';
import { Card, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';
import { CrawlerForm } from './CrawlerForm';
import { TaskList } from './TaskList';
import { CrawlerStats } from './CrawlerStats';
import { useCrawler } from '@/hooks/useCrawler';
import { Globe, Plus, Play, Pause, Square } from 'lucide-react';
import { logger } from '@/lib/utils';

interface CrawlerManagerProps {
  className?: string;
}

export const CrawlerManager: React.FC<CrawlerManagerProps> = ({ className = '' }) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [isOperating, setIsOperating] = useState(false);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {
    tasks,
    stats,
    loading,
    error,
    refreshData,
    createTask,
    startTask,
    pauseTask,
    stopTask,
    deleteTask,
  } = useCrawler();

  // 优化的自动刷新：减少刷新频率，避免操作时刷新
  useEffect(() => {
    const scheduleRefresh = () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
      
      // 如果正在进行操作，延迟刷新
      const delay = isOperating ? 10000 : 8000; // 操作时10秒，平时8秒
      
      refreshTimeoutRef.current = setTimeout(() => {
        if (!isOperating) {
          refreshData();
        }
        scheduleRefresh();
      }, delay);
    };

    scheduleRefresh();

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [refreshData, isOperating]);

  const handleCreateTask = async (taskData: any) => {
    try {
      setIsOperating(true);
      await createTask(taskData);
      setShowCreateForm(false);
      // 不立即刷新，等待 WebSocket 事件更新
    } catch (error) {
      console.error('Failed to create task:', error);
    } finally {
      setIsOperating(false);
    }
  };

  const handleStartSelected = async () => {
    if (selectedTasks.length === 0) return;

    try {
      setIsOperating(true);
      // 批量操作时显示进度，避免界面卡顿
      const promises = selectedTasks.map(taskId => startTask(taskId));
      await Promise.allSettled(promises); // 使用 allSettled 而不是 all，避免一个失败影响全部
      setSelectedTasks([]);
    } catch (error) {
      console.error('Failed to start tasks:', error);
    } finally {
      setIsOperating(false);
    }
  };

  const handlePauseSelected = async () => {
    if (selectedTasks.length === 0) return;

    try {
      setIsOperating(true);
      const promises = selectedTasks.map(taskId => pauseTask(taskId));
      await Promise.allSettled(promises);
      setSelectedTasks([]);
    } catch (error) {
      console.error('Failed to pause tasks:', error);
    } finally {
      setIsOperating(false);
    }
  };

  const handleStopSelected = async () => {
    if (selectedTasks.length === 0) return;

    try {
      setIsOperating(true);
      const promises = selectedTasks.map(taskId => stopTask(taskId));
      await Promise.allSettled(promises);
      setSelectedTasks([]);
    } catch (error) {
      console.error('Failed to stop tasks:', error);
    } finally {
      setIsOperating(false);
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedTasks.length === 0) return;
    // 显示确认对话框
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    setShowDeleteConfirm(false);
    
    try {
      setIsOperating(true);
      const promises = selectedTasks.map(taskId => deleteTask(taskId));
      await Promise.allSettled(promises);
      setSelectedTasks([]);
    } catch (error) {
      logger.error('Failed to delete tasks', error);
    } finally {
      setIsOperating(false);
    }
  };

  // 手动刷新
  const handleManualRefresh = async () => {
    setIsOperating(true);
    try {
      await refreshData();
    } finally {
      setIsOperating(false);
    }
  };

  if (error && !tasks.length) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <Globe className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">爬虫管理器加载失败</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={handleManualRefresh} variant="primary" loading={loading}>
            重试
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 爬虫统计 */}
      <CrawlerStats stats={stats} loading={loading && !tasks.length} />

      {/* 任务控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle icon={<Globe className="h-5 w-5" />}>
            爬虫任务管理
          </CardTitle>
          <div className="flex space-x-2">
            <Button
              onClick={() => setShowCreateForm(true)}
              variant="primary"
              size="sm"
              icon={<Plus className="h-4 w-4" />}
              disabled={isOperating}
            >
              创建任务
            </Button>
            <Button
              onClick={handleManualRefresh}
              variant="ghost"
              size="sm"
              loading={loading}
              disabled={isOperating}
            >
              刷新
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* 批量操作工具栏 */}
          {selectedTasks.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-800">
                  已选择 {selectedTasks.length} 个任务
                </span>
                <div className="flex space-x-2">
                  <Button
                    onClick={handleStartSelected}
                    variant="success"
                    size="sm"
                    icon={<Play className="h-4 w-4" />}
                    loading={isOperating}
                    disabled={isOperating}
                  >
                    批量启动
                  </Button>
                  <Button
                    onClick={handlePauseSelected}
                    variant="warning"
                    size="sm"
                    icon={<Pause className="h-4 w-4" />}
                    loading={isOperating}
                    disabled={isOperating}
                  >
                    批量暂停
                  </Button>
                  <Button
                    onClick={handleStopSelected}
                    variant="secondary"
                    size="sm"
                    icon={<Square className="h-4 w-4" />}
                    loading={isOperating}
                    disabled={isOperating}
                  >
                    批量停止
                  </Button>
                  <Button
                    onClick={handleDeleteSelected}
                    variant="danger"
                    size="sm"
                    loading={isOperating}
                    disabled={isOperating}
                  >
                    批量删除
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* 任务列表 */}
          <TaskList
            tasks={tasks}
            loading={loading && !tasks.length}
            selectedTasks={selectedTasks}
            onSelectionChange={setSelectedTasks}
            onStartTask={startTask}
            onPauseTask={pauseTask}
            onStopTask={stopTask}
            onDeleteTask={deleteTask}
            onRefresh={handleManualRefresh}
          />
        </CardContent>
      </Card>

      {/* 创建任务表单模态框 */}
      {showCreateForm && (
        <CrawlerForm
          onSubmit={handleCreateTask}
          onClose={() => setShowCreateForm(false)}
        />
      )}

      {/* 确认删除对话框 */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        title="确认删除任务"
        message={`此操作将删除所选的 ${selectedTasks.length} 个任务，且不可撤销。请确认您要继续操作。`}
        confirmText="确认删除"
        cancelText="取消"
        onConfirm={handleConfirmDelete}
        onCancel={() => setShowDeleteConfirm(false)}
        variant="danger"
      />
    </div>
  );
};