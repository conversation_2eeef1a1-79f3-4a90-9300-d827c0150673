{"name": "nodex", "version": "2.0.0", "description": "企业级智能爬虫 HTTP API 服务 - 提供 RESTful 接口的 Node.js 爬虫平台", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && tsc-alias", "build:watch": "tsc --watch", "build:frontend": "cd dashboard && npm run build", "build:full": "./build.sh", "dev:backend": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "dev:frontend": "cd dashboard && npm run dev", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "start": "node dist/index.js", "start:production": "NODE_ENV=production STATIC_PATH=dist/public node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "type-check:frontend": "cd dashboard && npm run type-check", "type-check:all": "npm run type-check && npm run type-check:frontend", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "lint:frontend": "cd dashboard && npm run lint", "lint:all": "npm run lint && npm run lint:frontend", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "format:frontend": "cd dashboard && npm run format", "format:all": "npm run format && npm run format:frontend", "clean": "rm -rf dist", "clean:all": "rm -rf dist && cd dashboard && rm -rf dist", "deploy:build": "npm run clean:all && npm run build:full", "deploy:start": "npm run deploy:build && npm run start:production"}, "keywords": ["crawler", "scraper", "spider", "nodejs", "typescript", "api", "restful", "http", "enterprise", "intelligent", "bullmq", "redis", "puppeteer", "playwright"], "author": "Nodex Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/nodex.git"}, "bugs": {"url": "https://github.com/your-org/nodex/issues"}, "homepage": "https://github.com/your-org/nodex#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@types/cheerio": "^0.22.35", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/inquirer": "^9.0.8", "@types/jest": "^30.0.0", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.19", "@types/mongodb": "^4.0.6", "@types/node": "^24.0.7", "@types/puppeteer": "^5.4.7", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "jest": "^30.0.3", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "dependencies": {"@elastic/elasticsearch": "^9.0.2", "@mozilla/readability": "^0.6.0", "@types/multer": "^2.0.0", "@types/xml2js": "^0.4.14", "axios": "^1.10.0", "bull": "^4.16.5", "bullmq": "^5.56.0", "chalk": "^5.3.0", "cheerio": "^1.1.0", "cli-table3": "^0.6.5", "commander": "^14.0.0", "concurrently": "^9.2.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^4.21.2", "glob": "^11.0.3", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "inquirer": "^9.3.7", "ioredis": "^5.6.1", "jsdom": "^26.1.0", "lodash": "^4.17.21", "meilisearch": "^0.51.0", "mongodb": "^6.17.0", "multer": "^2.0.1", "ora": "^7.0.1", "playwright": "^1.53.1", "puppeteer": "^24.11.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "redis": "^5.5.6", "socket.io": "^4.8.1", "winston": "^3.17.0", "xml2js": "^0.6.2"}, "packageManager": "pnpm@10.6.5+sha512.cdf928fca20832cd59ec53826492b7dc25dc524d4370b6b4adbf65803d32efaa6c1c88147c0ae4e8d579a6c9eec715757b50d4fa35eea179d868eada4ed043af"}