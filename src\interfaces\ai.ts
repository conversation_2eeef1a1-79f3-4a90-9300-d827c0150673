/**
 * AI 引擎相关接口定义
 */

/**
 * AI 提供商类型
 */
export type AIProvider = 'openai' | 'gemini' | 'local';

/**
 * AI 模型配置
 */
export interface AIModelConfig {
  /** 模型名称 */
  model: string;
  /** 最大 token 数 */
  maxTokens?: number;
  /** 温度参数 (0-1) */
  temperature?: number;
  /** Top-p 参数 */
  topP?: number;
  /** 频率惩罚 */
  frequencyPenalty?: number;
  /** 存在惩罚 */
  presencePenalty?: number;
  /** 停止词 */
  stopSequences?: string[];
  /** 额外参数 */
  extra?: Record<string, string>;
}

/**
 * AI 请求选项
 */
export interface AIRequestOptions {
  /** 模型配置 */
  model?: AIModelConfig;
  /** 请求超时 (毫秒) */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 流式响应 */
  stream?: boolean;
  /** 自定义选项 */
  customOptions?: Record<string, any>;
}

/**
 * AI 响应接口
 */
export interface AIResponse {
  /** 响应内容 */
  content: string;
  /** 使用的 token 数 */
  tokensUsed?: {
    prompt: number;
    completion: number;
    total: number;
  };
  /** 响应时间 (毫秒) */
  responseTime: number;
  /** 模型名称 */
  model: string;
  /** 完成原因 */
  finishReason?: 'stop' | 'length' | 'content_filter' | 'function_call';
  /** 原始响应 */
  rawResponse?: any;
}

/**
 * 内容提取请求
 */
export interface ContentExtractionRequest {
  /** 页面内容 */
  content: string;
  /** 页面URL */
  url: string;
  /** 提取模式 (自然语言描述) */
  schema: Record<string, string>;
  /** 内容类型 */
  contentType?: string;
  /** 额外上下文 */
  context?: Record<string, any>;
}

/**
 * 内容提取响应
 */
export interface ContentExtractionResponse {
  /** 提取的结构化数据 */
  extractedData: Record<string, any>;
  /** 置信度 (0-1) */
  confidence: number;
  /** 提取的字段数量 */
  extractedFieldsCount: number;
  /** AI 响应信息 */
  aiResponse: AIResponse;
  /** 错误信息 */
  error?: string;
}

/**
 * 抓取计划请求
 */
export interface CrawlPlanRequest {
  /** 主题描述 */
  topic: string;
  /** 目标数据类型 */
  dataType?: string;
  /** 语言偏好 */
  language?: string;
  /** 地区偏好 */
  region?: string;
  /** 最大URL数量 */
  maxUrls?: number;
  /** 额外要求 */
  requirements?: string[];
  /** 偏好设置 */
  preferences?: {
    strategy?: string;
    respectRobots?: boolean;
    useProxy?: boolean;
  };
}

/**
 * 抓取计划响应
 */
export interface CrawlPlanResponse {
  /** 生成的种子URL */
  seedUrls: string[];
  /** 相关关键词 */
  keywords: string[];
  /** 建议的数据提取模式 */
  potentialSchema: Record<string, string>;
  /** 建议的抓取策略 */
  strategy: {
    /** 遍历策略 */
    traversalStrategy: 'BFS' | 'DFS';
    /** 最大深度 */
    maxDepth: number;
    /** 域名限制 */
    domainRestrictions: string[];
  };
  /** 计划置信度 */
  confidence: number;
  /** AI 响应信息 */
  aiResponse: AIResponse;
  /** 错误信息 */
  error?: string;
}

/**
 * 链接提取请求
 */
export interface LinkExtractionRequest {
  /** 页面内容 */
  content: string;
  /** 页面URL */
  url: string;
  /** 链接类型描述 */
  linkTypeDescription?: string;
  /** 最大链接数量 */
  maxLinks?: number;
}

/**
 * 链接提取响应
 */
export interface LinkExtractionResponse {
  /** 提取的链接 */
  links: Array<{
    url: string;
    text: string;
    type: string;
    confidence: number;
  }>;
  /** AI 响应信息 */
  aiResponse: AIResponse;
  /** 错误信息 */
  error?: string;
}

/**
 * AI 提供商接口
 */
export interface IAIProvider {
  /** 提供商名称 */
  readonly name: AIProvider;
  /** 提供商版本 */
  readonly version: string;

  /**
   * 初始化提供商
   */
  initialize(): Promise<void>;

  /**
   * 发送聊天请求
   * @param messages 消息列表
   * @param options 请求选项
   */
  chat(
    _messages: Array<{
      role: 'system' | 'user' | 'assistant';
      content: string;
    }>,
    _options?: AIRequestOptions
  ): Promise<AIResponse>;

  /**
   * 发送完成请求
   * @param prompt 提示词
   * @param options 请求选项
   */
  complete(_prompt: string, _options?: AIRequestOptions): Promise<AIResponse>;

  /**
   * 检查健康状态
   */
  healthCheck(): Promise<boolean>;

  /**
   * 获取使用统计
   */
  getUsageStats(): {
    totalRequests: number;
    totalTokens: number;
    averageResponseTime: number;
    errorRate: number;
  };
}

/**
 * AI 引擎接口
 */
export interface IAIEngine {
  /**
   * 注册 AI 提供商
   * @param provider AI 提供商实例
   */
  registerProvider(_provider: IAIProvider): void;

  /**
   * 设置默认提供商
   * @param providerName 提供商名称
   */
  setDefaultProvider(_providerName: AIProvider): void;

  /**
   * 智能内容提取
   * @param request 内容提取请求
   * @param providerName 指定的提供商 (可选)
   */
  extractContent(
    _request: ContentExtractionRequest,
    _providerName?: AIProvider
  ): Promise<ContentExtractionResponse>;

  /**
   * 生成抓取计划
   * @param request 抓取计划请求
   * @param providerName 指定的提供商 (可选)
   */
  generateCrawlPlan(
    _request: CrawlPlanRequest,
    _providerName?: AIProvider
  ): Promise<CrawlPlanResponse>;

  /**
   * 智能链接提取
   * @param request 链接提取请求
   * @param providerName 指定的提供商 (可选)
   */
  extractLinks(
    _request: LinkExtractionRequest,
    _providerName?: AIProvider
  ): Promise<LinkExtractionResponse>;

  /**
   * 自定义 AI 请求
   * @param prompt 提示词
   * @param options 请求选项
   * @param providerName 指定的提供商 (可选)
   */
  customRequest(
    _prompt: string,
    _options?: AIRequestOptions,
    _providerName?: AIProvider
  ): Promise<AIResponse>;

  /**
   * 获取可用的提供商列表
   */
  getAvailableProviders(): AIProvider[];

  /**
   * 获取提供商状态
   */
  getProviderStatus(): Record<AIProvider, boolean>;

  /**
   * 初始化 AI 引擎
   */
  initialize(): Promise<void>;

  /**
   * 关闭 AI 引擎
   */
  close(): Promise<void>;

  /**
   * 获取使用统计
   */
  getUsageStats(): Record<AIProvider, any>;
}

/**
 * AI 解析器接口
 */
export interface IAIParser {
  /**
   * 使用 AI 解析内容
   * @param content 页面内容
   * @param schema 提取模式
   * @param options 解析选项
   */
  parseWithAI(
    _content: string,
    _schema: Record<string, string>,
    _options?: {
      url?: string;
      contentType?: string;
      model?: AIModelConfig;
      extractLinks?: boolean;
    }
  ): Promise<{
    extractedData: Record<string, any>;
    newUrls: string[];
    confidence: number;
    aiResponse: AIResponse;
  }>;

  /**
   * 智能识别页面类型
   * @param content 页面内容
   * @param url 页面URL
   */
  identifyPageType(
    _content: string,
    _url: string
  ): Promise<{
    pageType: string;
    confidence: number;
    suggestedSchema: Record<string, string>;
    aiResponse: AIResponse;
  }>;

  /**
   * 生成提取模式
   * @param content 示例内容
   * @param description 数据描述
   */
  generateSchema(
    _content: string,
    _description: string
  ): Promise<{
    schema: Record<string, string>;
    confidence: number;
    aiResponse: AIResponse;
  }>;
}
