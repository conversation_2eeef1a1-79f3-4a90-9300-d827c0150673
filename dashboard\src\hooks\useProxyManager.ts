import { useState, useEffect, useCallback } from 'react';
import { ProxyConfig, ApiResponse } from '@/types/task';

// 代理统计类型
export interface ProxyStats {
  total: number;
  available: number;
  healthy: number;
  failed: number;
}

export const useProxyManager = () => {
  const [proxies, setProxies] = useState<ProxyConfig[]>([]);
  const [stats, setStats] = useState<ProxyStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE = '/api/v1/proxy';

  const handleApiError = (error: any) => {
    console.error('API Error:', error);
    const message = error?.response?.data?.error || error?.message || '操作失败';
    setError(message);
    return message;
  };

  const fetchProxies = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(API_BASE);
      const result: ApiResponse<{ proxies: ProxyConfig[]; stats: ProxyStats }> = await response.json();

      if (result.success && result.data) {
        setProxies(result.data.proxies || []);
        setStats(result.data.stats || { total: 0, available: 0, healthy: 0, failed: 0 });
      } else {
        throw new Error(result.error || '获取代理列表失败');
      }
    } catch (error) {
      handleApiError(error);
    } finally {
      setLoading(false);
    }
  }, []);

  const addProxy = useCallback(async (proxyData: any) => {
    try {
      setLoading(true);
      setError(null);

      const endpoint = proxyData.proxies ? `${API_BASE}/batch` : API_BASE;
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(proxyData),
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || '添加代理失败');
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  const removeProxy = useCallback(async (proxy: ProxyConfig) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(API_BASE, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(proxy),
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || '删除代理失败');
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  const updateProxy = useCallback(async (oldProxy: ProxyConfig, newProxy: ProxyConfig) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(API_BASE, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ oldProxy, newProxy }),
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || '更新代理失败');
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  const testAllProxies = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE}/test`, {
        method: 'POST',
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || '测试代理失败');
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  const clearAllProxies = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE}/all`, {
        method: 'DELETE',
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || '清空代理失败');
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  const loadProxiesFromFile = useCallback(async (file: File) => {
    try {
      setLoading(true);
      setError(null);

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${API_BASE}/load`, {
        method: 'POST',
        body: formData,
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || '从文件加载代理失败');
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchProxies();
  }, [fetchProxies]);

  // 初始化时加载数据
  useEffect(() => {
    fetchProxies();
  }, [fetchProxies]);

  return {
    proxies,
    stats,
    loading,
    error,
    refreshData,
    addProxy,
    removeProxy,
    updateProxy,
    testAllProxies,
    clearAllProxies,
    loadProxiesFromFile,
  };
};
