import type {
  SystemStats,
  QueueStats,
  ModuleStatus,
  CrawlSession,
  HealthCheckResult,
  ApiResponse,
} from '@/types/monitoring';
import {
  Task,
  CrawlerStats,
  CreateTaskRequest,
  CreateTaskResponse,
  TaskOperationResult
} from '@/types/task';
import { parseApiError, logError } from './errors';

/**
 * 简化的 API 客户端
 * 只负责基础的 HTTP 请求，不进行数据转换
 * 所有数据转换逻辑都由后端处理，前端直接使用标准化数据
 */
class ApiClient {
  private baseUrl: string;

  constructor() {
    // 在开发模式下使用 Vite 代理，生产模式下使用环境变量
    this.baseUrl = import.meta.env.VITE_API_URL || '';
  }

  /**
   * 通用请求方法
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorText = await response.text();
        const error = new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        const appError = parseApiError(error);
        logError(appError);
        throw error;
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (!(error as Error).message?.includes('HTTP')) {
        const appError = parseApiError(error);
        logError(appError);
      }
      throw error;
    }
  }

  /**
   * GET 请求
   */
  private async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  /**
   * POST 请求
   */
  private async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // === 核心监控 API ===
  // 注意：这些 API 端点期望后端提供标准化的数据格式，无需前端转换

  /**
   * 获取系统状态 - 期望后端返回 SystemStats 格式
   */
  async getSystemStats(): Promise<SystemStats> {
    return this.get<SystemStats>('/api/v1/monitoring/system');
  }

  /**
   * 获取队列状态 - 期望后端返回 QueueStats[] 格式
   */
  async getQueueStats(): Promise<QueueStats[]> {
    return this.get<QueueStats[]>('/api/v1/monitoring/queues');
  }

  /**
   * 获取模块状态 - 期望后端返回 ModuleStatus[] 格式
   */
  async getModuleStatus(): Promise<ModuleStatus[]> {
    return this.get<ModuleStatus[]>('/api/v1/monitoring/modules');
  }

  /**
   * 获取会话列表 - 期望后端返回 CrawlSession[] 格式
   */
  async getSessions(): Promise<CrawlSession[]> {
    return this.get<CrawlSession[]>('/api/v1/monitoring/sessions');
  }

  /**
   * 健康检查 - 期望后端返回 HealthCheckResult 格式
   */
  async healthCheck(): Promise<HealthCheckResult> {
    return this.get<HealthCheckResult>('/api/v1/system/health');
  }

  // === 系统控制 API ===

  /**
   * 暂停系统
   */
  async pauseSystem(): Promise<ApiResponse> {
    return this.post<ApiResponse>('/api/v1/system/pause');
  }

  /**
   * 恢复系统
   */
  async resumeSystem(): Promise<ApiResponse> {
    return this.post<ApiResponse>('/api/v1/system/resume');
  }

  /**
   * 重启系统
   */
  async restartSystem(): Promise<ApiResponse> {
    return this.post<ApiResponse>('/api/v1/system/restart');
  }

  // === 队列控制 API ===

  /**
   * 暂停队列
   */
  async pauseQueue(queueName: string): Promise<ApiResponse> {
    return this.post<ApiResponse>(`/api/v1/queues/${queueName}/pause`);
  }

  /**
   * 恢复队列
   */
  async resumeQueue(queueName: string): Promise<ApiResponse> {
    return this.post<ApiResponse>(`/api/v1/queues/${queueName}/resume`);
  }

  /**
   * 清空队列
   */
  async clearQueue(queueName: string): Promise<ApiResponse> {
    return this.post<ApiResponse>(`/api/v1/queues/${queueName}/clear`);
  }

  /**
   * 重试失败的任务
   */
  async retryFailedJobs(queueName: string): Promise<ApiResponse> {
    return this.post<ApiResponse>(`/api/v1/queues/${queueName}/retry-failed`);
  }

  // === 爬虫任务管理 API ===

  /**
   * 获取任务列表
   */
  async getTasks(): Promise<Task[]> {
    const response = await this.get<TaskOperationResult>('/api/v1/tasks');
    return response.data as Task[]
  }

  /**
   * 获取爬虫统计数据
   */
  async getCrawlerStats(): Promise<CrawlerStats> {
    return this.get<CrawlerStats>('/api/v1/tasks/stats');
  }

  /**
   * 创建爬虫任务
   */
  async createTask(taskData: CreateTaskRequest): Promise<CreateTaskResponse> {
    return this.post<CreateTaskResponse>('/api/v1/tasks', taskData);
  }

  /**
   * 启动任务
   */
  async startTask(taskId: string): Promise<TaskOperationResult> {
    return this.post<TaskOperationResult>(`/api/v1/tasks/${taskId}/start`);
  }

  /**
   * 暂停任务
   */
  async pauseTask(taskId: string): Promise<TaskOperationResult> {
    return this.post<TaskOperationResult>(`/api/v1/tasks/${taskId}/pause`);
  }

  /**
   * 停止任务
   */
  async stopTask(taskId: string): Promise<TaskOperationResult> {
    return this.post<TaskOperationResult>(`/api/v1/tasks/${taskId}/stop`);
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId: string): Promise<TaskOperationResult> {
    return this.request<TaskOperationResult>(`/api/v1/tasks/${taskId}`, { method: 'DELETE' });
  }

  /**
   * 获取任务详情
   */
  async getTask(taskId: string): Promise<Task> {
    return this.get<Task>(`/api/v1/tasks/${taskId}`);
  }

  /**
   * 获取任务结果
   */
  async getTaskResults(taskId: string, page: number = 1, limit: number = 20): Promise<any> {
    return this.get<any>(`/api/v1/tasks/${taskId}/results?page=${page}&limit=${limit}`);
  }
}

// 创建单例实例
const apiClient = new ApiClient();

export default apiClient;
