/**
 * 简化的 AI 引擎实现
 */

import {
  IAIProvider,
  AIProvider,
  AIResponse,
  AIRequestOptions,
} from '@/interfaces';
import { OpenAIProvider } from './openai-provider';
import { GeminiProvider } from './gemini-provider';
import { getLogger, info, error } from '@/utils';

/**
 * 简化的 AI 引擎实现类
 */
export class AIEngine {
  private providers: Map<AIProvider, IAIProvider> = new Map();
  private defaultProvider: AIProvider = 'openai';
  private isInitialized = false;
  private logger = getLogger('AIEngine');
  private stats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    totalTokensUsed: 0,
    averageResponseTime: 0,
    lastRequestTime: new Date(),
  };

  constructor() {
    // 初始化默认提供商
    this.initializeDefaultProviders();
  }

  /**
   * 初始化默认提供商
   */
  private initializeDefaultProviders(): void {
    // 可以在这里初始化默认的AI提供商
  }

  /**
   * 注册 AI 提供商
   */
  public registerProvider(provider: IAIProvider): void {
    this.providers.set(provider.name, provider);
    info(`Registered AI provider: ${provider.name}`);
  }

  /**
   * 设置默认提供商
   */
  public setDefaultProvider(provider: AIProvider): void {
    if (this.providers.has(provider)) {
      this.defaultProvider = provider;
    } else {
      throw new Error(`Provider ${provider} is not registered`);
    }
  }

  /**
   * 获取提供商
   */
  private getProvider(providerName?: AIProvider): IAIProvider {
    const name = providerName || this.defaultProvider;
    const provider = this.providers.get(name);
    
    if (!provider) {
      throw new Error(`AI provider ${name} not found`);
    }
    
    return provider;
  }

  /**
   * 生成响应
   */
  public async generateResponse(request: {
    prompt: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    provider?: AIProvider;
  }): Promise<AIResponse> {
    const startTime = Date.now();
    
    try {
      const provider = this.getProvider(request.provider);
      
      const aiOptions: AIRequestOptions = {
        model: {
          model: request.model || 'gpt-3.5-turbo',
          maxTokens: request.maxTokens || 2000,
          temperature: request.temperature || 0.7,
        },
      };

      const response = await provider.complete(request.prompt, aiOptions);
      
      // 更新统计信息
      const tokensUsed = typeof response.tokensUsed === 'number' ? response.tokensUsed : 0;
      this.updateStats(startTime, true, tokensUsed);
      
      return response;
      
    } catch (error) {
      this.updateStats(startTime, false, 0);
      throw error;
    }
  }

  /**
   * 完成文本
   */
  public async complete(prompt: string, options?: AIRequestOptions): Promise<AIResponse> {
    return this.generateResponse({
      prompt,
      model: options?.model?.model,
      maxTokens: options?.model?.maxTokens,
      temperature: options?.model?.temperature,
    });
  }

  /**
   * 更新统计信息
   */
  private updateStats(startTime: number, success: boolean, tokensUsed: number): void {
    const responseTime = Date.now() - startTime;
    
    this.stats.totalRequests++;
    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
    }
    
    this.stats.totalTokensUsed += tokensUsed;
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / 
      this.stats.totalRequests;
    this.stats.lastRequestTime = new Date();
  }

  /**
   * 获取统计信息
   */
  public getStats(): any {
    return { ...this.stats };
  }

  /**
   * 初始化
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // 初始化所有注册的提供商
    for (const provider of this.providers.values()) {
      try {
        await provider.initialize();
      } catch (err) {
        error(`Failed to initialize provider ${provider.name}:`, err);
      }
    }

    this.isInitialized = true;
    info('AI Engine initialized');
  }

  /**
   * 关闭
   */
  public async close(): Promise<void> {
    for (const provider of this.providers.values()) {
      try {
        // 如果提供商有close方法，则调用
        if (typeof (provider as any).close === 'function') {
          await (provider as any).close();
        }
      } catch (err) {
        error(`Failed to close provider ${provider.name}:`, err);
      }
    }

    this.isInitialized = false;
    info('AI Engine closed');
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const provider = this.getProvider();
      return await provider.healthCheck();
    } catch (err) {
      error('AI Engine health check failed:', err);
      return false;
    }
  }

  /**
   * 获取可用的提供商列表
   */
  public getAvailableProviders(): AIProvider[] {
    return Array.from(this.providers.keys());
  }

  /**
   * 检查提供商是否可用
   */
  public isProviderAvailable(provider: AIProvider): boolean {
    return this.providers.has(provider);
  }
}
