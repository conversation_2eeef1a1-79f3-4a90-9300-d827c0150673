您是一位专业的 JavaScript 后端开发专家，熟悉 Node.js 爬虫开发和文档撰写。您的任务是分析一个基于 JavaScript 的爬虫服务后端代码（位于 `@src` 目录），并生成一份详细的 Markdown 技术文档，输出到 `@docs/back.md`。请按照以下要求完成：

### 任务要求
1. **分析代码逻辑**：
   - 描述爬虫服务的核心功能（如网页爬取、API爬取、数据提取、数据解析、存储）。
   - 说明每个主要模块的实现方式（如使用 `axios` 进行 HTTP 请求、`cheerio` 解析 HTML、 数据库存储结果）。
   - 提供伪代码或逻辑描述
2. **描述架构**：
   - 列出 `@src` 目录的文件结构
   - 说明模块之间的依赖关系（如爬虫模块调用数据库模块）。
   - 描述技术栈（如 Node.js、Express、MongoDB）。
3. **梳理运行流程**：
   - 描述程序的启动流程（如 Express 服务器初始化）。
   - 说明数据流（如 HTTP 请求 → 爬取 → 解析 → 存储）。
   - 提供文字描述或伪代码，必要时附带流程图（Markdown 格式）。
4. **输出格式**：
   - 使用清晰的 Markdown 结构，包含标题、列表、表格、伪代码。
   - 确保内容适合技术开发人员，语言简洁且专业。
   - 文件路径：`@docs/back.md`。

------------------------------------------------------------------------------------------------------
您是一位专业的前端开发专家，精通 JavaScript 和 React 开发，熟悉前端看板设计和文档撰写。您的任务是分析一个基于 JavaScript 的爬虫服务前端看板代码（位于 `@dashboard` 目录），并生成一份详细的 Markdown 技术文档，输出到 `@docs/front.md`。请按照以下要求完成：

### 任务要求
1. **分析代码逻辑**：
   - 描述前端看板的核心功能（如展示爬虫数据、用户输入 URL 触发爬取）。
   - 说明每个主要组件的实现方式。
   - 提供伪代码或逻辑描述。
2. **描述架构**：
   - 列出 `@dashboard` 目录的文件结构。
   - 说明组件之间的关系（如父子组件、状态管理）。
   - 描述技术栈（如 React、Axios、Redux、Chart.js）。
3. **梳理运行流程**：
   - 描述应用的启动流程（如 React 渲染根组件）。
   - 说明数据流（如用户输入 → API 请求 → 数据渲染）。
   - 提供文字描述或伪代码，必要时附带流程图（Markdown 格式）。
4. **输出格式**：
   - 使用清晰的 Markdown 结构，包含标题、列表、表格、伪代码。
   - 确保内容适合前端开发人员，语言简洁且专业。
   - 文件路径：`@docs/front.md`。
   - 与 `@docs/back.md` 的风格一致（基于之前的后端文档）。