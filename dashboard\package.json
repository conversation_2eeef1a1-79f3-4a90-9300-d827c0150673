{"name": "nodex-monitoring-dashboard", "version": "2.0.0", "description": "Nodex 监控面板前端 - 基于 Vite + React 的现代化监控界面", "private": true, "type": "module", "scripts": {"dev": "vite --port 3002", "build": "tsc && vite build", "preview": "vite preview --port 3002", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@mozilla/readability": "^0.6.0", "@types/jsdom": "^21.1.7", "chart.js": "^4.4.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "jsdom": "^26.1.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.3", "recharts": "^2.8.0", "socket.io-client": "^4.7.2", "tailwind-merge": "^2.0.0", "use-debounce": "^10.0.0"}, "devDependencies": {"@types/node": "^20.19.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.0", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.4.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "tailwindcss": "^3.3.0", "typescript": "^5.2.0", "vite": "^7.0.0", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}