/**
 * 反屏蔽浏览器下载器实现
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, LaunchOptions as PuppeteerLaunchOptions } from 'puppeteer';
import { addExtra } from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import { <PERSON><PERSON><PERSON>er<PERSON><PERSON><PERSON>, BrowserRequestOptions, FetchResponse, ErrorInfo } from '@/interfaces';
import { info, warn } from '@/utils';

// 使用 puppeteer-extra 和 stealth 插件
const puppeteerExtra = addExtra(puppeteer);
puppeteerExtra.use(StealthPlugin());

/**
 * 反屏蔽浏览器实例管理器
 */
class StealthBrowserManager {
  private browsers: Map<string, Browser> = new Map();
  private maxInstances = 3; // 减少实例数量以节省资源
  private instanceUsage: Map<string, number> = new Map();

  public async getBrowser(options: PuppeteerLaunchOptions = {}): Promise<Browser> {
    const key = this.generateBrowserKey(options);

    if (this.browsers.has(key)) {
      const browser = this.browsers.get(key)!;
      if (browser.isConnected()) {
        this.instanceUsage.set(key, (this.instanceUsage.get(key) || 0) + 1);
        return browser;
      } else {
        this.browsers.delete(key);
        this.instanceUsage.delete(key);
      }
    }

    if (this.browsers.size >= this.maxInstances) {
      await this.closeLeastUsedBrowser();
    }

    const browser = await puppeteerExtra.launch(options);
    this.browsers.set(key, browser);
    this.instanceUsage.set(key, 1);
    return browser;
  }

  private generateBrowserKey(options: PuppeteerLaunchOptions): string {
    const keyParts = [
      options.headless ? 'headless' : 'headed',
      options.args?.join(',') || '',
      JSON.stringify(options.defaultViewport || {}),
    ];
    return keyParts.join('|');
  }

  private async closeLeastUsedBrowser(): Promise<void> {
    let leastUsedKey = '';
    let minUsage = Infinity;

    for (const [key, usage] of this.instanceUsage.entries()) {
      if (usage < minUsage) {
        minUsage = usage;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      const browser = this.browsers.get(leastUsedKey);
      if (browser) {
        await browser.close().catch(console.error);
        this.browsers.delete(leastUsedKey);
        this.instanceUsage.delete(leastUsedKey);
      }
    }
  }

  public async closeAll(): Promise<void> {
    const closePromises = Array.from(this.browsers.values()).map(browser =>
      browser.close().catch(console.error)
    );
    await Promise.all(closePromises);
    this.browsers.clear();
    this.instanceUsage.clear();
  }
}

/**
 * 反屏蔽浏览器下载器实现类
 */
export class StealthBrowserFetcher implements IBrowserFetcher {
  public readonly name = 'stealth-browser-fetcher';
  public readonly type = 'browser' as const;
  public readonly version = '1.0.0';

  private browserManager: StealthBrowserManager;
  private defaultOptions: PuppeteerLaunchOptions;
  private stats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    totalResponseTime: 0,
    detectionAttempts: 0,
    bypassedDetections: 0,
  };

  constructor(defaultOptions: PuppeteerLaunchOptions = {}) {
    this.browserManager = new StealthBrowserManager();
    this.defaultOptions = {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-blink-features=AutomationControlled',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images', // 可选：禁用图片加载以提高速度
      ],
      defaultViewport: {
        width: 1920,
        height: 1080,
      },
      ...defaultOptions,
    };
  }
  async closeAllBrowsers() {}
  getBrowserInstanceCount() {
    return 1;
  }
  /**
   * 执行浏览器请求
   */
  public async fetch(url: string, options: BrowserRequestOptions = {}): Promise<FetchResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    let page: Page | null = null;

    try {
      // 获取浏览器实例
      const launchOptions = this.buildLaunchOptions(options);
      const browser = await this.browserManager.getBrowser(launchOptions);

      // 创建新页面
      page = await browser.newPage();

      // 应用反屏蔽配置
      await this.applyStealthConfiguration(page, options);

      // 导航到页面
      const response = await page.goto(url, {
        waitUntil: options.waitForLoad ? 'networkidle0' : 'domcontentloaded',
        timeout: options.waitTime || 30000,
      });

      if (!response) {
        throw new Error('Failed to load page');
      }

      // 检测是否被反爬虫系统拦截
      const isBlocked = await this.detectAntiBot(page);
      if (isBlocked) {
        this.stats.detectionAttempts++;
        // 尝试绕过反爬虫检测
        const bypassed = await this.bypassAntiBot(page, options);
        if (bypassed) {
          this.stats.bypassedDetections++;
        } else {
          throw new Error('Anti-bot detection could not be bypassed');
        }
      }

      // 等待特定选择器（如果指定）
      if (options.waitForSelector) {
        await page.waitForSelector(options.waitForSelector, {
          timeout: options.waitTime || 30000,
        });
      }

      // 执行自定义脚本（如果指定）
      if (options.executeJavaScript && options.customScript) {
        await page.evaluate(options.customScript);
      }

      // 模拟人类行为（如果启用）
      if (options.simulateHuman) {
        await this.simulateHumanBehavior(page);
      }

      // 截图（如果需要）
      if (options.screenshot?.enabled) {
        await this.takeScreenshot(page, options.screenshot);
      }

      // 获取页面内容
      const content = await page.content();
      const finalUrl = page.url();

      const responseTime = Date.now() - startTime;
      this.stats.successfulRequests++;
      this.stats.totalResponseTime += responseTime;

      // 构建响应对象
      return this.buildFetchResponse(content, response, responseTime, finalUrl);
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.stats.failedRequests++;
      this.stats.totalResponseTime += responseTime;

      throw this.handleError(error, url, responseTime);
    } finally {
      if (page) {
        await page.close().catch(console.error);
      }
    }
  }

  /**
   * 检查是否支持该URL
   */
  public canHandle(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * 初始化下载器
   */
  public async initialize(): Promise<void> {
    info('Stealth Browser Fetcher initialized');
  }

  /**
   * 清理下载器资源
   */
  public async cleanup(): Promise<void> {
    await this.browserManager.closeAll();
    info('Stealth Browser Fetcher cleaned up');
  }

  /**
   * 获取下载器统计信息
   */
  public getStats() {
    return {
      ...this.stats,
      averageResponseTime:
        this.stats.totalRequests > 0 ? this.stats.totalResponseTime / this.stats.totalRequests : 0,
      successRate:
        this.stats.totalRequests > 0 ? this.stats.successfulRequests / this.stats.totalRequests : 0,
      detectionBypassRate:
        this.stats.detectionAttempts > 0
          ? this.stats.bypassedDetections / this.stats.detectionAttempts
          : 0,
    };
  }

  /**
   * 应用反屏蔽配置
   */
  private async applyStealthConfiguration(
    page: Page,
    options: BrowserRequestOptions
  ): Promise<void> {
    // 设置视口
    if (options.viewport) {
      await page.setViewport(options.viewport);
    }

    // 设置随机用户代理
    const userAgent = options.userAgent || this.getRandomUserAgent();
    await page.setUserAgent(userAgent);

    // 设置语言和时区
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    });

    // 设置 Cookie
    if (options.cookies && options.cookies.length > 0) {
      await page.setCookie(...options.cookies);
    }

    // 覆盖 webdriver 属性
    await page.evaluateOnNewDocument(() => {
      // 删除 webdriver 属性
      delete (navigator as any).webdriver;

      // 修改 plugins 长度
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });

      // 修改 languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });

      // 修改 permissions
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = parameters =>
        parameters.name === 'notifications'
          ? Promise.resolve({ state: Notification.permission } as any)
          : originalQuery(parameters);
    });
  }

  /**
   * 检测反爬虫系统
   */
  private async detectAntiBot(page: Page): Promise<boolean> {
    try {
      // 检测常见的反爬虫指标
      const indicators = await page.evaluate(() => {
        const checks = {
          cloudflare:
            !!document.querySelector('[data-ray]') ||
            document.title.includes('Just a moment') ||
            document.body.innerHTML.includes('Checking your browser'),
          captcha:
            !!document.querySelector('.g-recaptcha') ||
            !!document.querySelector('[data-sitekey]') ||
            document.body.innerHTML.toLowerCase().includes('captcha'),
          blocked:
            document.body.innerHTML.toLowerCase().includes('access denied') ||
            document.body.innerHTML.toLowerCase().includes('blocked') ||
            document.title.toLowerCase().includes('403'),
        };

        return checks;
      });

      return indicators.cloudflare || indicators.captcha || indicators.blocked;
    } catch {
      return false;
    }
  }

  /**
   * 尝试绕过反爬虫检测
   */
  private async bypassAntiBot(page: Page, options: BrowserRequestOptions): Promise<boolean> {
    try {
      // 等待一段时间让页面加载完成
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 检查是否有 Cloudflare 挑战
      const hasCloudflare = await page.$('[data-ray]');
      if (hasCloudflare) {
        // 等待 Cloudflare 挑战完成
        await page
          .waitForNavigation({
            waitUntil: 'networkidle0',
            timeout: 30000,
          })
          .catch(() => {});

        // 再次检查是否仍然被阻止
        const stillBlocked = await this.detectAntiBot(page);
        return !stillBlocked;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * 模拟人类行为
   */
  private async simulateHumanBehavior(page: Page): Promise<void> {
    try {
      // 随机滚动
      await page.evaluate(() => {
        const scrollHeight = Math.random() * (document.body.scrollHeight - window.innerHeight);
        window.scrollTo(0, scrollHeight);
      });

      // 随机等待
      await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000));

      // 随机鼠标移动
      const viewport = page.viewport();
      if (viewport) {
        await page.mouse.move(Math.random() * viewport.width, Math.random() * viewport.height);
      }
    } catch (error) {
      warn('Failed to simulate human behavior:', error);
    }
  }

  /**
   * 获取随机用户代理
   */
  private getRandomUserAgent(): string {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    ];

    return userAgents[Math.floor(Math.random() * userAgents.length)] || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
  }

  /**
   * 构建启动选项
   */
  private buildLaunchOptions(options: BrowserRequestOptions): PuppeteerLaunchOptions {
    const launchOptions = { ...this.defaultOptions };

    if (options.headless !== undefined) {
      launchOptions.headless = options.headless;
    }

    if (options.proxy) {
      const proxyArg = `--proxy-server=${options.proxy.protocol || 'http'}://${options.proxy.host}:${options.proxy.port}`;
      launchOptions.args = [...(launchOptions.args || []), proxyArg];
    }

    return launchOptions;
  }

  // 其他辅助方法的实现...
  private async takeScreenshot(page: Page, screenshotOptions: any): Promise<void> {
    // 截图实现
  }

  private buildFetchResponse(
    content: string,
    response: any,
    responseTime: number,
    finalUrl: string
  ): FetchResponse {
    // 构建响应对象
    const headers = response.headers();
    const contentType = headers['content-type'] || 'text/html';
    const size = Buffer.byteLength(content, 'utf8');

    return {
      content,
      statusCode: response.status(),
      headers,
      contentType: contentType.split(';')[0].trim(),
      size,
      responseTime,
      finalUrl: finalUrl,
      fetcherType: 'browser',
      fromCache: false,
    };
  }

  private handleError(error: any, url: string, responseTime: number): ErrorInfo {
    return {
      code: 'BROWSER_FETCH_FAILED',
      message: error instanceof Error ? error.message : 'Unknown browser error',
      timestamp: new Date(),
      details: { url, responseTime },
    };
  }
}
