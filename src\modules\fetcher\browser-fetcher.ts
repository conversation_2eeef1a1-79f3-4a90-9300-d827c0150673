/**
 * 浏览器下载器实现
 */
import puppeteer from 'puppeteer';
import type { <PERSON><PERSON><PERSON>, <PERSON>, LaunchOptions } from 'puppeteer';
import { <PERSON><PERSON><PERSON>er<PERSON><PERSON><PERSON>, BrowserRequestOptions, FetchResponse, ProxyConfig } from '@/interfaces';
import { info, error } from '@/utils';

/**
 * 浏览器实例管理器
 */
class BrowserInstanceManager {
  private browsers: Map<string, Browser> = new Map();
  private maxInstances = 5;
  private instanceCounter = 0;

  public async getBrowser(options: LaunchOptions = {}): Promise<Browser> {
    const key = this.generateBrowserKey(options);

    if (this.browsers.has(key)) {
      const browser = this.browsers.get(key)!;
      if (browser.isConnected()) {
        return browser;
      } else {
        this.browsers.delete(key);
      }
    }

    if (this.browsers.size >= this.maxInstances) {
      await this.closeLeastUsedBrowser();
    }

    const browser = await puppeteer.launch(options);
    this.browsers.set(key, browser);
    return browser;
  }

  public async closeAllBrowsers(): Promise<void> {
    const closePromises = Array.from(this.browsers.values()).map(browser =>
      browser.close().catch(error)
    );
    await Promise.all(closePromises);
    this.browsers.clear();
  }

  public getBrowserCount(): number {
    return this.browsers.size;
  }

  private generateBrowserKey(options: LaunchOptions): string {
    return JSON.stringify({
      headless: options.headless,
      proxy: options.args?.find((arg: any) => arg.startsWith('--proxy-server')),
    });
  }

  private async closeLeastUsedBrowser(): Promise<void> {
    const [firstKey] = this.browsers.keys();
    if (firstKey) {
      const browser = this.browsers.get(firstKey)!;
      await browser.close().catch(error);
      this.browsers.delete(firstKey);
    }
  }
}

/**
 * 浏览器下载器实现类
 */
export class BrowserFetcher implements IBrowserFetcher {
  public readonly name = 'browser-fetcher';
  public readonly type = 'browser' as const;
  public readonly version = '1.0.0';

  private browserManager: BrowserInstanceManager;
  private defaultOptions: LaunchOptions;
  private stats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    totalResponseTime: 0,
  };

  constructor(defaultOptions: LaunchOptions = {}) {
    this.browserManager = new BrowserInstanceManager();
    this.defaultOptions = {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
      ],
      ...defaultOptions,
    };
  }

  /**
   * 执行浏览器请求
   */
  public async fetch(url: string, options: BrowserRequestOptions = {}): Promise<FetchResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    let page: Page | null = null;

    try {
      // 获取浏览器实例
      const launchOptions = this.buildLaunchOptions(options);
      const browser = await this.browserManager.getBrowser(launchOptions);

      // 创建新页面
      page = await browser.newPage();

      // 配置页面
      await this.configurePage(page, options);

      // 导航到页面 - 改进等待策略
      const response = await page.goto(url, {
        waitUntil: options.waitForLoad ? 'networkidle0' : 'domcontentloaded',
        timeout: options.waitTime || 30000,
      });

      if (!response) {
        throw new Error('Failed to load page');
      }

      // 等待页面完全加载 - 新增的智能等待逻辑
      await this.waitForPageLoad(page, options);

      // 等待特定选择器（如果指定）
      if (options.waitForSelector) {
        await page.waitForSelector(options.waitForSelector, {
          timeout: options.waitTime || 30000,
        });
      }

      // 执行自定义脚本（如果指定）
      if (options.executeJavaScript && options.customScript) {
        await page.evaluate(options.customScript);
      }

      // 截图（如果需要）
      if (options.screenshot?.enabled) {
        await this.takeScreenshot(page, options.screenshot);
      }

      // 获取页面内容
      const content = await page.content();
      const finalUrl = page.url();

      const responseTime = Date.now() - startTime;
      this.stats.successfulRequests++;
      this.stats.totalResponseTime += responseTime;

      // 构建响应对象
      return this.buildFetchResponse(content, response, responseTime, finalUrl);
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.stats.failedRequests++;
      this.stats.totalResponseTime += responseTime;

      throw this.handleError(error, url, responseTime);
    } finally {
      if (page) {
        await page.close().catch(error);
      }
    }
  }

  /**
   * 智能等待页面加载完成
   */
  private async waitForPageLoad(page: Page, options: BrowserRequestOptions): Promise<void> {
    const maxWaitTime = options.waitTime || 30000;
    const checkInterval = 500; // 500ms检查间隔
    const startTime = Date.now();

    try {
      // 等待策略1: 等待网络空闲 (使用 Puppeteer 的 networkidle2)
      await Promise.race([
        page.waitForSelector('body', { timeout: 5000 }),
        new Promise(resolve => setTimeout(resolve, Math.min(maxWaitTime, 10000)))
      ]);

      // 等待策略2: 等待常见内容加载指示器
      await Promise.race([
        // 等待页面内容元素出现
        page.waitForSelector('body', { timeout: 5000 }).catch(() => {}),
        // 等待基本HTML结构
        page.waitForFunction(() => {
          return document.readyState === 'complete' &&
                 document.body &&
                 document.body.children.length > 0;
        }, { timeout: 5000 }).catch(() => {}),
        new Promise(resolve => setTimeout(resolve, 5000))
      ]);

      // 等待策略3: 动态内容加载检测
      let previousContentLength = 0;
      let stableCount = 0;
      const requiredStableCount = 3; // 需要连续3次内容长度稳定

      while (Date.now() - startTime < maxWaitTime) {
        try {
          const currentContentLength = await page.evaluate(() => {
            return document.body ? document.body.innerText.length : 0;
          });

          if (currentContentLength === previousContentLength) {
            stableCount++;
            if (stableCount >= requiredStableCount) {
              break; // 内容稳定，退出循环
            }
          } else {
            stableCount = 0;
            previousContentLength = currentContentLength;
          }

          await new Promise(resolve => setTimeout(resolve, checkInterval));
        } catch (error) {
          // 页面可能还没准备好，继续等待
          await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
      }

      // 等待策略4: 等待常见的动态内容加载
      const commonSelectors = [
        'main', 'article', '.content', '#content', 
        '.post', '.article', '.entry', '.container'
      ];

      for (const selector of commonSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2000 });
          break; // 找到主内容区域就退出
        } catch {
          // 继续尝试下一个选择器
        }
      }

      // 最后等待一小段时间确保页面完全渲染
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      // 等待过程中的错误不应该中断整个抓取过程
      info(`Page load wait completed with potential issues: ${error}`);
    }
  }

  /**
   * 检查是否支持该URL
   */
  public canHandle(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * 初始化下载器
   */
  public async initialize(): Promise<void> {
    // 预热一个浏览器实例
    try {
      const browser = await this.browserManager.getBrowser(this.defaultOptions);
      info('Browser Fetcher initialized with browser instance');
    } catch (err) {
      error('Failed to initialize browser:', err);
      throw err;
    }
  }

  /**
   * 清理下载器资源
   */
  public async cleanup(): Promise<void> {
    await this.browserManager.closeAllBrowsers();
    info('Browser Fetcher cleaned up');
  }

  /**
   * 获取浏览器实例数量
   */
  public getBrowserInstanceCount(): number {
    return this.browserManager.getBrowserCount();
  }

  /**
   * 关闭所有浏览器实例
   */
  public async closeAllBrowsers(): Promise<void> {
    await this.browserManager.closeAllBrowsers();
  }

  /**
   * 获取下载器统计信息
   */
  public getStats() {
    return {
      totalRequests: this.stats.totalRequests,
      successfulRequests: this.stats.successfulRequests,
      failedRequests: this.stats.failedRequests,
      averageResponseTime:
        this.stats.totalRequests > 0 ? this.stats.totalResponseTime / this.stats.totalRequests : 0,
    };
  }

  /**
   * 构建启动选项
   */
  private buildLaunchOptions(options: BrowserRequestOptions): LaunchOptions {
    const launchOptions = { ...this.defaultOptions };

    if (options.headless !== undefined) {
      launchOptions.headless = options.headless;
    }

    if (options.proxy) {
      const proxyArg = `--proxy-server=${options.proxy.protocol || 'http'}://${options.proxy.host}:${options.proxy.port}`;
      launchOptions.args = [...(launchOptions.args || []), proxyArg];
    }

    return launchOptions;
  }

  /**
   * 配置页面
   */
  private async configurePage(page: Page, options: BrowserRequestOptions): Promise<void> {
    // 设置视口
    if (options.viewport) {
      await page.setViewport(options.viewport);
    } else {
      await page.setViewport({ width: 1920, height: 1080 });
    }

    // 设置用户代理
    if (options.userAgent) {
      await page.setUserAgent(options.userAgent);
    }

    // 设置 Cookie
    if (options.cookies && options.cookies.length > 0) {
      await page.setCookie(...options.cookies);
    }

    // 设置额外的 HTTP 头
    const headers: Record<string, string> = {};
    if (options.userAgent) {
      headers['User-Agent'] = options.userAgent;
    }

    if (Object.keys(headers).length > 0) {
      await page.setExtraHTTPHeaders(headers);
    }

    // 拦截请求（用于代理认证等）
    if (options.proxy?.auth) {
      await page.authenticate({
        username: options.proxy.auth.username,
        password: options.proxy.auth.password,
      });
    }
  }

  /**
   * 截图
   */
  private async takeScreenshot(
    page: Page,
    screenshotOptions: NonNullable<BrowserRequestOptions['screenshot']>
  ): Promise<void> {
    const options: any = {
      fullPage: screenshotOptions.fullPage || false,
    };

    if (screenshotOptions.path) {
      options.path = screenshotOptions.path;
    }

    await page.screenshot(options);
  }

  /**
   * 构建响应对象
   */
  private buildFetchResponse(
    content: string,
    response: any,
    responseTime: number,
    finalUrl: string
  ): FetchResponse {
    const headers = response.headers() || {};
    const contentType = headers['content-type'] || 'text/html';
    const size = Buffer.byteLength(content, 'utf8');

    return {
      content,
      statusCode: response.status(),
      headers,
      contentType: contentType.split(';')[0].trim(),
      size,
      responseTime,
      finalUrl,
      fromCache: response.fromCache(),
      metadata: {
        browserType: 'puppeteer',
        securityDetails: response.securityDetails(),
        remoteAddress: response.remoteAddress(),
      },
    };
  }

  /**
   * 处理错误
   */
  private handleError(error: any, url: string, responseTime: number): Error {
    let message = 'Browser request failed';
    let code = 'BROWSER_ERROR';

    if (error.message) {
      message = error.message;
    }

    if (error.message?.includes('timeout')) {
      code = 'TIMEOUT';
    } else if (error.message?.includes('net::')) {
      code = 'NETWORK_ERROR';
    }

    const enhancedError = new Error(`${message} (URL: ${url})`);
    (enhancedError as any).code = code;
    (enhancedError as any).url = url;
    (enhancedError as any).responseTime = responseTime;
    (enhancedError as any).originalError = error;

    return enhancedError;
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalResponseTime: 0,
    };
  }

  /**
   * 测试连接
   */
  public async testConnection(url = 'https://httpbin.org/get'): Promise<boolean> {
    try {
      await this.fetch(url, { waitTime: 5000 });
      return true;
    } catch {
      return false;
    }
  }
}
