/**
 * 任务管理路由 - 重构版
 * 
 * 使用新的TaskManager来管理多个独立的爬虫任务
 */

import { Router } from 'express';
import { Server as SocketIOServer } from 'socket.io';
import { TaskManager } from '@/app/task-manager';
import { 
  CreateTaskRequest, 
  CreateTaskResponse,
  TaskListQuery,
  TaskStatus,
  TaskType,
  TaskEventType
} from '@/types/task';
import { error } from '@/utils';

/**
 * 创建任务管理路由
 */
export function createTaskRoutes(
  taskManager: TaskManager,
  io: SocketIOServer
): Router {
  const router = Router();

  // 设置TaskManager事件监听
  setupTaskManagerEvents(taskManager, io);

  /**
   * GET /tasks/stats - 获取爬虫统计数据
   */
  router.get('/stats', async (_req, res) => {
    try {
      const stats = taskManager.getCrawlerStats();
      return res.json(stats);
    } catch (err) {
      error('获取统计数据失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get stats',
      });
    }
  });

  /**
   * POST /tasks - 创建新的爬虫任务
   */
  router.post('/', async (req, res) => {
    try {
      const requestData = req.body;
      
      // 转换为标准格式
      const createRequest: CreateTaskRequest = {
        name: requestData.name,
        type: requestData.type || TaskType.HTML_CRAWLER,
        urls: requestData.urls,
        config: requestData.config
      };

      const taskId = await taskManager.createTask(createRequest);

      const response: CreateTaskResponse = {
        success: true,
        taskId,
        status: TaskStatus.PENDING,
        message: 'Task created successfully.'
      };

      return res.json(response);
    } catch (err) {
      error('创建任务失败:', err);
      const response: CreateTaskResponse = {
        success: false,
        taskId: '',
        status: TaskStatus.FAILED,
        message: err instanceof Error ? err.message : 'Failed to create task'
      };
      return res.status(500).json(response);
    }
  });

  /**
   * GET /tasks - 获取所有任务列表
   */
  router.get('/', async (req, res) => {
    try {
      const query = req.query as TaskListQuery;
      let tasks = taskManager.getAllTasks();

      // 按状态过滤
      if (query.status && query.status.length > 0) {
        tasks = tasks.filter(task => query.status!.includes(task.status));
      }

      // 按类型过滤
      if (query.type && query.type.length > 0) {
        tasks = tasks.filter(task => query.type!.includes(task.type));
      }

      // 按创建时间降序排序
      tasks.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      // 分页
      const limit = Number(query.limit) || 50;
      const offset = Number(query.offset) || 0;
      const paginatedTasks = tasks.slice(offset, offset + limit);

      res.json({
        success: true,
        data: paginatedTasks,
        total: tasks.length,
        limit,
        offset
      });
    } catch (err) {
      error('获取任务列表失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get tasks',
      });
    }
  });

  /**
   * GET /tasks/:taskId - 获取特定任务的状态
   */
  router.get('/:taskId', async (req, res) => {
    try {
      const { taskId } = req.params;
      const task = taskManager.getTask(taskId);

      if (!task) {
        return res.status(404).json({
          success: false,
          error: 'Task not found',
        });
      }

      return res.json(task);
    } catch (err) {
      error('获取任务状态失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get task status',
      });
    }
  });

  /**
   * POST /tasks/:taskId/start - 启动特定任务
   */
  router.post('/:taskId/start', async (req, res) => {
    try {
      const { taskId } = req.params;
      const result = await taskManager.startTask(taskId);
      
      if (result.success) {
        return res.json(result);
      } else {
        return res.status(400).json(result);
      }
    } catch (err) {
      error('启动任务失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to start task',
      });
    }
  });

  /**
   * POST /tasks/:taskId/stop - 停止特定任务
   */
  router.post('/:taskId/stop', async (req, res) => {
    try {
      const { taskId } = req.params;
      const result = await taskManager.stopTask(taskId);
      
      if (result.success) {
        return res.json(result);
      } else {
        return res.status(400).json(result);
      }
    } catch (err) {
      error('停止任务失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to stop task',
      });
    }
  });

  /**
   * POST /tasks/:taskId/pause - 暂停特定任务
   */
  router.post('/:taskId/pause', async (req, res) => {
    try {
      const { taskId } = req.params;
      const result = await taskManager.pauseTask(taskId);
      
      if (result.success) {
        return res.json(result);
      } else {
        return res.status(400).json(result);
      }
    } catch (err) {
      error('暂停任务失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to pause task',
      });
    }
  });

  /**
   * POST /tasks/:taskId/resume - 恢复特定任务
   */
  router.post('/:taskId/resume', async (req, res) => {
    try {
      const { taskId } = req.params;
      const result = await taskManager.resumeTask(taskId);
      
      if (result.success) {
        return res.json(result);
      } else {
        return res.status(400).json(result);
      }
    } catch (err) {
      error('恢复任务失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to resume task',
      });
    }
  });

  /**
   * GET /tasks/debug - 调试存储数据
   */
  router.get('/debug', async (req, res) => {
    try {
      console.log('=== DEBUG API CALLED ===');
      const results = await taskManager.getTaskResults('any-task-id');
      return res.json({
        success: true,
        message: 'Debug endpoint',
        results,
        total: results.length
      });
    } catch (err) {
      return res.json({
        success: false,
        error: err instanceof Error ? err.message : 'Debug error'
      });
    }
  });

  /**
   * GET /tasks/:taskId/results - 获取任务的抓取结果
   */
  router.get('/:taskId/results', async (req, res) => {
    try {
      const { taskId } = req.params;
      const task = taskManager.getTask(taskId);

      if (!task) {
        return res.status(404).json({
          success: false,
          error: 'Task not found',
        });
      }

      // 获取任务结果
      const results = await taskManager.getTaskResults(taskId);
      
      return res.json({
        success: true,
        results,
        total: results.length,
        debug: `Requested taskId: ${taskId}`
      });
    } catch (err) {
      error('获取任务结果失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get task results',
      });
    }
  });

  /**
   * POST /tasks/:taskId/export - 导出任务结果
   */
  router.post('/:taskId/export', async (req, res) => {
    try {
      const { taskId } = req.params;
      const { format = 'json' } = req.body;
      const task = taskManager.getTask(taskId);

      if (!task) {
        return res.status(404).json({
          success: false,
          error: 'Task not found',
        });
      }

      // 获取任务结果
      const results = await taskManager.getTaskResults(taskId);
      
      if (format === 'json') {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="${task.name}_results.json"`);
        return res.json({
          taskId,
          taskName: task.name,
          exportTime: new Date().toISOString(),
          results,
        });
      } else if (format === 'csv') {
        // 简单的CSV导出
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${task.name}_results.csv"`);
        
        const csvHeader = 'URL,Title,Status,Content,Timestamp\n';
        const csvRows = results.map((result: any) => {
          const url = `"${result.url || ''}"`;
          const title = `"${(result.title || '').replace(/"/g, '""')}"`;
          const status = result.status || 'unknown';
          const content = `"${(result.content || '').replace(/"/g, '""').substring(0, 500)}"`;
          const timestamp = result.timestamp || '';
          return `${url},${title},${status},${content},${timestamp}`;
        }).join('\n');
        
        return res.send(csvHeader + csvRows);
      } else {
        return res.status(400).json({
          success: false,
          error: 'Unsupported format. Use "json" or "csv".',
        });
      }
    } catch (err) {
      error('导出任务结果失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to export task results',
      });
    }
  });

  /**
   * DELETE /tasks/:taskId - 删除特定任务
   */
  router.delete('/:taskId', async (req, res) => {
    try {
      const { taskId } = req.params;
      const result = await taskManager.deleteTask(taskId);
      
      if (result.success) {
        return res.json(result);
      } else {
        return res.status(400).json(result);
      }
    } catch (err) {
      error('删除任务失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to delete task',
      });
    }
  });

  return router;
}

/**
 * 设置TaskManager事件监听，将事件转发到WebSocket
 */
function setupTaskManagerEvents(taskManager: TaskManager, io: SocketIOServer): void {
  taskManager.on('taskCreated', (task) => {
    io.emit(TaskEventType.TASK_CREATED, {
      type: TaskEventType.TASK_CREATED,
      taskId: task.id,
      task,
      timestamp: new Date().toISOString()
    });
  });

  taskManager.on('taskStatusChanged', (task) => {
    let eventType: TaskEventType;
    switch (task.status) {
      case TaskStatus.RUNNING:
        eventType = TaskEventType.TASK_STARTED;
        break;
      case TaskStatus.STOPPED:
        eventType = TaskEventType.TASK_STOPPED;
        break;
      case TaskStatus.PAUSED:
        eventType = TaskEventType.TASK_PAUSED;
        break;
      case TaskStatus.COMPLETED:
        eventType = TaskEventType.TASK_COMPLETED;
        break;
      case TaskStatus.FAILED:
        eventType = TaskEventType.TASK_FAILED;
        break;
      default:
        return;
    }

    io.emit(eventType, {
      type: eventType,
      taskId: task.id,
      task,
      timestamp: new Date().toISOString()
    });
  });

  taskManager.on('taskStatsUpdated', (task) => {
    io.emit(TaskEventType.TASK_STATS_UPDATED, {
      type: TaskEventType.TASK_STATS_UPDATED,
      taskId: task.id,
      stats: task.stats,
      timestamp: new Date().toISOString()
    });
  });

  taskManager.on('urlProcessed', (data) => {
    io.emit('url_processed', data);
  });

  taskManager.on('dataExtracted', (data) => {
    io.emit('data_extracted', data);
  });

  taskManager.on('taskError', (data) => {
    io.emit(TaskEventType.TASK_FAILED, {
      type: TaskEventType.TASK_FAILED,
      taskId: data.taskId,
      error: data.error,
      timestamp: new Date().toISOString()
    });
  });

  taskManager.on('taskCompleted', (task) => {
    io.emit(TaskEventType.TASK_COMPLETED, {
      type: TaskEventType.TASK_COMPLETED,
      taskId: task.id,
      task,
      timestamp: new Date().toISOString()
    });
  });

  taskManager.on('taskDeleted', (data) => {
    io.emit(TaskEventType.TASK_DELETED, {
      type: TaskEventType.TASK_DELETED,
      taskId: data.taskId,
      timestamp: new Date().toISOString()
    });
  });
}