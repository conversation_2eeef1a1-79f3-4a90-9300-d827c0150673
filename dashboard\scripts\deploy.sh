#!/bin/bash

# Nodex 监控面板前端部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_requirements() {
    log_info "检查部署环境..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    # 检查 Node.js 版本
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本过低，需要 18.0+，当前版本: $(node -v)"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    log_success "依赖安装完成"
}

# 类型检查
type_check() {
    log_info "执行 TypeScript 类型检查..."
    
    npm run type-check
    
    log_success "类型检查通过"
}

# 代码规范检查
lint_check() {
    log_info "执行代码规范检查..."
    
    npm run lint
    
    log_success "代码规范检查通过"
}

# 构建项目
build_project() {
    log_info "构建生产版本..."
    
    # 设置生产环境变量
    export NODE_ENV=production
    
    npm run build
    
    log_success "项目构建完成"
}

# 启动服务
start_service() {
    local mode=$1
    
    if [ "$mode" = "dev" ]; then
        log_info "启动开发服务器..."
        npm run dev
    else
        log_info "启动生产服务器..."
        npm start
    fi
}

# Docker 部署
docker_deploy() {
    log_info "使用 Docker 部署..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 构建 Docker 镜像
    log_info "构建 Docker 镜像..."
    docker build -t nodex-monitoring-frontend:latest .
    
    # 停止现有容器
    log_info "停止现有容器..."
    docker stop nodex-monitoring-frontend 2>/dev/null || true
    docker rm nodex-monitoring-frontend 2>/dev/null || true
    
    # 启动新容器
    log_info "启动新容器..."
    docker run -d \
        --name nodex-monitoring-frontend \
        -p 3002:3002 \
        --env-file .env.local \
        --restart unless-stopped \
        nodex-monitoring-frontend:latest
    
    log_success "Docker 部署完成"
}

# 健康检查
health_check() {
    local url=${1:-"http://localhost:3002"}
    local max_attempts=30
    local attempt=1
    
    log_info "执行健康检查..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null; then
            log_success "服务健康检查通过"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "服务健康检查失败"
    return 1
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 清理 node_modules（可选）
    if [ "$1" = "full" ]; then
        rm -rf node_modules
        rm -rf .next
        log_info "完全清理完成"
    else
        rm -rf .next
        log_info "构建缓存清理完成"
    fi
}

# 显示帮助信息
show_help() {
    echo "Nodex 监控面板前端部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  dev         启动开发服务器"
    echo "  build       构建生产版本"
    echo "  start       启动生产服务器"
    echo "  deploy      完整部署流程（检查、构建、启动）"
    echo "  docker      使用 Docker 部署"
    echo "  check       仅执行环境和代码检查"
    echo "  clean       清理构建缓存"
    echo "  clean-all   完全清理（包括 node_modules）"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev      # 开发模式"
    echo "  $0 deploy   # 生产部署"
    echo "  $0 docker   # Docker 部署"
}

# 主函数
main() {
    local command=${1:-"help"}
    
    case $command in
        "dev")
            check_requirements
            install_dependencies
            start_service "dev"
            ;;
        "build")
            check_requirements
            install_dependencies
            type_check
            lint_check
            build_project
            ;;
        "start")
            start_service "prod"
            ;;
        "deploy")
            check_requirements
            install_dependencies
            type_check
            lint_check
            build_project
            start_service "prod" &
            sleep 5
            health_check
            ;;
        "docker")
            docker_deploy
            sleep 10
            health_check
            ;;
        "check")
            check_requirements
            install_dependencies
            type_check
            lint_check
            ;;
        "clean")
            cleanup
            ;;
        "clean-all")
            cleanup "full"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
