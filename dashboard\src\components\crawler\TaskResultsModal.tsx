import React, { useState, useEffect } from 'react';
import { X, Download, ExternalLink, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

interface TaskResult {
  id: string;
  url: string;
  title: string;
  content: string;
  extractedData?: any;
  status: 'success' | 'failed';
  timestamp: string;
  metadata?: {
    responseTime: number;
    statusCode: number;
    contentType: string;
  };
}

interface TaskResultsModalProps {
  isOpen: boolean;
  taskId: string;
  taskName: string;
  onClose: () => void;
}

export const TaskResultsModal: React.FC<TaskResultsModalProps> = ({
  isOpen,
  taskId,
  taskName,
  onClose,
}) => {
  const [results, setResults] = useState<TaskResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'success' | 'failed'>('all');
  const [selectedResult, setSelectedResult] = useState<TaskResult | null>(null);

  useEffect(() => {
    if (isOpen && taskId) {
      fetchResults();
    }
  }, [isOpen, taskId]);

  const fetchResults = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/tasks/${taskId}/results`);
      const data = await response.json();
      
      if (data.success) {
        setResults(data.results || []);
      }
    } catch (error) {
      console.error('Failed to fetch task results:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportResults = async () => {
    try {
      const response = await fetch(`/api/v1/tasks/${taskId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ format: 'json' }),
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${taskName}_results.json`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to export results:', error);
    }
  };

  const filteredResults = results.filter(result => {
    const matchesSearch = result.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         result.url.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || result.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* 弹框内容 */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] flex flex-col">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">任务结果</h2>
            <p className="text-sm text-gray-500 mt-1">{taskName}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={exportResults}
              variant="secondary"
              size="sm"
              icon={<Download className="h-4 w-4" />}
            >
              导出结果
            </Button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* 搜索和过滤 */}
        <div className="p-6 border-b bg-gray-50">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索标题或URL..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">全部状态</option>
              <option value="success">成功</option>
              <option value="failed">失败</option>
            </select>
          </div>
        </div>

        {/* 结果列表 */}
        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">加载中...</span>
            </div>
          ) : filteredResults.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500">
              <Search className="h-12 w-12 mb-4" />
              <p className="text-lg">暂无结果</p>
              <p className="text-sm">该任务还没有抓取到任何数据</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
              {/* 左侧：结果列表 */}
              <div className="overflow-y-auto border-r">
                <div className="p-4 space-y-3">
                  {filteredResults.map((result) => (
                    <Card 
                      key={result.id} 
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedResult?.id === result.id ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => setSelectedResult(result)}
                    >
                      <div className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="font-medium text-gray-900 truncate flex-1">
                            {result.title || '未知标题'}
                          </h3>
                          <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                            result.status === 'success' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {result.status === 'success' ? '成功' : '失败'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 truncate mb-2">
                          {result.url}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(result.timestamp).toLocaleString('zh-CN')}
                        </p>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>

              {/* 右侧：详细内容 */}
              <div className="overflow-y-auto">
                {selectedResult ? (
                  <div className="p-4">
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-medium text-gray-900 mb-2">基本信息</h3>
                        <div className="bg-gray-50 p-3 rounded-md space-y-2">
                          <div className="flex items-center space-x-2">
                            <ExternalLink className="h-4 w-4 text-gray-400" />
                            <a 
                              href={selectedResult.url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 truncate"
                            >
                              {selectedResult.url}
                            </a>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p><strong>标题:</strong> {selectedResult.title}</p>
                            <p><strong>状态:</strong> {selectedResult.status === 'success' ? '成功' : '失败'}</p>
                            <p><strong>时间:</strong> {new Date(selectedResult.timestamp).toLocaleString('zh-CN')}</p>
                            {selectedResult.metadata && (
                              <>
                                <p><strong>状态码:</strong> {selectedResult.metadata.statusCode}</p>
                                <p><strong>响应时间:</strong> {selectedResult.metadata.responseTime}ms</p>
                                <p><strong>内容类型:</strong> {selectedResult.metadata.contentType}</p>
                              </>
                            )}
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="font-medium text-gray-900 mb-2">抓取内容</h3>
                        <div className="bg-gray-50 p-3 rounded-md max-h-96 overflow-y-auto">
                          <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                            {selectedResult.content || '暂无内容'}
                          </pre>
                        </div>
                      </div>

                      {selectedResult.extractedData && (
                        <div>
                          <h3 className="font-medium text-gray-900 mb-2">提取数据</h3>
                          <div className="bg-gray-50 p-3 rounded-md max-h-96 overflow-y-auto">
                            <pre className="text-sm text-gray-700">
                              {JSON.stringify(selectedResult.extractedData, null, 2)}
                            </pre>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <p>选择一个结果查看详细信息</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 底部统计 */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>总计 {results.length} 个结果</span>
            <span>
              成功: {results.filter(r => r.status === 'success').length} | 
              失败: {results.filter(r => r.status === 'failed').length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskResultsModal;