import 'dotenv/config';
import { resolve } from 'path';
import { ApiServer } from './api/server';
import { AppManager } from './app/app-manager';
import { info, error, errorWithStack, getLogger } from '@/utils';

const appLogger = getLogger('App');

async function main(): Promise<void> {
  const startTime = Date.now();

  try {
    info(
      '🚀 启动 Nodex API 服务器...',
      {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
      },
      'App'
    );

    const appManager = new AppManager();

    await appManager.initialize();

    const serverConfig = {
      port: parseInt(process.env.API_PORT ?? '3000', 10),
      host: process.env.API_HOST ?? '0.0.0.0',
      enableCors: process.env.ENABLE_CORS !== 'false',
      corsOrigin: process.env.CORS_ORIGIN ?? '*',
      staticPath:
        process.env.STATIC_PATH ??
        (process.env.NODE_ENV === 'production'
          ? resolve(__dirname, 'public') // 生产环境：使用构建后的静态文件
          : resolve(__dirname, '../dashboard/dist')), // 开发环境：使用前端构建目录
    };

    // 从 AppManager 获取依赖项
    const taskManager = appManager.getTaskManager()!;
    const serverDependencies = {
      fetcherManager: taskManager.getDependencies().fetcherManager,
      parserManager: taskManager.getDependencies().parserManager,
      storageManager: taskManager.getDependencies().storageManager,
    };

    appLogger.debug('服务器配置', serverConfig);

    const apiServer = new ApiServer(serverConfig, serverDependencies);
    await apiServer.start();

    const startupTime = Date.now() - startTime;
    info(`✅ 服务器启动成功，耗时 ${startupTime}ms`, { startupTime, config: serverConfig }, 'App');

    const gracefulShutdown = async (signal: string) => {
      info(`📡 接收到 ${signal} 信号，正在关闭服务器...`, { signal }, 'App');
      try {
        const shutdownStart = Date.now();

        // 先停止API服务器
        await apiServer.stop();

        // 再停止应用管理器
        await appManager.cleanup();

        const shutdownTime = Date.now() - shutdownStart;
        info(`✅ 服务器已成功关闭，耗时 ${shutdownTime}ms`, { shutdownTime, signal }, 'App');
        process.exit(0);
      } catch (shutdownError) {
        errorWithStack('❌ 关闭服务器时发生错误', shutdownError as Error, 'App');
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    process.on('uncaughtException', async uncaughtError => {
      errorWithStack('❌ 未捕获的异常', uncaughtError, 'App');
      await gracefulShutdown('uncaughtException');
    });

    process.on('unhandledRejection', async (reason, promise) => {
      error(
        '❌ 未处理的 Promise 拒绝',
        {
          reason: reason instanceof Error ? reason.message : reason,
          stack: reason instanceof Error ? reason.stack : undefined,
          promise: promise.toString(),
        },
        'App'
      );
      await gracefulShutdown('unhandledRejection');
    });
  } catch (startupError) {
    errorWithStack('❌ 启动 API 服务器失败', startupError as Error, 'App');
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(mainError => {
    errorWithStack('❌ 主程序启动失败', mainError as Error, 'App');
    process.exit(1);
  });
}
