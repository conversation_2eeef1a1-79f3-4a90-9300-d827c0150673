/**
 * Task-specific Socket.IO Hook (legacy compatibility)
 * 这个hook保持向后兼容性，实际上使用统一的useSocket hook
 */

import { useSocket } from './useSocket';
import type { TaskEvent } from '@/types/task';

export function useTaskSocket(taskId?: string) {
  const { on, off, emit, isConnected, connectionError } = useSocket();

  // Task-specific event handlers
  const onTaskEvent = (eventType: string, handler: (event: TaskEvent) => void) => {
    on(eventType as keyof any, handler);
  };

  const offTaskEvent = (eventType: string, handler: (event: TaskEvent) => void) => {
    off(eventType as keyof any, handler);
  };

  const emitTaskEvent = (eventType: string, data: any) => {
    emit(eventType as keyof any, data);
  };

  return {
    on: onTaskEvent,
    off: offTaskEvent,
    emit: emitTaskEvent,
    isConnected,
    connectionError,
  };
}

export default useTaskSocket;