/**
 * TypeScript 工具类型定义
 */

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 非空类型
 */
export type NonNullable<T> = T extends null | undefined ? never : T;

/**
 * 提取 Promise 类型
 */
export type Awaited<T> = T extends Promise<infer U> ? U : T;

/**
 * 函数参数类型
 */
export type Parameters<T extends (...args: any) => any> = T extends (...args: infer P) => any
  ? P
  : never;

/**
 * 函数返回类型
 */
export type ReturnType<T extends (...args: any) => any> = T extends (...args: any) => infer R
  ? R
  : any;

/**
 * 构造函数类型
 */
export type Constructor<T = {}> = new (...args: any[]) => T;

/**
 * 抽象构造函数类型
 */
export type AbstractConstructor<T = {}> = abstract new (...args: any[]) => T;

/**
 * 可选键类型
 */
export type OptionalKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
}[keyof T];

/**
 * 必需键类型
 */
export type RequiredKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
}[keyof T];

/**
 * 值类型联合
 */
export type ValueOf<T> = T[keyof T];

/**
 * 键值对类型
 */
export type Entry<T> = {
  [K in keyof T]: [K, T[K]];
}[keyof T];

/**
 * 条件类型
 */
export type If<C extends boolean, T, F> = C extends true ? T : F;

/**
 * 数组元素类型
 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never;

/**
 * 对象路径类型
 */
export type Path<T> = T extends object
  ? {
      [K in keyof T]: K extends string
        ? T[K] extends object
          ? K | `${K}.${Path<T[K]>}`
          : K
        : never;
    }[keyof T]
  : never;

/**
 * 根据路径获取值类型
 */
export type PathValue<T, P extends Path<T>> = P extends keyof T
  ? T[P]
  : P extends `${infer K}.${infer Rest}`
    ? K extends keyof T
      ? Rest extends Path<T[K]>
        ? PathValue<T[K], Rest>
        : never
      : never
    : never;

/**
 * 排除类型
 */
export type Except<T, K extends keyof T> = Omit<T, K>;

/**
 * 合并类型
 */
export type Merge<T, U> = Omit<T, keyof U> & U;

/**
 * 覆盖类型
 */
export type Override<T, U> = Merge<T, U>;

/**
 * 严格提取类型
 */
export type StrictExtract<T, U extends T> = Extract<T, U>;

/**
 * 严格排除类型
 */
export type StrictExclude<T, U extends T> = Exclude<T, U>;

/**
 * 可为空类型
 */
export type Nullable<T> = T | null;

/**
 * 可为未定义类型
 */
export type Optional<T> = T | undefined;

/**
 * 可为空或未定义类型
 */
export type Maybe<T> = T | null | undefined;

/**
 * 字符串字面量类型
 */
export type StringLiteral<T> = T extends string ? (string extends T ? never : T) : never;

/**
 * 数字字面量类型
 */
export type NumberLiteral<T> = T extends number ? (number extends T ? never : T) : never;

/**
 * 布尔字面量类型
 */
export type BooleanLiteral<T> = T extends boolean ? (boolean extends T ? never : T) : never;

/**
 * 原始类型
 */
export type Primitive = string | number | boolean | symbol | null | undefined;

/**
 * 非原始类型
 */
export type NonPrimitive = object;

/**
 * 序列化类型
 */
export type Serializable =
  | string
  | number
  | boolean
  | null
  | undefined
  | Serializable[]
  | { [key: string]: Serializable };

/**
 * JSON 类型
 */
export type JSONValue = string | number | boolean | null | JSONObject | JSONArray;
export interface JSONObject {
  [key: string]: JSONValue;
}
export interface JSONArray extends Array<JSONValue> {}

/**
 * 事件映射类型
 */
export type EventMap = Record<string, any>;

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (event: T) => void | Promise<void>;

/**
 * 事件发射器类型
 */
export interface EventEmitter<T extends EventMap = EventMap> {
  on<K extends keyof T>(event: K, listener: EventListener<T[K]>): this;
  off<K extends keyof T>(event: K, listener: EventListener<T[K]>): this;
  emit<K extends keyof T>(event: K, data: T[K]): boolean;
}

/**
 * 异步迭代器类型
 */
export type AsyncIterableIterator<T> = AsyncIterator<T> & AsyncIterable<T>;

/**
 * 生成器类型
 */
export type Generator<T = unknown, TReturn = any, TNext = unknown> = Iterator<T, TReturn, TNext> &
  Iterable<T>;

/**
 * 异步生成器类型
 */
export type AsyncGenerator<T = unknown, TReturn = any, TNext = unknown> = AsyncIterator<
  T,
  TReturn,
  TNext
> &
  AsyncIterable<T>;

/**
 * 类型守卫函数
 */
export type TypeGuard<T> = (value: unknown) => value is T;

/**
 * 断言函数
 */
export type AssertionFunction<T> = (value: unknown) => asserts value is T;

/**
 * 验证结果类型
 */
export interface ValidationResult<T = any> {
  success: boolean;
  data?: T;
  errors?: string[];
}

/**
 * 结果类型 (成功或失败)
 */
export type Result<T, E = Error> = Success<T> | Failure<E>;

export interface Success<T> {
  success: true;
  data: T;
}

export interface Failure<E> {
  success: false;
  error: E;
}
