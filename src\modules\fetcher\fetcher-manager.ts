/**
 * 下载器管理器实现
 */

import {
  IFetcherManager,
  IFetcher,
  IHttpFetcher,
  IBrowserFetcher,
  FetchResponse,
  HttpRequestOptions,
  BrowserRequestOptions,
  ProxyConfig,
} from '@/interfaces';
import { HttpFetcher } from './http-fetcher';
import { getLogger, info, error } from '@/utils';
// import { EnhancedHttpFetcher } from './enhanced-http-fetcher'; // 暂时禁用
import { BrowserFetcher } from './browser-fetcher';
import { StealthBrowserFetcher } from './stealth-browser-fetcher';
import { ProxyManager } from './proxy-manager';
// import { SmartFetcherSelector, SelectionStrategy } from './smart-fetcher-selector';

/**
 * 下载器管理器实现类
 */
export class FetcherManager implements IFetcherManager {
  private fetchers: Map<string, IFetcher> = new Map();
  private proxyManager: ProxyManager | null = null;
  private defaultHttpFetcher: IHttpFetcher | null = null;
  // private enhancedHttpFetcher: EnhancedHttpFetcher | null = null; // 暂时禁用
  private defaultBrowserFetcher: IBrowserFetcher | null = null;
  private stealthBrowserFetcher: StealthBrowserFetcher | null = null;
  private useSmartSelection = false;
  private logger = getLogger('FetcherManager');
  private stats = {
    totalRequests: 0,
    httpRequests: 0,
    browserRequests: 0,
    stealthRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    totalResponseTime: 0,

  };

  constructor() {
    // 注册默认的下载器
    this.defaultHttpFetcher = new HttpFetcher();
    // this.enhancedHttpFetcher = new EnhancedHttpFetcher(); // 暂时禁用
    this.defaultBrowserFetcher = new BrowserFetcher();
    this.stealthBrowserFetcher = new StealthBrowserFetcher();

    this.registerFetcher(this.defaultHttpFetcher);
    // this.registerFetcher(this.enhancedHttpFetcher); // 暂时禁用
    this.registerFetcher(this.defaultBrowserFetcher);
    this.registerFetcher(this.stealthBrowserFetcher);
  }

  /**
   * 注册下载器
   */
  public registerFetcher(fetcher: IFetcher): void {
    this.fetchers.set(fetcher.name, fetcher);
    this.logger.info('注册下载器', {
      name: fetcher.name,
      type: fetcher.type
    });
  }

  /**
   * 注销下载器
   */
  public unregisterFetcher(name: string): void {
    const fetcher = this.fetchers.get(name);
    if (fetcher) {
      this.fetchers.delete(name);
      this.logger.info('注销下载器', { name });
    }
  }

  /**
   * 获取适用的下载器
   */
  public getFetcher(
    url: string,
    preferredType?: 'http' | 'browser',
    options: HttpRequestOptions | BrowserRequestOptions = {}
  ): IFetcher | null {
    // 简化的选择逻辑

    // 回退到传统选择逻辑
    if (preferredType) {
      for (const fetcher of this.fetchers.values()) {
        if (fetcher.type === preferredType && fetcher.canHandle(url)) {
          return fetcher;
        }
      }
    }

    // 否则找到第一个能处理该URL的下载器
    for (const fetcher of this.fetchers.values()) {
      if (fetcher.canHandle(url)) {
        return fetcher;
      }
    }

    return null;
  }

  /**
   * 执行下载
   */
  public async fetch(
    url: string,
    options: HttpRequestOptions | BrowserRequestOptions = {},
    preferredType?: 'http' | 'browser'
  ): Promise<FetchResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      // 获取适用的下载器
      const fetcher = this.getFetcher(url, preferredType, options);
      if (!fetcher) {
        throw new Error(`No suitable fetcher found for URL: ${url}`);
      }

      // 如果启用了代理，添加代理配置
      if (this.proxyManager) {
        const proxy = this.proxyManager.getProxy();
        if (proxy) {
          (options as any).proxy = proxy;
        }
      }

      // 执行下载
      const response = await fetcher.fetch(url, options);

      // 更新统计信息
      const responseTime = Date.now() - startTime;
      this.stats.successfulRequests++;
      this.stats.totalResponseTime += responseTime;

      if (fetcher.type === 'http') {
        this.stats.httpRequests++;
      } else if (fetcher.type === 'browser') {
        if (fetcher.name === 'stealth-browser-fetcher') {
          this.stats.stealthRequests++;
        } else {
          this.stats.browserRequests++;
        }
      }

      // 记录成功统计
      this.stats.successfulRequests++;

      // 如果使用了代理且请求成功，标记代理为成功
      if (this.proxyManager && (options as any).proxy) {
        this.proxyManager.markProxySuccess((options as any).proxy);
      }

      return response;
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.stats.failedRequests++;

      // 记录失败结果到智能选择器
      // 记录失败统计
      this.stats.failedRequests++;

      // 如果使用了代理且请求失败，标记代理为失败
      if (this.proxyManager && (options as any).proxy) {
        this.proxyManager.markProxyFailed((options as any).proxy, {
          code: 'FETCH_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date(),
        });
      }

      throw error;
    }
  }

  /**
   * 获取所有已注册的下载器
   */
  public getRegisteredFetchers(): Map<string, IFetcher> {
    return new Map(this.fetchers);
  }

  /**
   * 初始化所有下载器
   */
  public async initialize(): Promise<void> {
    const initPromises = Array.from(this.fetchers.values()).map(async (fetcher) => {
      if (fetcher.initialize) {
        try {
          await fetcher.initialize();
        } catch (error) {
          this.logger.error('下载器初始化失败', {
            name: fetcher.name,
            error: error instanceof Error ? error.message : error,
          });
        }
      }
    });

    await Promise.all(initPromises);
    this.logger.info('所有下载器初始化完成', {
      count: this.fetchers.size,
    });
  }

  /**
   * 清理所有下载器
   */
  public async cleanup(): Promise<void> {
    const cleanupPromises = Array.from(this.fetchers.values()).map(async (fetcher) => {
      if (fetcher.cleanup) {
        try {
          await fetcher.cleanup();
        } catch (err) {
          error(`Failed to cleanup fetcher ${fetcher.name}:`, err);
        }
      }
    });

    await Promise.all(cleanupPromises);
    info('All fetchers cleaned up');
  }

  /**
   * 获取下载器统计信息
   */
  public getStats(): Record<string, any> {
    const fetcherStats: Record<string, any> = {};
    
    // 收集各个下载器的统计信息
    for (const [name, fetcher] of this.fetchers) {
      if (fetcher.getStats) {
        fetcherStats[name] = fetcher.getStats();
      }
    }

    return {
      overall: {
        totalRequests: this.stats.totalRequests,
        httpRequests: this.stats.httpRequests,
        browserRequests: this.stats.browserRequests,
        stealthRequests: this.stats.stealthRequests,
        successfulRequests: this.stats.successfulRequests,
        failedRequests: this.stats.failedRequests,

        averageResponseTime: this.stats.totalRequests > 0
          ? this.stats.totalResponseTime / this.stats.totalRequests
          : 0,
        successRate: this.stats.totalRequests > 0
          ? this.stats.successfulRequests / this.stats.totalRequests
          : 0,
      },
      fetchers: fetcherStats,
      proxy: this.proxyManager ? {
        totalProxies: this.proxyManager.getAllProxies().length,
        availableProxies: this.proxyManager.getAvailableProxyCount(),
        healthyProxies: this.proxyManager.getHealthyProxyCount(),
        failedProxies: this.proxyManager.getFailedProxyCount(),
        strategy: this.proxyManager.getStrategy(),
      } : null,
    };
  }

  /**
   * 设置代理管理器
   */
  public setProxyManager(proxyManager: ProxyManager): void {
    this.proxyManager = proxyManager;
  }

  /**
   * 获取代理管理器
   */
  public getProxyManager(): ProxyManager | null {
    return this.proxyManager;
  }

  /**
   * 添加代理
   */
  public addProxy(proxy: ProxyConfig): void {
    if (!this.proxyManager) {
      throw new Error('Proxy manager is not set');
    }
    this.proxyManager.addProxy(proxy);
  }

  /**
   * 批量添加代理
   */
  public addProxies(proxies: ProxyConfig[]): void {
    if (!this.proxyManager) {
      throw new Error('Proxy manager is not set');
    }
    this.proxyManager.addProxies(proxies);
  }

  /**
   * 移除代理
   */
  public removeProxy(proxy: ProxyConfig): void {
    if (!this.proxyManager) {
      throw new Error('Proxy manager is not set');
    }
    this.proxyManager.removeProxy(proxy);
  }

  /**
   * 获取HTTP下载器
   */
  public getHttpFetcher(): IHttpFetcher | null {
    return this.defaultHttpFetcher;
  }

  /**
   * 获取浏览器下载器
   */
  public getBrowserFetcher(): IBrowserFetcher | null {
    return this.defaultBrowserFetcher;
  }

  /**
   * 测试下载器连接
   */
  public async testFetchers(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    for (const [name, fetcher] of this.fetchers) {
      try {
        if ('testConnection' in fetcher && typeof fetcher.testConnection === 'function') {
          results[name] = await (fetcher as any).testConnection();
        } else {
          // 如果没有 testConnection 方法，尝试简单的请求
          await fetcher.fetch('https://httpbin.org/get', { timeout: 5000 } as any);
          results[name] = true;
        }
      } catch {
        results[name] = false;
      }
    }
    
    return results;
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalRequests: 0,
      httpRequests: 0,
      browserRequests: 0,
      stealthRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalResponseTime: 0,

    };

    // 重置各个下载器的统计信息
    for (const fetcher of this.fetchers.values()) {
      if ('resetStats' in fetcher && typeof fetcher.resetStats === 'function') {
        (fetcher as any).resetStats();
      }
    }
  }

  /**
   * 获取下载器数量
   */
  public getFetcherCount(): number {
    return this.fetchers.size;
  }

  /**
   * 检查下载器是否存在
   */
  public hasFetcher(name: string): boolean {
    return this.fetchers.has(name);
  }

  /**
   * 获取下载器类型分布
   */
  public getFetcherTypeDistribution(): Record<string, number> {
    const distribution: Record<string, number> = {};

    for (const fetcher of this.fetchers.values()) {
      distribution[fetcher.type] = (distribution[fetcher.type] || 0) + 1;
    }

    return distribution;
  }

  /**
   * 启用/禁用智能选择器
   */
  public setSmartSelectionEnabled(enabled: boolean): void {
    this.useSmartSelection = enabled;
    info(`Smart fetcher selection ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * 启用/禁用智能选择
   */
  public setSmartSelection(enabled: boolean): void {
    this.useSmartSelection = enabled;
    info(`Smart selection ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * 获取反屏蔽下载器
   */
  public getStealthFetcher(): StealthBrowserFetcher | null {
    return this.stealthBrowserFetcher;
  }

  /**
   * 强制使用反屏蔽下载器
   */
  public async fetchWithStealth(
    url: string,
    options: BrowserRequestOptions = {}
  ): Promise<FetchResponse> {
    if (!this.stealthBrowserFetcher) {
      throw new Error('Stealth browser fetcher is not available');
    }

    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      const response = await this.stealthBrowserFetcher.fetch(url, options);

      const responseTime = Date.now() - startTime;
      this.stats.successfulRequests++;
      this.stats.stealthRequests++;
      this.stats.totalResponseTime += responseTime;

      // 记录成功结果
      this.stats.successfulRequests++;

      return response;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.stats.failedRequests++;
      this.stats.totalResponseTime += responseTime;

      // 记录失败结果
      this.stats.failedRequests++;

      throw error;
    }
  }

  /**
   * 检查URL是否需要反屏蔽处理
   */
  public shouldUseStealth(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname.toLowerCase();

      // 检查是否是已知的反爬虫网站
      const protectedDomains = [
        'cloudflare.com',
        'amazonaws.com',
        'google.com',
        'facebook.com',
        'twitter.com',
        'linkedin.com',
      ];

      return protectedDomains.some(protectedDomain =>
        domain.includes(protectedDomain) || domain.endsWith(protectedDomain)
      );
    } catch {
      return false;
    }
  }
}
