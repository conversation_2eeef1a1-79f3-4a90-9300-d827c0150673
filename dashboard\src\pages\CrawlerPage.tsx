/**
 * HTML爬虫页面
 * 
 * 整合爬虫任务创建、任务列表和结果查看
 */

import React, { useState, useEffect } from 'react';
import { CrawlerTaskForm } from '@/components/crawler/CrawlerTaskForm';
import { TaskList } from '@/components/crawler/TaskList';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useNotification } from '@/contexts/NotificationContext';
import { TaskType, TaskStatus } from '@/types/task';
import { useCrawler } from '@/hooks/useCrawler';

interface CrawlerTaskConfig {
  name: string;
  startUrls: string[];
  maxDepth: number;
  maxConcurrency: number;
  delayMs: number;
  allowedDomains?: string[];
  excludePatterns?: string[];
  includePatterns?: string[];
  enableAI: boolean;
  useProxy: boolean;
  respectRobots: boolean;
  userAgent: string;
  useReadability?: boolean;
  includeRawHtml?: boolean;
  customHeaders?: Record<string, string>;
  cookies?: string;
  randomUserAgent?: boolean;
  randomIP?: boolean;
  selectors?: {
    title?: string;
    content?: string;
    description?: string;
    images?: string;
    links?: string;
  };
}

export const CrawlerPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'create' | 'tasks'>('create');
  const { showSuccess, showError } = useNotification();
  
  // 使用 useCrawler hook 来管理任务状态和操作
  const {
    tasks,
    stats,
    loading,
    error,
    refreshData,
    createTask,
    startTask,
    pauseTask,
    stopTask,
    deleteTask,
  } = useCrawler();
  console.log("tasks:", tasks)
  // 创建爬虫任务
  const handleCreateTask = async (config: CrawlerTaskConfig) => {
    try {
      await createTask({
        name: config.name,
        type: TaskType.HTML_CRAWLER,
        urls: config.startUrls,
        config: {
          maxDepth: config.maxDepth,
          maxConcurrency: config.maxConcurrency,
          delayMs: config.delayMs,
          allowedDomains: config.allowedDomains,
          enableAI: config.enableAI,
          useProxy: config.useProxy,
          respectRobots: config.respectRobots,
          userAgent: config.userAgent,
          useReadability: config.useReadability,
          includeRawHtml: config.includeRawHtml,
          customHeaders: config.customHeaders,
          cookies: config.cookies,
          selectors: config.selectors
        }
      });
      
      showSuccess('爬虫任务创建成功！');
      setActiveTab('tasks');
    } catch (err) {
      showError('创建失败', err instanceof Error ? err.message : '网络错误');
    }
  };

  // 页面加载时获取任务列表
  useEffect(() => {
    if (activeTab === 'tasks') {
      refreshData();
    }
  }, [activeTab, refreshData]);

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">HTML爬虫</h1>
        <p className="text-gray-600 mt-1">
          多层级智能爬取，支持AI内容解析、链接发现和深度控制
        </p>
      </div>

      {/* 标签页导航 */}
      <Card className="mb-6">
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('create')}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${ 
              activeTab === 'create'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            创建任务
          </button>
          <button
            onClick={() => setActiveTab('tasks')}
            className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'tasks'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            任务管理
          </button>
        </div>

        <div className="p-6">
          {activeTab === 'create' && (
            <div className="max-w-4xl">
              <CrawlerTaskForm onSubmit={handleCreateTask} loading={loading} />
            </div>
          )}

          {activeTab === 'tasks' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">爬虫任务列表</h3>
                <Button onClick={refreshData} disabled={loading}>
                  {loading ? '刷新中...' : '刷新列表'}
                </Button>
              </div>

              {error && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600">{error}</p>
                </div>
              )}

              <TaskList 
                tasks={tasks}
                loading={loading}
                selectedTasks={[]}
                onSelectionChange={() => {}}
                onStartTask={startTask}
                onPauseTask={pauseTask}
                onStopTask={stopTask}
                onDeleteTask={deleteTask}
                onRefresh={refreshData}
              />
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};