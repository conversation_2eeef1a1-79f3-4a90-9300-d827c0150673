/**
 * API 路由索引
 * 
 * 整合所有API路由模块
 */

import { Router } from 'express';
import { TaskManager } from '@/app/task-manager';
import { Server as SocketIOServer } from 'socket.io';

import { createTaskRoutes } from './tasks';
import { createResultRoutes } from './results';
import { createSystemRoutes } from './system';
import { createMetricsRoutes } from './metrics';
import { createAIRoutes } from './ai';
import { createMonitoringRoutes } from './monitoring';
import { createQueuesRoutes } from './queues';
import { createProxyRoutes } from './proxy';
import { createApiRoutes as createFetchRoutes } from './fetch';
import { createCrawlerRoutes } from './crawler';
import { error } from '@/utils';

/**
 * 路由配置接口
 */
export interface RouteConfig {
  taskManager: TaskManager;
  io: SocketIOServer;
  activeTasks: Map<string, any>;
}

/**
 * 创建所有API路由
 */
export function createApiRoutes(config: RouteConfig): Router {
  const router = Router();

  // API版本信息
  router.get('/', (req, res) => {
    res.json({
      name: 'Nodex API',
      version: '2.0.0',
      description: '简化版智能爬虫系统 API',
      endpoints: {
        tasks: '/api/v1/tasks (8个端点)',
        results: '/api/v1/results (5个端点)',
        system: '/api/v1/system (4个端点)',
        metrics: '/api/v1/metrics (3个端点)',
        ai: '/api/v1/ai (2个端点)',
        proxy: '/api/v1/proxy (7个端点)',
        fetch: '/api/v1/fetch (3个端点)',
        crawler: '/api/v1/crawler (5个端点)',
      },
      websocket: {
        tasks: '/ws/tasks/{taskId}',
        system: '/ws/system',
        metrics: '/ws/metrics',
        logs: '/ws/logs',
      },
      totalEndpoints: 37,
      health: '/health',
      timestamp: new Date().toISOString(),
    });
  });

  // 任务管理路由 - 使用新的TaskManager
  router.use('/tasks', createTaskRoutes(
    config.taskManager,
    config.io
  ));

  // 结果查询路由
  router.use('/results', createResultRoutes(
    config.taskManager,
    config.activeTasks
  ));

  // 系统控制路由
  router.use('/system', createSystemRoutes(
    config.taskManager,
    config.io
  ));

  // 监控指标路由
  router.use('/metrics', createMetricsRoutes(
    config.taskManager
  ));

  // AI功能路由
  router.use('/ai', createAIRoutes(
    config.taskManager
  ));

  // 标准化监控路由 (v1)
  router.use('/monitoring', createMonitoringRoutes(
    config.taskManager,
    config.io,
    config.activeTasks
  ));

  // 标准化队列控制路由 (v1)
  router.use('/queues', createQueuesRoutes(
    config.taskManager,
    config.io
  ));

  // 代理管理路由
  router.use('/proxy', createProxyRoutes(
    config.taskManager
  ));

  // API 抓取路由
  router.use('/fetch', createFetchRoutes(config.taskManager));

  // HTML 爬虫路由
  router.use('/crawler', createCrawlerRoutes(config.taskManager));

  // API文档路由
  router.get('/docs', (req, res) => {
    res.json({
      openapi: '3.0.0',
      info: {
        title: 'Nodex API',
        version: '1.0.0',
        description: '企业级智能爬虫系统 RESTful API',
      },
      servers: [
        {
          url: '/api/v1',
          description: 'API v1',
        },
      ],
      paths: {
        '/tasks': {
          get: {
            summary: '获取任务列表',
            parameters: [
              {
                name: 'status',
                in: 'query',
                schema: { type: 'string' },
                description: '按状态过滤任务',
              },
              {
                name: 'limit',
                in: 'query',
                schema: { type: 'integer', default: 50 },
                description: '返回结果数量限制',
              },
              {
                name: 'offset',
                in: 'query',
                schema: { type: 'integer', default: 0 },
                description: '结果偏移量',
              },
            ],
          },
          post: {
            summary: '创建新任务',
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      name: { type: 'string' },
                      config: {
                        type: 'object',
                        properties: {
                          startUrls: {
                            type: 'array',
                            items: { type: 'string' },
                          },
                          match: { type: 'string' },
                          parser: {
                            type: 'object',
                            properties: {
                              type: { type: 'string' },
                              options: { type: 'object' },
                            },
                          },
                        },
                        required: ['startUrls'],
                      },
                    },
                    required: ['name', 'config'],
                  },
                },
              },
            },
          },
        },
        '/tasks/{taskId}': {
          get: {
            summary: '获取任务状态',
            parameters: [
              {
                name: 'taskId',
                in: 'path',
                required: true,
                schema: { type: 'string' },
              },
            ],
          },
        },
        '/tasks/{taskId}/stop': {
          post: {
            summary: '停止任务',
            parameters: [
              {
                name: 'taskId',
                in: 'path',
                required: true,
                schema: { type: 'string' },
              },
            ],
          },
        },
        '/results/{taskId}': {
          get: {
            summary: '获取任务结果',
            parameters: [
              {
                name: 'taskId',
                in: 'path',
                required: true,
                schema: { type: 'string' },
              },
              {
                name: 'limit',
                in: 'query',
                schema: { type: 'integer', default: 10 },
              },
              {
                name: 'offset',
                in: 'query',
                schema: { type: 'integer', default: 0 },
              },
            ],
          },
        },
        '/system/status': {
          get: {
            summary: '获取系统状态',
          },
        },
        '/system/init': {
          post: {
            summary: '初始化系统',
          },
        },
        '/system/start': {
          post: {
            summary: '启动系统',
          },
        },
        '/system/stop': {
          post: {
            summary: '停止系统',
          },
        },
        '/system/pause': {
          post: {
            summary: '暂停系统',
          },
        },
        '/system/resume': {
          post: {
            summary: '恢复系统',
          },
        },
        '/system/health': {
          get: {
            summary: '系统健康检查',
          },
        },
      },
      components: {
        schemas: {
          Task: {
            type: 'object',
            properties: {
              taskId: { type: 'string' },
              name: { type: 'string' },
              status: {
                type: 'string',
                enum: ['running', 'paused', 'stopped', 'completed', 'failed'],
              },
              createdAt: { type: 'string', format: 'date-time' },
              stats: {
                type: 'object',
                properties: {
                  processed: { type: 'integer' },
                  inQueue: { type: 'integer' },
                  errors: { type: 'integer' },
                },
              },
            },
          },
          SystemStatus: {
            type: 'object',
            properties: {
              initialized: { type: 'boolean' },
              state: { type: 'string' },
              uptime: { type: 'number' },
              stats: { type: 'object' },
              health: { type: 'object' },
            },
          },
          Error: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: { type: 'string' },
            },
          },
        },
      },
    });
  });

  // 监控指标路由
  router.get('/metrics', async (req, res) => {
    try {
      const stats = config.taskManager.getCrawlerStats();
      const systemMetrics = {
        timestamp: new Date().toISOString(),
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          platform: process.platform,
          nodeVersion: process.version,
        },
        api: {
          activeTasks: config.activeTasks.size,
          connectedClients: config.io.sockets.sockets.size,
        },
        crawler: {
          totalUrls: stats.totalUrls || 0,
          processedUrls: stats.processedUrls || 0,
          failedUrls: stats.failedUrls || 0,
          queueSize: stats.queueSize || 0,
          extractedItems: stats.extractedItems || 0,
        },
      };

      res.json({
        success: true,
        metrics: systemMetrics,
      });
    } catch (err) {
      error('获取系统指标失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get metrics',
      });
    }
  });

  return router;
}

export * from './tasks';
export * from './results';
export * from './system';
