/**
 * API 抓取路由
 * 
 * 提供智能代理轮换的单URL请求服务
 */

import { Router } from 'express';
import { CreateApiTaskRequest, ApiResult } from '@/types/task';
import { getLogger, error as logError } from '@/utils';
import { TaskManager } from '@/app/task-manager';
import { HttpRequestOptions } from '@/interfaces';

const logger = getLogger('ApiRoute');

/**
 * 创建 API 抓取路由
 */
export function createApiRoutes(taskManager: TaskManager): Router {
  const router = Router();

  /**
   * POST /api/v1/fetch - 单次API抓取
   */
  router.post('/', async (req, res) => {
    try {
      const config = req.body;
      
      // 验证请求参数
      if (!config.url) {
        return res.status(400).json({
          success: false,
          error: 'URL is required'
        });
      }

      // 设置默认值
      const fetchConfig = {
        url: config.url,
        method: config.method || 'GET',
        headers: config.headers || {},
        body: config.body,
        timeout: config.timeout || 30000,
        retryCount: config.retryCount || 3,
        useProxy: config.useProxy !== false,
        followRedirects: config.followRedirects !== false
      };

      logger.info('开始API抓取', { url: fetchConfig.url, method: fetchConfig.method });

      // 获取依赖管理器
      const dependencies = taskManager.getDependencies();
      const fetcherManager = dependencies.fetcherManager;
      
      let result: ApiResult;
      const startTime = Date.now();
      
      try {
        // 使用FetcherManager进行请求，支持代理轮换
        const fetchResponse = await fetcherManager.fetch(fetchConfig.url, {
          method: fetchConfig.method,
          headers: fetchConfig.headers,
          body: fetchConfig.body,
          timeout: fetchConfig.timeout,
          useProxy: fetchConfig.useProxy,
          maxRedirects: fetchConfig.followRedirects ? 5 : 0
        });
        
        const responseTime = Date.now() - startTime;

        result = {
          id: `api-${Date.now()}`,
          taskId: 'single-fetch',
          url: fetchConfig.url,
          status: fetchResponse.statusCode,
          statusText: fetchResponse.statusCode === 200 ? 'OK' : 'Error',
          headers: fetchResponse.headers,
          data: fetchResponse.content,
          responseTime,
          timestamp: new Date().toISOString(),
          proxyUsed: fetchResponse.metadata?.proxyUsed === 'true' || fetchResponse.metadata?.proxyUsed === true
        };

        logger.info('API抓取成功', { 
          url: fetchConfig.url, 
          statusCode: fetchResponse.statusCode, 
          responseTime,
          proxyUsed: result.proxyUsed
        });

      } catch (err) {
        const responseTime = Date.now() - startTime;
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        
        logger.error('API抓取失败', { 
          url: fetchConfig.url, 
          error: errorMessage,
          responseTime 
        });

        result = {
          id: `api-${Date.now()}`,
          taskId: 'single-fetch',
          url: fetchConfig.url,
          status: 0,
          statusText: 'Error',
          headers: {},
          data: '',
          responseTime,
          timestamp: new Date().toISOString(),
          proxyUsed: false,
          error: errorMessage
        };
      }

      res.json({
        success: !result.error,
        data: result,
        message: result.error ? 'API抓取失败' : 'API抓取完成'
      });

    } catch (err) {
      logError('API抓取处理失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'API fetch failed'
      });
    }
  });

  /**
   * POST /api/v1/fetch/task - 创建API抓取任务
   */
  router.post('/task', async (req, res) => {
    try {
      const request: CreateApiTaskRequest = req.body;

      // 验证请求参数
      if (!request.name || !request.config?.url) {
        return res.status(400).json({
          success: false,
          error: 'Task name and URL are required'
        });
      }

      // 创建API抓取任务
      const config = request.config;
      
      logger.info('创建API抓取任务', { 
        name: request.name, 
        url: config.url,
        method: config.method || 'GET'
      });

      try {
        // 使用TaskManager创建API抓取任务
        const createTaskRequest = {
          name: request.name,
          type: 'api_fetch' as any,
          urls: config.url ? [config.url] : [], // API任务只有一个URL
          config: {
            name: request.name,
            urls: config.url ? [config.url] : [],
            maxDepth: 1, // API任务深度固定为1
            maxConcurrency: 1, // API任务并发固定为1
            delayMs: config.timeout || 30000,
            enableProxy: config.useProxy !== false,
            enableAI: false, // API任务通常不需要AI解析
            allowedDomains: [],
            useReadability: false,
            includeRawHtml: false,
            customHeaders: config.headers || {},
            cookies: '',
            userAgent: config.headers?.['User-Agent'] || 'Nodex-API-Fetcher/1.0'
          }
        };

        const actualTaskId = await taskManager.createTask(createTaskRequest);
        
        logger.info('API抓取任务创建成功', { 
          taskId: actualTaskId, 
          name: request.name,
          url: config.url
        });

        res.json({
          success: true,
          taskId: actualTaskId,
          status: 'pending',
          message: 'API抓取任务创建成功',
          data: {
            taskId: actualTaskId,
            name: request.name,
            config: config
          }
        });

      } catch (taskError) {
        logger.error('创建API抓取任务失败', { 
          name: request.name,
          url: config.url,
          error: taskError instanceof Error ? taskError.message : taskError
        });

        res.status(500).json({
          success: false,
          error: taskError instanceof Error ? taskError.message : 'Failed to create API fetch task'
        });
      }

    } catch (err) {
      logError('创建API抓取任务失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to create API fetch task'
      });
    }
  });

  /**
   * GET /api/v1/fetch/results/:taskId - 获取API抓取结果
   */
  router.get('/results/:taskId', async (req, res) => {
    try {
      const { taskId } = req.params;
      const { page = 1, limit = 20 } = req.query;

      logger.info('获取API抓取结果', { taskId, page, limit });

      // 从存储中获取API抓取结果
      let results: ApiResult[] = [];
      let total = 0;

      try {
        // 从TaskManager获取任务结果
        const taskResults = await taskManager.getTaskResults(taskId);
        
        // 转换为ApiResult格式
        results = taskResults.map(item => ({
          id: item.id,
          taskId: item.taskId || taskId,
          url: item.url,
          status: item.metadata?.statusCode || 200,
          statusText: (item.metadata?.statusCode === 200) ? 'OK' : 'Error',
          headers: (item.metadata?.headers as Record<string, string>) || {},
          data: item.content || '',
          responseTime: item.metadata?.responseTime || 0,
          timestamp: item.timestamp || new Date().toISOString(),
          proxyUsed: item.metadata?.proxyUsed === true || item.metadata?.proxyUsed === 'true',
          error: item.status === 'failed' ? (item.extractedData?.error || 'Request failed') : undefined
        }));

        total = results.length;
        
        // 分页处理
        const offset = (Number(page) - 1) * Number(limit);
        results = results.slice(offset, offset + Number(limit));

        logger.info('成功获取API抓取结果', { 
          taskId, 
          total: total,
          returned: results.length
        });

      } catch (storageError) {
        logger.error('从存储获取API结果失败', { 
          taskId, 
          error: storageError instanceof Error ? storageError.message : storageError 
        });
        // 返回空结果而不是错误，保持API稳定性
      }

      res.json({
        success: true,
        data: results,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: 0,
          totalPages: 0
        }
      });

    } catch (err) {
      logError('获取API抓取结果失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get API fetch results'
      });
    }
  });

  return router;
}