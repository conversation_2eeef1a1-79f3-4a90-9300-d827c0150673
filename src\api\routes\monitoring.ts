/**
 * 标准化监控 API v1 路由
 * 为前端提供标准化的监控数据格式
 */

import { Router } from 'express';
import { TaskManager } from '@/app/task-manager';
import { Server as SocketIOServer } from 'socket.io';
import { error } from '@/utils';
import { cpus } from 'os';

// CPU使用率监控变量
let previousCpuUsage = process.cpuUsage();
let previousTimestamp = Date.now();

/**
 * 获取CPU使用率百分比
 */
function getCpuUsage(): number {
  const currentCpuUsage = process.cpuUsage();
  const currentTimestamp = Date.now();
  
  const timeDelta = currentTimestamp - previousTimestamp;
  const userDelta = currentCpuUsage.user - previousCpuUsage.user;
  const systemDelta = currentCpuUsage.system - previousCpuUsage.system;
  
  // 更新previous值
  previousCpuUsage = currentCpuUsage;
  previousTimestamp = currentTimestamp;
  
  // 计算CPU使用率
  const totalCpuTime = (userDelta + systemDelta) / 1000; // 转换为毫秒
  const cpuUsagePercent = (totalCpuTime / (timeDelta * cpus().length)) * 100;
  
  return Math.min(100, Math.max(0, cpuUsagePercent)); // 确保在0-100范围内
}

/**
 * 计算系统吞吐量 (每分钟处理的URL数量)
 */
function calculateThroughput(stats: any, activeTasks: Map<string, any>): number {
  const runningTasks = Array.from(activeTasks.values()).filter(t => (t as any).status === 'running');
  
  if (runningTasks.length === 0) {
    return 0;
  }
  
  const totalProcessed = stats.processedUrls || 0;
  
  // 计算平均运行时间（分钟）
  const averageRuntime = runningTasks.reduce((sum, task) => {
    if ((task as any).startedAt) {
      const runtime = (Date.now() - new Date((task as any).startedAt).getTime()) / (1000 * 60); // 转换为分钟
      return sum + runtime;
    }
    return sum;
  }, 0) / runningTasks.length;
  
  // 避免除零错误
  if (averageRuntime <= 0) {
    return 0;
  }
  
  // 计算吞吐量：处理的URL数量 / 平均运行时间（分钟）
  const throughput = totalProcessed / averageRuntime;
  
  return Math.round(throughput * 100) / 100; // 保留两位小数
}

/**
 * 创建标准化监控路由
 */
export function createMonitoringRoutes(
  taskManager: TaskManager,
  io: SocketIOServer,
  activeTasks: Map<string, any>
): Router {
  const router = Router();

  /**
   * GET /api/v1/monitoring/system - 获取系统状态
   * 返回标准化的 SystemStats 格式
   */
  router.get('/system', async (req, res) => {
    try {
      const stats = taskManager.getCrawlerStats();
      const tasks = taskManager.getAllTasks();
      
      // 转换为标准化的 SystemStats 格式
      const systemStats = {
        status: 'running', // TaskManager总是运行状态
        uptime: process.uptime(),
        startTime: new Date(Date.now() - process.uptime() * 1000).toISOString(),
        memory: process.memoryUsage(),
        cpu: {
          usage: getCpuUsage(), // 实现 CPU 使用率监控
          user: process.cpuUsage().user / 1000000, // 转换为秒
          system: process.cpuUsage().system / 1000000, // 转换为秒
        },
        sessions: {
          total: activeTasks.size,
          active: Array.from(activeTasks.values()).filter(t => (t as any).status === 'running').length,
          completed: Array.from(activeTasks.values()).filter(t => (t as any).status === 'completed').length,
          failed: Array.from(activeTasks.values()).filter(t => (t as any).status === 'failed').length,
        },
        urls: {
          total: stats.totalUrls || 0,
          processed: stats.processedUrls || 0,
          failed: stats.failedUrls || 0,
          successRate: stats.totalUrls > 0 ? 
            ((stats.processedUrls - stats.failedUrls) / stats.totalUrls) * 100 : 0,
        },
      };

      res.json(systemStats);
    } catch (err) {
      error('获取系统状态失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get system stats',
      });
    }
  });

  /**
   * GET /api/v1/monitoring/queues - 获取队列状态
   * 返回标准化的 QueueStats[] 格式
   */
  router.get('/queues', async (req, res) => {
    try {
      const stats = taskManager.getCrawlerStats();
      const tasks = taskManager.getAllTasks();
      
      // 模拟队列数据（实际应该从队列管理器获取）
      const queueStats = [
        {
          queueName: 'crawl',
          waiting: stats.queueSize || 0,
          active: Array.from(activeTasks.values()).filter(t => (t as any).status === 'running').length,
          completed: stats.processedUrls || 0,
          failed: stats.failedUrls || 0,
          delayed: 0,
          paused: false,
          timestamp: new Date(),
          totalJobs: stats.totalUrls || 0,
          successRate: stats.totalUrls > 0 ? 
            ((stats.processedUrls - stats.failedUrls) / stats.totalUrls) * 100 : 0,
          throughput: calculateThroughput(stats, activeTasks), // 计算吞吐量
          health: stats.failedUrls > stats.processedUrls * 0.1 ? 'critical' : 'healthy',
        },
      ];

      res.json(queueStats);
    } catch (err) {
      error('获取队列状态失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get queue stats',
      });
    }
  });

  /**
   * GET /api/v1/monitoring/modules - 获取模块状态
   * 返回标准化的 ModuleStatus[] 格式
   */
  router.get('/modules', async (req, res) => {
    try {
      const tasks = taskManager.getAllTasks();
      const stats = taskManager.getCrawlerStats();
      const dependencies = taskManager.getDependencies();
      const now = new Date();

      // 获取真实的模块健康状态
      const moduleStatus = [];

      // 1. TaskManager / Crawler 模块
      // 简化逻辑：初始化成功且无错误 = 健康
      try {
        const crawlerHealthy = true; // TaskManager已初始化成功
        const crawlerStatus = tasks.length > 0 ? 'healthy' : 'stopped';
        
        moduleStatus.push({
          name: 'crawler',
          status: crawlerStatus,
          healthy: crawlerHealthy,
          initialized: true,
          lastChecked: now,
          lastUpdate: now.toISOString(),
          uptime: process.uptime(),
          details: {
            startTime: new Date(Date.now() - process.uptime() * 1000),
            uptime: process.uptime(),
            dependencies: ['taskManager'],
          },
          metrics: {
            activeTasks: tasks.length,
            processedUrls: stats.processedUrls || 0,
            failedUrls: stats.failedUrls || 0,
            queueSize: stats.queueSize || 0,
            successRate: stats.processedUrls > 0 ? ((stats.processedUrls - stats.failedUrls) / stats.processedUrls * 100).toFixed(1) : 0,
          },
        });
      } catch (err) {
        moduleStatus.push({
          name: 'crawler',
          status: 'critical',
          healthy: false,
          initialized: false,
          lastChecked: now,
          lastUpdate: now.toISOString(),
          uptime: process.uptime(),
          error: err instanceof Error ? err.message : 'Crawler initialization failed',
          details: {
            startTime: new Date(Date.now() - process.uptime() * 1000),
            uptime: process.uptime(),
            dependencies: ['taskManager'],
          },
          metrics: {},
        });
      }

      // 2. Storage 模块
      // 简化逻辑：能获取健康检查结果 = 已初始化，无异常 = 健康
      try {
        const storageHealthCheck = await dependencies.storageManager.healthCheck();
        const storageStats = dependencies.storageManager.getStats();
        const allStoragesHealthy = Object.values(storageHealthCheck).every(healthy => healthy);
        
        moduleStatus.push({
          name: 'storage',
          status: allStoragesHealthy ? 'healthy' : 'warning',
          healthy: allStoragesHealthy,
          initialized: true,
          lastChecked: now,
          lastUpdate: now.toISOString(),
          uptime: process.uptime(),
          details: {
            startTime: new Date(Date.now() - process.uptime() * 1000),
            uptime: process.uptime(),
            dependencies: Object.keys(storageHealthCheck),
          },
          metrics: {
            extractedItems: stats.extractedItems || 0,
            storageHealth: storageHealthCheck,
            storageStats,
          },
        });
      } catch (err) {
        moduleStatus.push({
          name: 'storage',
          status: 'critical',
          healthy: false,
          initialized: false,
          lastChecked: now,
          lastUpdate: now.toISOString(),
          uptime: process.uptime(),
          error: err instanceof Error ? err.message : 'Storage health check failed',
          details: {
            startTime: new Date(Date.now() - process.uptime() * 1000),
            uptime: process.uptime(),
            dependencies: ['memory'],
          },
          metrics: {},
        });
      }

      // 3. Fetcher 模块
      // 简化逻辑：能获取统计数据 = 已初始化，无异常 = 健康
      try {
        const fetcherStats = dependencies.fetcherManager.getStats();
        // 初始化成功即为健康，只有真正的错误才标记为不健康
        const fetcherHealthy = true; // 能获取stats说明已正常初始化
        
        moduleStatus.push({
          name: 'fetcher',
          status: 'healthy',
          healthy: fetcherHealthy,
          initialized: true,
          lastChecked: now,
          lastUpdate: now.toISOString(),
          uptime: process.uptime(),
          details: {
            startTime: new Date(Date.now() - process.uptime() * 1000),
            uptime: process.uptime(),
            dependencies: ['http', 'proxy'],
          },
          metrics: {
            totalRequests: fetcherStats.totalRequests,
            successfulRequests: fetcherStats.successfulRequests,
            failedRequests: fetcherStats.failedRequests,
            successRate: fetcherStats.successRate,
            healthyProxies: fetcherStats.healthyProxies,
            averageResponseTime: fetcherStats.averageResponseTime,
          },
        });
      } catch (err) {
        moduleStatus.push({
          name: 'fetcher',
          status: 'critical',
          healthy: false,
          initialized: false,
          lastChecked: now,
          lastUpdate: now.toISOString(),
          uptime: process.uptime(),
          error: err instanceof Error ? err.message : 'Fetcher initialization failed',
          details: {
            startTime: new Date(Date.now() - process.uptime() * 1000),
            uptime: process.uptime(),
            dependencies: ['http'],
          },
          metrics: {},
        });
      }

      // 4. Parser 模块
      // 简化逻辑：能获取统计数据 = 已初始化，无异常 = 健康
      try {
        const parserStats = dependencies.parserManager.getStats();
        // 能获取解析器状态说明初始化成功，即为健康
        const parserHealthy = true;
        
        moduleStatus.push({
          name: 'parser',
          status: 'healthy',
          healthy: parserHealthy,
          initialized: true,
          lastChecked: now,
          lastUpdate: now.toISOString(),
          uptime: process.uptime(),
          details: {
            startTime: new Date(Date.now() - process.uptime() * 1000),
            uptime: process.uptime(),
            dependencies: ['cheerio', 'json', 'xml'],
          },
          metrics: {
            ...parserStats,
          },
        });
      } catch (err) {
        moduleStatus.push({
          name: 'parser',
          status: 'critical',
          healthy: false,
          initialized: false,
          lastChecked: now,
          lastUpdate: now.toISOString(),
          uptime: process.uptime(),
          error: err instanceof Error ? err.message : 'Parser initialization failed',
          details: {
            startTime: new Date(Date.now() - process.uptime() * 1000),
            uptime: process.uptime(),
            dependencies: ['cheerio'],
          },
          metrics: {},
        });
      }

      // 5. AI 模块 (可选)
      // 简化逻辑：配置了AI引擎且能通过健康检查 = 健康，否则为停止状态
      if (dependencies.aiEngine) {
        try {
          const aiHealthy = await dependencies.aiEngine.healthCheck();
          const aiStats = dependencies.aiEngine.getStats();
          
          moduleStatus.push({
            name: 'ai',
            status: aiHealthy ? 'healthy' : 'warning',
            healthy: aiHealthy,
            initialized: true,
            lastChecked: now,
            lastUpdate: now.toISOString(),
            uptime: process.uptime(),
            details: {
              startTime: new Date(Date.now() - process.uptime() * 1000),
              uptime: process.uptime(),
              dependencies: ['openai', 'gemini'],
            },
            metrics: aiStats,
          });
        } catch (err) {
          moduleStatus.push({
            name: 'ai',
            status: 'critical',
            healthy: false,
            initialized: false,
            lastChecked: now,
            lastUpdate: now.toISOString(),
            uptime: process.uptime(),
            error: err instanceof Error ? err.message : 'AI Engine initialization failed',
            details: {
              startTime: new Date(Date.now() - process.uptime() * 1000),
              uptime: process.uptime(),
              dependencies: ['openai'],
            },
            metrics: {},
          });
        }
      } else {
        moduleStatus.push({
          name: 'ai',
          status: 'stopped',
          healthy: false,
          initialized: false,
          lastChecked: now,
          lastUpdate: now.toISOString(),
          uptime: process.uptime(),
          details: {
            startTime: new Date(Date.now() - process.uptime() * 1000),
            uptime: process.uptime(),
            dependencies: ['openai'],
          },
          metrics: {},
          error: 'AI Engine not configured',
        });
      }

      res.json(moduleStatus);
    } catch (err) {
      error('获取模块状态失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get module status',
      });
    }
  });

  /**
   * GET /api/v1/monitoring/sessions - 获取会话列表
   * 返回标准化的 CrawlSession[] 格式
   */
  router.get('/sessions', async (req, res) => {
    try {
      const sessions = Array.from(activeTasks.values()).map(task => ({
        id: (task as any).taskId,
        name: (task as any).name,
        status: (task as any).status,
        createdAt: new Date((task as any).createdAt),
        stats: {
          processedUrls: (task as any).stats?.processed || 0,
          totalUrls: ((task as any).stats?.processed || 0) + ((task as any).stats?.inQueue || 0),
          failedUrls: (task as any).stats?.errors || 0,
          successRate: (task as any).stats?.processed > 0 ? 
            (((task as any).stats.processed - (task as any).stats.errors) / (task as any).stats.processed) * 100 : 0,
        },
      }));

      res.json(sessions);
    } catch (err) {
      error('获取会话列表失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get sessions',
      });
    }
  });

  return router;
}