import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tsconfigPaths(), // 支持 tsconfig.json 中的路径别名
  ],
  
  // 开发服务器配置
  server: {
    port: 3002,
    host: true, // 允许外部访问
    open: false, // 不自动打开浏览器
    cors: true,
    proxy: {
      // 代理 API 请求到后端服务器
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
      },
      // 代理 Socket.IO 请求
      '/socket.io': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        ws: true, // 启用 WebSocket 代理
      },
    },
    fs: {
      allow: ['..'], // 允许 Vite 访问上层目录 nodex/src
    },
  },

  // 构建配置
  build: {
    outDir: 'dist',
    sourcemap: true,
    minify: 'esbuild',
    target: 'es2015',
    rollupOptions: {
      output: {
        manualChunks: {
          // 将大型依赖分离到单独的 chunk
          vendor: ['react', 'react-dom'],
          charts: ['chart.js', 'react-chartjs-2', 'recharts'],
          socket: ['socket.io-client'],
        },
      },
    },
  },

  // 路径解析
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/lib': resolve(__dirname, './src/lib'),
      '@/hooks': resolve(__dirname, './src/hooks'),
      '@/types': resolve(__dirname, './src/types'),
      '@/utils': resolve(__dirname, './src/utils'),
      // 添加对共享类型的别名 - 指向前端项目内的副本
      '@/shared-types': resolve(__dirname, './src/shared-types'),
    },
  },

  // 环境变量
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
  },

  // 优化配置
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'socket.io-client',
      'chart.js',
      'react-chartjs-2',
      'recharts',
      'date-fns',
      'clsx',
      'tailwind-merge',
    ],
  },
});
