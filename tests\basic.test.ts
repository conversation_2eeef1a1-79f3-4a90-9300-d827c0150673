/**
 * Nodex 基础功能测试
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { ConfigManager, ConfigFactory } from '../src/config';
import { HttpFetcher } from '../src/modules/fetcher';
import { CheerioParser } from '../src/modules/parser';
import { MongoDBStorage } from '../src/modules/storage';

describe('Nodex 基础功能测试', () => {
  describe('配置管理', () => {
    test('应该能够创建默认配置', () => {
      const config = ConfigFactory.createDefaultConfig();
      
      expect(config).toBeDefined();
      expect(config.task).toBeDefined();
      expect(config.crawlScope).toBeDefined();
      expect(config.depthControl).toBeDefined();
      expect(config.concurrency).toBeDefined();
      expect(config.storage).toBeDefined();
    });

    test('应该能够验证配置', () => {
      const configManager = new ConfigManager();
      const config = ConfigFactory.createDefaultConfig();
      
      const isValid = configManager.validateConfig(config);
      expect(isValid).toBe(true);
    });

    test('应该能够创建完整配置', () => {
      const config = ConfigFactory.createFullConfig({
        seedUrls: ['https://example.com'],
        allowedDomains: ['example.com'],
        maxDepth: 3,
        maxConcurrent: 5,
        storageType: 'mongodb',
        enableAI: false,
        logLevel: 'info'
      });

      expect(config.task.seedUrls).toContain('https://example.com');
      expect(config.crawlScope.allowedDomains).toContain('example.com');
      expect(config.depthControl.global).toBe(3);
      expect(config.concurrency.maxConcurrentRequests).toBe(5);
    });
  });

  describe('HTTP 下载器', () => {
    let httpFetcher: HttpFetcher;

    beforeAll(() => {
      httpFetcher = new HttpFetcher();
    });

    test('应该能够处理HTTP URL', () => {
      expect(httpFetcher.canHandle('https://httpbin.org/get')).toBe(true);
      expect(httpFetcher.canHandle('http://httpbin.org/get')).toBe(true);
      expect(httpFetcher.canHandle('ftp://example.com')).toBe(false);
    });

    test('应该能够下载网页内容', async () => {
      const response = await httpFetcher.fetch('https://httpbin.org/get');
      
      expect(response).toBeDefined();
      expect(response.url).toBe('https://httpbin.org/get');
      expect(response.statusCode).toBe(200);
      expect(response.content).toBeDefined();
      expect(response.responseTime).toBeGreaterThan(0);
    }, 10000);

    test('应该能够处理请求选项', async () => {
      const response = await httpFetcher.fetch('https://httpbin.org/user-agent', {
        headers: {
          'User-Agent': 'Nodex-Test/1.0'
        }
      });
      
      expect(response.statusCode).toBe(200);
      expect(response.content).toContain('Nodex-Test/1.0');
    }, 10000);

    test('应该能够处理错误状态码', async () => {
      try {
        await httpFetcher.fetch('https://httpbin.org/status/404');
      } catch (error) {
        expect(error).toBeDefined();
      }
    }, 10000);
  });

  describe('Cheerio 解析器', () => {
    let cheerioParser: CheerioParser;

    beforeAll(() => {
      cheerioParser = new CheerioParser({
        selectors: {
          title: 'title',
          h1: 'h1',
          links: 'a[href]'
        },
        extractLinks: true
      });
    });

    test('应该能够判断是否可以解析', () => {
      const htmlInput = {
        url: 'https://example.com',
        content: '<html><head><title>Test</title></head><body><h1>Hello</h1></body></html>',
        headers: { 'content-type': 'text/html' }
      };

      expect(cheerioParser.canParse(htmlInput)).toBe(true);

      const jsonInput = {
        url: 'https://api.example.com/data',
        content: '{"data": "test"}',
        headers: { 'content-type': 'application/json' }
      };

      expect(cheerioParser.canParse(jsonInput)).toBe(false);
    });

    test('应该能够解析HTML内容', async () => {
      const input = {
        url: 'https://example.com',
        content: `
          <html>
            <head><title>Test Page</title></head>
            <body>
              <h1>Main Title</h1>
              <a href="/page1">Link 1</a>
              <a href="/page2">Link 2</a>
            </body>
          </html>
        `,
        headers: { 'content-type': 'text/html' }
      };

      const result = await cheerioParser.parse(input);

      expect(result.extractedData).toBeDefined();
      expect(result.extractedData?.title).toBe('Test Page');
      expect(result.extractedData?.h1).toBe('Main Title');
      expect(result.newUrls).toHaveLength(2);
      expect(result.newUrls).toContain('https://example.com/page1');
      expect(result.newUrls).toContain('https://example.com/page2');
    });

    test('应该能够处理解析错误', async () => {
      const input = {
        url: 'https://example.com',
        content: '', // 空内容
        headers: { 'content-type': 'text/html' }
      };

      const result = await cheerioParser.parse(input);

      expect(result).toBeDefined();
      // 空内容应该不会导致错误，只是提取不到数据
    });
  });

  describe('MongoDB 存储', () => {
    let mongoStorage: MongoDBStorage;
    const testConfig = {
      connectionString: 'mongodb://localhost:27017',
      database: 'nodex_test',
      collection: 'test_items'
    };

    beforeAll(async () => {
      mongoStorage = new MongoDBStorage(testConfig);
      
      // 只有在MongoDB可用时才运行测试
      try {
        await mongoStorage.initialize();
      } catch (error) {
        console.warn('MongoDB not available, skipping storage tests');
        return;
      }
    });

    afterAll(async () => {
      if (mongoStorage) {
        try {
          await mongoStorage.close();
        } catch (error) {
          // 忽略关闭错误
        }
      }
    });

    test('应该能够保存数据项', async () => {
      if (!await mongoStorage.healthCheck()) {
        console.warn('MongoDB not healthy, skipping test');
        return;
      }

      const item = {
        id: 'test-1',
        url: 'https://example.com/test',
        data: {
          title: 'Test Item',
          content: 'This is a test item'
        },
        depth: 1,
        crawledAt: new Date()
      };

      const savedId = await mongoStorage.save(item);
      expect(savedId).toBeDefined();

      const retrieved = await mongoStorage.getById(savedId);
      expect(retrieved).toBeDefined();
      expect(retrieved?.url).toBe(item.url);
      expect(retrieved?.data.title).toBe(item.data.title);
    });

    test('应该能够查询数据', async () => {
      if (!await mongoStorage.healthCheck()) {
        console.warn('MongoDB not healthy, skipping test');
        return;
      }

      const result = await mongoStorage.query({
        conditions: [
          {
            field: 'depth',
            operator: 'eq',
            value: 1
          }
        ],
        limit: 10
      });

      expect(result).toBeDefined();
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
    });

    test('应该能够检查项目是否存在', async () => {
      if (!await mongoStorage.healthCheck()) {
        console.warn('MongoDB not healthy, skipping test');
        return;
      }

      const exists = await mongoStorage.exists('https://example.com/test');
      expect(typeof exists).toBe('boolean');
    });
  });

  describe('工具函数', () => {
    test('应该能够生成唯一ID', () => {
      const id1 = generateId();
      const id2 = generateId();
      
      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
    });

    test('应该能够验证URL', () => {
      expect(isValidUrl('https://example.com')).toBe(true);
      expect(isValidUrl('http://example.com')).toBe(true);
      expect(isValidUrl('ftp://example.com')).toBe(false);
      expect(isValidUrl('invalid-url')).toBe(false);
    });

    test('应该能够解析相对URL', () => {
      const baseUrl = 'https://example.com/path/page.html';
      
      expect(resolveUrl('/absolute', baseUrl)).toBe('https://example.com/absolute');
      expect(resolveUrl('relative', baseUrl)).toBe('https://example.com/path/relative');
      expect(resolveUrl('../parent', baseUrl)).toBe('https://example.com/parent');
      expect(resolveUrl('https://other.com', baseUrl)).toBe('https://other.com');
    });
  });
});

// 工具函数
function generateId(): string {
  return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function isValidUrl(url: string): boolean {
  try {
    const parsed = new URL(url);
    return ['http:', 'https:'].includes(parsed.protocol);
  } catch {
    return false;
  }
}

function resolveUrl(url: string, baseUrl: string): string {
  try {
    return new URL(url, baseUrl).toString();
  } catch {
    return url;
  }
}
