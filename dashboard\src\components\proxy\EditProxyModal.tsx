import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';

interface ProxyConfig {
  host: string;
  port: number;
  protocol: string;
  username?: string;
  password?: string;
  weight?: number;
  priority?: number;
  countryCode?: string;
  cityName?: string;
  asnName?: string;
  asnNumber?: number;
}

interface EditProxyModalProps {
  proxy: ProxyConfig;
  onUpdate: (updatedProxy: ProxyConfig) => void;
  onClose: () => void;
}

export const EditProxyModal: React.FC<EditProxyModalProps> = ({ proxy, onUpdate, onClose }) => {
  const [formData, setFormData] = useState<ProxyConfig>({
    host: '',
    port: 8080,
    protocol: 'http',
    username: '',
    password: '',
    weight: 1,
    priority: 0,
    countryCode: '',
    cityName: '',
    asnName: '',
    asnNumber: 0,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (proxy) {
      setFormData({
        host: proxy.host || '',
        port: proxy.port || 8080,
        protocol: proxy.protocol || 'http',
        username: proxy.username || '',
        password: proxy.password || '',
        weight: proxy.weight || 1,
        priority: proxy.priority || 0,
        countryCode: proxy.countryCode || '',
        cityName: proxy.cityName || '',
        asnName: proxy.asnName || '',
        asnNumber: proxy.asnNumber || 0,
      });
    }
  }, [proxy]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.host.trim()) {
      newErrors.host = '主机地址不能为空';
    }
    if (formData.port < 1 || formData.port > 65535) {
      newErrors.port = '端口号必须在 1-65535 之间';
    }
    if (formData.weight && formData.weight < 1) {
      newErrors.weight = '权重必须大于 0';
    }
    if (formData.priority && formData.priority < 0) {
      newErrors.priority = '优先级不能为负数';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onUpdate(formData);
    }
  };

  const handleChange = (field: keyof ProxyConfig) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const value = e.target.type === 'number' ? Number(e.target.value) : e.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">编辑代理</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 基本信息 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  主机地址 *
                </label>
                <input
                  type="text"
                  value={formData.host}
                  onChange={handleChange('host')}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.host ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="***********"
                />
                {errors.host && <p className="text-red-500 text-xs mt-1">{errors.host}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  端口 *
                </label>
                <input
                  type="number"
                  value={formData.port}
                  onChange={handleChange('port')}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.port ? 'border-red-500' : 'border-gray-300'
                  }`}
                  min="1"
                  max="65535"
                />
                {errors.port && <p className="text-red-500 text-xs mt-1">{errors.port}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                协议类型
              </label>
              <select
                value={formData.protocol}
                onChange={handleChange('protocol')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="http">HTTP</option>
                <option value="https">HTTPS</option>
                <option value="socks4">SOCKS4</option>
                <option value="socks5">SOCKS5</option>
              </select>
            </div>

            {/* 认证信息 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  用户名
                </label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={handleChange('username')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="可选"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  密码
                </label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={handleChange('password')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="可选"
                />
              </div>
            </div>

            {/* 地域信息 */}
            <div className="border-t pt-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">地域信息</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    国家代码
                  </label>
                  <input
                    type="text"
                    value={formData.countryCode}
                    onChange={handleChange('countryCode')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="US, CN, etc."
                    maxLength={2}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    城市名称
                  </label>
                  <input
                    type="text"
                    value={formData.cityName}
                    onChange={handleChange('cityName')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="New York, Beijing"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ASN 名称
                  </label>
                  <input
                    type="text"
                    value={formData.asnName}
                    onChange={handleChange('asnName')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="ISP Provider"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ASN 编号
                  </label>
                  <input
                    type="number"
                    value={formData.asnNumber}
                    onChange={handleChange('asnNumber')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="13335"
                    min="0"
                  />
                </div>
              </div>
            </div>

            {/* 高级选项 */}
            <div className="border-t pt-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">高级选项</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    权重
                  </label>
                  <input
                    type="number"
                    value={formData.weight}
                    onChange={handleChange('weight')}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.weight ? 'border-red-500' : 'border-gray-300'
                    }`}
                    min="1"
                  />
                  {errors.weight && <p className="text-red-500 text-xs mt-1">{errors.weight}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    优先级
                  </label>
                  <input
                    type="number"
                    value={formData.priority}
                    onChange={handleChange('priority')}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.priority ? 'border-red-500' : 'border-gray-300'
                    }`}
                    min="0"
                  />
                  {errors.priority && <p className="text-red-500 text-xs mt-1">{errors.priority}</p>}
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-3 pt-6">
              <Button
                type="submit"
                variant="primary"
                className="flex-1"
              >
                保存更改
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={onClose}
                className="flex-1"
              >
                取消
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};