/**
 * 监控系统类型定义
 */

export interface SystemStats {
  status: 'running' | 'stopped' | 'paused' | 'error';
  uptime: number;
  startTime: string; // 改为string以匹配ISO字符串格式
  memory: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  cpu: { // 移除可选标记，确保一致性
    usage: number;
    user: number;
    system: number;
  };
  sessions: {
    total: number;
    active: number;
    completed: number;
    failed: number;
  };
  urls: {
    total: number;
    processed: number;
    failed: number;
    successRate: number;
  };
}

export interface QueueStats {
  queueName: string;
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
  timestamp: Date;
  totalJobs?: number;
  successRate?: number;
  throughput?: number;
  health?: 'healthy' | 'warning' | 'critical';
}

export interface ModuleStatus {
  name: string;
  status: 'healthy' | 'warning' | 'critical' | 'stopped' | 'initializing';
  healthy: boolean; // 新增：简单的健康状态布尔值
  initialized: boolean; // 新增：是否已初始化
  lastChecked: Date;
  lastUpdate: string; // 添加后端发送的字段
  uptime?: number;
  details?: {
    startTime?: Date;
    uptime?: number;
    dependencies?: string[];
  };
  metrics?: Record<string, any>; // 新增：模块指标数据
  error?: string;
}

export interface CrawlSession {
  id: string;
  name: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  createdAt: Date;
  stats?: {
    processedUrls: number;
    totalUrls: number;
    failedUrls: number;
    successRate: number;
  };
}

export interface PerformanceMetrics {
  timestamp: Date;
  cpu: {
    user: number;
    system: number;
    usage: number;
  };
  memory: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
    usage: number;
  };
  uptime: number;
  eventLoop: {
    delay: number;
    utilization: number;
  };
}

export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  services: Record<
    string,
    {
      status: 'healthy' | 'unhealthy';
      message?: string;
      responseTime?: number;
    }
  >;
}

export interface LogEntry {
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: Date;
  context?: Record<string, any>;
}

export interface SystemEvent {
  type: 'started' | 'stopped' | 'paused' | 'resumed' | 'error';
  timestamp: Date;
  data?: Record<string, any>;
  source: string;
  id: string;
}

export interface SessionEvent {
  type: 'created' | 'completed' | 'failed' | 'paused' | 'resumed';
  sessionId: string;
  timestamp: Date;
  data?: Record<string, any>;
}

// Socket.IO 事件类型
export interface SocketEvents {
  'status-update': (data: SystemStats) => void;
  'modules-update': (data: ModuleStatus[]) => void;
  'sessions-update': (data: CrawlSession[]) => void;
  'queues-update': (data: QueueStats[]) => void;
  'system-event': (event: SystemEvent) => void;
  'session-event': (event: SessionEvent) => void;
  'error': (error: SocketError) => void;
  // 添加其他后端发送的事件
  'system_state_changed': (event: { from: string; to: string }) => void;
  'system_stats': (stats: any) => void;
  'url_processed': (data: any) => void;
  'data_extracted': (data: any) => void;
  'system_error': (error: { error: string }) => void;
  'crawl_completed': () => void;
  'task_status_changed': (data: { taskId: string; status: string }) => void;
  'task_progress': (data: { taskId: string; stats: any }) => void;
  'task_failed': (data: { taskId: string; error: string }) => void;
  'task_completed': (data: { taskId: string }) => void;
}

// Socket.IO 错误类型
export interface SocketError {
  message: string;
  code?: string;
  timestamp?: string;
  details?: any;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

// 统计数据类型
export interface StatsData {
  current: number;
  previous?: number;
  change?: number;
  changePercent?: number;
  trend?: 'up' | 'down' | 'stable';
}

// 数据验证相关类型
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 数据默认值类型
export interface DataDefaults {
  systemStats: SystemStats;
  queueStats: QueueStats[];
  moduleStatus: ModuleStatus[];
  sessions: CrawlSession[];
}

// 类型守卫函数类型
export type TypeGuard<T> = (data: any) => data is T;

// Socket.IO 事件数据验证器类型
export interface SocketEventValidators {
  validateSystemStats: TypeGuard<SystemStats>;
  validateQueueStats: TypeGuard<QueueStats[]>;
  validateModuleStatus: TypeGuard<ModuleStatus[]>;
  validateSessions: TypeGuard<CrawlSession[]>;
}
