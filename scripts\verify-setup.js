#!/usr/bin/env node

/**
 * Nodex 项目设置验证脚本
 * 
 * 验证项目配置和依赖是否正确设置
 */

const fs = require('fs');
const path = require('path');

/**
 * 检查文件是否存在
 */
function checkFile(filePath, description) {
  const exists = fs.existsSync(filePath);
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`);
  return exists;
}

/**
 * 检查目录是否存在
 */
function checkDirectory(dirPath, description) {
  const exists = fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  console.log(`${exists ? '✅' : '❌'} ${description}: ${dirPath}`);
  return exists;
}

/**
 * 检查package.json中的脚本
 */
function checkPackageScripts() {
  console.log('\n📦 检查 package.json 脚本...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredScripts = [
      'build',
      'build:backend',
      'build:frontend',
      'dev',
      'dev:backend',
      'dev:frontend',
      'dev:api',
      'start:api',
      'start:production',
    ];

    let allPresent = true;
    for (const script of requiredScripts) {
      const exists = packageJson.scripts && packageJson.scripts[script];
      console.log(`${exists ? '✅' : '❌'} 脚本 "${script}"`);
      if (!exists) allPresent = false;
    }

    return allPresent;
  } catch (error) {
    console.log('❌ 无法读取 package.json:', error.message);
    return false;
  }
}

/**
 * 检查前端配置
 */
function checkFrontendConfig() {
  console.log('\n🌐 检查前端配置...');
  
  let allGood = true;
  
  // 检查前端package.json
  const frontendPackageExists = checkFile('src/frontend/package.json', '前端 package.json');
  allGood = allGood && frontendPackageExists;
  
  // 检查next.config.js
  const nextConfigExists = checkFile('src/frontend/next.config.js', 'Next.js 配置');
  allGood = allGood && nextConfigExists;
  
  // 检查环境变量示例
  const envExampleExists = checkFile('src/frontend/.env.local.example', '环境变量示例');
  allGood = allGood && envExampleExists;
  
  return allGood;
}

/**
 * 检查API服务器文件
 */
function checkApiServer() {
  console.log('\n🔧 检查 API 服务器文件...');
  
  const requiredFiles = [
    'src/api/server.ts',
    'src/api/index.ts',
    'src/api/monitoring-integration.ts',
    'src/api/routes/index.ts',
    'src/api/routes/tasks.ts',
    'src/api/routes/results.ts',
    'src/api/routes/system.ts',
  ];

  let allPresent = true;
  for (const file of requiredFiles) {
    const exists = checkFile(file, `API 文件`);
    if (!exists) allPresent = false;
  }

  return allPresent;
}

/**
 * 检查Docker配置
 */
function checkDockerConfig() {
  console.log('\n🐳 检查 Docker 配置...');
  
  let allGood = true;
  
  allGood = allGood && checkFile('Dockerfile', 'Dockerfile');
  allGood = allGood && checkFile('docker-compose.yml', 'Docker Compose 生产配置');
  allGood = allGood && checkFile('docker-compose.dev.yml', 'Docker Compose 开发配置');
  
  return allGood;
}

/**
 * 检查配置文件
 */
function checkConfigFiles() {
  console.log('\n⚙️  检查配置文件...');
  
  let allGood = true;
  
  allGood = allGood && checkFile('nodex.config.json', 'Nodex 配置文件');
  allGood = allGood && checkFile('tsconfig.json', 'TypeScript 配置');
  
  return allGood;
}

/**
 * 检查文档
 */
function checkDocumentation() {
  console.log('\n📚 检查文档...');
  
  let allGood = true;
  
  allGood = allGood && checkFile('docs/qa.md', 'Q&A 文档');
  allGood = allGood && checkFile('docs/change.md', '变更文档');
  allGood = allGood && checkFile('docs/NEW_ARCHITECTURE.md', '新架构文档');
  
  return allGood;
}

/**
 * 检查测试脚本
 */
function checkTestScripts() {
  console.log('\n🧪 检查测试脚本...');
  
  let allGood = true;
  
  allGood = allGood && checkFile('scripts/test-api.js', 'API 测试脚本');
  allGood = allGood && checkFile('scripts/verify-setup.js', '设置验证脚本');
  
  return allGood;
}

/**
 * 主验证函数
 */
function main() {
  console.log('🔍 Nodex 项目设置验证\n');
  
  const checks = [
    { name: 'Package.json 脚本', fn: checkPackageScripts },
    { name: 'API 服务器文件', fn: checkApiServer },
    { name: '前端配置', fn: checkFrontendConfig },
    { name: 'Docker 配置', fn: checkDockerConfig },
    { name: '配置文件', fn: checkConfigFiles },
    { name: '文档', fn: checkDocumentation },
    { name: '测试脚本', fn: checkTestScripts },
  ];

  let passed = 0;
  let failed = 0;

  for (const check of checks) {
    const result = check.fn();
    if (result) {
      passed++;
    } else {
      failed++;
    }
  }

  console.log('\n📊 验证结果:');
  console.log(`   ✅ 通过: ${passed}`);
  console.log(`   ❌ 失败: ${failed}`);
  console.log(`   📈 完成度: ${Math.round((passed / checks.length) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 项目设置验证通过！');
    console.log('\n📋 下一步操作:');
    console.log('   1. 安装依赖: npm install');
    console.log('   2. 启动开发服务器: npm run dev');
    console.log('   3. 运行API测试: node scripts/test-api.js');
    console.log('   4. 访问应用: http://localhost:3000');
  } else {
    console.log('\n⚠️  项目设置不完整，请检查失败的项目。');
  }

  console.log('\n📖 更多信息请参考:');
  console.log('   - 新架构文档: docs/NEW_ARCHITECTURE.md');
  console.log('   - Q&A 文档: docs/qa.md');
  console.log('   - 变更文档: docs/change.md');
}

main();
