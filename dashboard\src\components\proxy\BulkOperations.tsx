import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface ProxyConfig {
  host: string;
  port: number;
  auth?: {
    username: string;
    password: string;
  };
  protocol?: 'http' | 'https' | 'socks4' | 'socks5';
}

interface ProxyStatus {
  proxy: ProxyConfig;
  status: 'active' | 'failed' | 'recovering';
  successCount: number;
  failureCount: number;
  lastUsed?: Date;
  lastChecked?: Date;
  responseTime?: number;
  healthScore?: number;
}

interface BulkOperationsProps {
  selectedProxies: string[];
  proxies: ProxyStatus[];
  onBulkTest: (proxyKeys: string[]) => Promise<void>;
  onBulkEdit: (proxyKeys: string[], updates: Partial<ProxyConfig>) => Promise<void>;
  onBulkDelete: (proxyKeys: string[]) => Promise<void>;
  onBulkEnable: (proxyKeys: string[]) => Promise<void>;
  onBulkDisable: (proxyKeys: string[]) => Promise<void>;
  loading?: boolean;
}

export const BulkOperations: React.FC<BulkOperationsProps> = ({
  selectedProxies,
  proxies,
  onBulkTest,
  onBulkEdit,
  onBulkDelete,
  onBulkEnable,
  onBulkDisable,
  loading = false,
}) => {
  const [showBulkEditModal, setShowBulkEditModal] = useState(false);
  const [bulkEditData, setBulkEditData] = useState<Partial<ProxyConfig>>({});
  const [showConfirmDialog, setShowConfirmDialog] = useState<{
    action: string;
    title: string;
    message: string;
    onConfirm: () => void;
  } | null>(null);

  const selectedProxyObjects = proxies.filter(p => 
    selectedProxies.includes(`${p.proxy.host}:${p.proxy.port}`)
  );

  const handleBulkEdit = async () => {
    if (selectedProxies.length === 0) return;
    
    try {
      await onBulkEdit(selectedProxies, bulkEditData);
      setShowBulkEditModal(false);
      setBulkEditData({});
    } catch (error) {
      console.error('Bulk edit failed:', error);
    }
  };

  const confirmAction = (action: string, title: string, message: string, onConfirm: () => void) => {
    setShowConfirmDialog({ action, title, message, onConfirm });
  };

  const executeConfirmedAction = () => {
    if (showConfirmDialog) {
      showConfirmDialog.onConfirm();
      setShowConfirmDialog(null);
    }
  };

  const getStatusStats = () => {
    const stats = {
      active: 0,
      failed: 0,
      recovering: 0,
      totalRequests: 0,
      successRate: 0,
    };

    selectedProxyObjects.forEach(proxy => {
      stats[proxy.status]++;
      stats.totalRequests += proxy.successCount + proxy.failureCount;
    });

    if (selectedProxyObjects.length > 0) {
      const totalSuccess = selectedProxyObjects.reduce((sum, p) => sum + p.successCount, 0);
      stats.successRate = stats.totalRequests > 0 ? (totalSuccess / stats.totalRequests) * 100 : 0;
    }

    return stats;
  };

  const stats = getStatusStats();

  if (selectedProxies.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500">
          请选择代理以进行批量操作
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* 选中代理概览 */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            已选择 {selectedProxies.length} 个代理
          </h3>
          <Badge variant="info">{selectedProxies.length} 个选中</Badge>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-green-600">{stats.active}</div>
            <div className="text-sm text-gray-500">正常</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-red-600">{stats.failed}</div>
            <div className="text-sm text-gray-500">失败</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-yellow-600">{stats.recovering}</div>
            <div className="text-sm text-gray-500">恢复中</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-blue-600">{stats.totalRequests}</div>
            <div className="text-sm text-gray-500">总请求</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-purple-600">{stats.successRate.toFixed(1)}%</div>
            <div className="text-sm text-gray-500">成功率</div>
          </div>
        </div>

        {/* 批量操作按钮 */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={() => onBulkTest(selectedProxies)}
            variant="primary"
            size="sm"
            disabled={loading}
          >
            批量测试
          </Button>
          
          <Button
            onClick={() => setShowBulkEditModal(true)}
            variant="secondary"
            size="sm"
            disabled={loading}
          >
            批量编辑
          </Button>
          
          <Button
            onClick={() => confirmAction(
              'enable',
              '启用代理',
              `确定要启用选中的 ${selectedProxies.length} 个代理吗？`,
              () => onBulkEnable(selectedProxies)
            )}
            variant="success"
            size="sm"
            disabled={loading}
          >
            批量启用
          </Button>
          
          <Button
            onClick={() => confirmAction(
              'disable',
              '禁用代理',
              `确定要禁用选中的 ${selectedProxies.length} 个代理吗？`,
              () => onBulkDisable(selectedProxies)
            )}
            variant="warning"
            size="sm"
            disabled={loading}
          >
            批量禁用
          </Button>
          
          <Button
            onClick={() => confirmAction(
              'delete',
              '删除代理',
              `确定要删除选中的 ${selectedProxies.length} 个代理吗？此操作不可撤销。`,
              () => onBulkDelete(selectedProxies)
            )}
            variant="danger"
            size="sm"
            disabled={loading}
          >
            批量删除
          </Button>
        </div>
      </Card>

      {/* 批量编辑模态框 */}
      {showBulkEditModal && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">批量编辑代理</h3>
            <Button
              onClick={() => setShowBulkEditModal(false)}
              variant="secondary"
              size="sm"
            >
              取消
            </Button>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  协议类型
                </label>
                <select
                  value={bulkEditData.protocol || ''}
                  onChange={(e) => setBulkEditData({
                    ...bulkEditData,
                    protocol: e.target.value as 'http' | 'https' | 'socks4' | 'socks5'
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">保持原有协议</option>
                  <option value="http">HTTP</option>
                  <option value="https">HTTPS</option>
                  <option value="socks4">SOCKS4</option>
                  <option value="socks5">SOCKS5</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  用户名 (可选)
                </label>
                <input
                  type="text"
                  value={bulkEditData.auth?.username || ''}
                  onChange={(e) => setBulkEditData({
                    ...bulkEditData,
                    auth: {
                      ...bulkEditData.auth,
                      username: e.target.value,
                      password: bulkEditData.auth?.password || ''
                    }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="留空表示不修改"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  密码 (可选)
                </label>
                <input
                  type="password"
                  value={bulkEditData.auth?.password || ''}
                  onChange={(e) => setBulkEditData({
                    ...bulkEditData,
                    auth: {
                      ...bulkEditData.auth,
                      username: bulkEditData.auth?.username || '',
                      password: e.target.value
                    }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="留空表示不修改"
                />
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                onClick={() => setShowBulkEditModal(false)}
                variant="secondary"
              >
                取消
              </Button>
              <Button
                onClick={handleBulkEdit}
                variant="primary"
                disabled={loading}
              >
                应用更改
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* 确认对话框 */}
      {showConfirmDialog && (
        <Card className="p-6 border-l-4 border-yellow-500">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              {showConfirmDialog.title}
            </h3>
          </div>
          <p className="text-gray-600 mb-6">{showConfirmDialog.message}</p>
          <div className="flex justify-end gap-2">
            <Button
              onClick={() => setShowConfirmDialog(null)}
              variant="secondary"
            >
              取消
            </Button>
            <Button
              onClick={executeConfirmedAction}
              variant="danger"
              disabled={loading}
            >
              确认
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};