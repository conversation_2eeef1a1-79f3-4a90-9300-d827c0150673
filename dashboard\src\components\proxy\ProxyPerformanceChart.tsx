import React from 'react';
import { Card } from '@/components/ui/Card';

interface ProxyPerformanceData {
  timestamp: string;
  responseTime: number;
  successRate: number;
  throughput: number;
}

interface ProxyPerformanceChartProps {
  data: ProxyPerformanceData[];
  loading?: boolean;
  className?: string;
}

export const ProxyPerformanceChart: React.FC<ProxyPerformanceChartProps> = ({
  data,
  loading = false,
  className = '',
}) => {
  // 简单的SVG图表实现
  const chartWidth = 600;
  const chartHeight = 300;
  const padding = 40;

  const maxResponseTime = Math.max(...data.map(d => d.responseTime), 1000);
  const maxSuccessRate = 100;

  const getX = (index: number) => {
    return padding + (index / (data.length - 1)) * (chartWidth - 2 * padding);
  };

  const getResponseTimeY = (responseTime: number) => {
    return chartHeight - padding - (responseTime / maxResponseTime) * (chartHeight - 2 * padding);
  };

  const getSuccessRateY = (successRate: number) => {
    return chartHeight - padding - (successRate / maxSuccessRate) * (chartHeight - 2 * padding);
  };

  const responseTimePath = data.map((d, i) => {
    const x = getX(i);
    const y = getResponseTimeY(d.responseTime);
    return i === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
  }).join(' ');

  const successRatePath = data.map((d, i) => {
    const x = getX(i);
    const y = getSuccessRateY(d.successRate);
    return i === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
  }).join(' ');

  if (loading) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-500">加载性能数据中...</span>
        </div>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">代理性能图表</h3>
          <p className="text-gray-500">暂无性能数据</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-6 ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">代理性能图表</h3>
        <p className="text-sm text-gray-500">响应时间和成功率趋势</p>
      </div>

      <div className="mb-4">
        <div className="flex items-center space-x-6">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span className="text-sm text-gray-600">响应时间 (ms)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span className="text-sm text-gray-600">成功率 (%)</span>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <svg width={chartWidth} height={chartHeight} className="border border-gray-200 rounded">
          {/* 网格线 */}
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Y轴标签 - 响应时间 */}
          <g>
            <text x="10" y="20" className="text-xs fill-gray-500">响应时间</text>
            <text x="10" y="35" className="text-xs fill-blue-600">{maxResponseTime}ms</text>
            <text x="10" y={chartHeight - 10} className="text-xs fill-blue-600">0ms</text>
          </g>

          {/* Y轴标签 - 成功率 */}
          <g>
            <text x={chartWidth - 60} y="20" className="text-xs fill-gray-500">成功率</text>
            <text x={chartWidth - 40} y="35" className="text-xs fill-green-600">100%</text>
            <text x={chartWidth - 30} y={chartHeight - 10} className="text-xs fill-green-600">0%</text>
          </g>

          {/* 响应时间线 */}
          <path
            d={responseTimePath}
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2"
            className="drop-shadow-sm"
          />

          {/* 成功率线 */}
          <path
            d={successRatePath}
            fill="none"
            stroke="#10b981"
            strokeWidth="2"
            className="drop-shadow-sm"
          />

          {/* 数据点 */}
          {data.map((d, i) => (
            <g key={i}>
              {/* 响应时间点 */}
              <circle
                cx={getX(i)}
                cy={getResponseTimeY(d.responseTime)}
                r="3"
                fill="#3b82f6"
                className="drop-shadow-sm"
              />
              {/* 成功率点 */}
              <circle
                cx={getX(i)}
                cy={getSuccessRateY(d.successRate)}
                r="3"
                fill="#10b981"
                className="drop-shadow-sm"
              />
            </g>
          ))}
        </svg>
      </div>

      {/* 统计摘要 */}
      <div className="mt-4 grid grid-cols-3 gap-4">
        <div className="text-center">
          <div className="text-lg font-semibold text-blue-600">
            {Math.round(data.reduce((sum, d) => sum + d.responseTime, 0) / data.length)}ms
          </div>
          <div className="text-sm text-gray-500">平均响应时间</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-green-600">
            {Math.round(data.reduce((sum, d) => sum + d.successRate, 0) / data.length)}%
          </div>
          <div className="text-sm text-gray-500">平均成功率</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-purple-600">
            {Math.round(data.reduce((sum, d) => sum + d.throughput, 0) / data.length)}
          </div>
          <div className="text-sm text-gray-500">平均吞吐量</div>
        </div>
      </div>
    </Card>
  );
};
