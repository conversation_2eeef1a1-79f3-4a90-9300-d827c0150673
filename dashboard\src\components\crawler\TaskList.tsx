import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { StatusBadge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';
import { TaskResultsModal } from './TaskResultsModal';
import { 
  Play, 
  Pause, 
  Square, 
  Trash2, 
  ExternalLink, 
  Clock,
  Globe,
  TrendingUp,
  AlertCircle,
  FileText
} from 'lucide-react';
import { Task } from '@/types/task';

interface TaskListProps {
  tasks: Task[];
  loading: boolean;
  selectedTasks: string[];
  onSelectionChange: (taskIds: string[]) => void;
  onStartTask: (taskId: string) => void;
  onPauseTask: (taskId: string) => void;
  onStopTask: (taskId: string) => void;
  onDeleteTask: (taskId: string) => void;
  onRefresh: () => void;
}

export const TaskList: React.FC<TaskListProps> = ({
  tasks,
  loading,
  selectedTasks,
  onSelectionChange,
  onStartTask,
  onPauseTask,
  onStopTask,
  onDeleteTask,
  // onRefresh, // 未使用的参数
}) => {
  const [confirmDelete, setConfirmDelete] = useState<{isOpen: boolean, taskId: string, taskName: string}>({
    isOpen: false,
    taskId: '',
    taskName: ''
  });
  const [showResults, setShowResults] = useState<string | null>(null);
  
  const handleDeleteClick = (taskId: string, taskName: string) => {
    setConfirmDelete({
      isOpen: true,
      taskId,
      taskName
    });
  };

  const handleConfirmDelete = () => {
    onDeleteTask(confirmDelete.taskId);
    setConfirmDelete({ isOpen: false, taskId: '', taskName: '' });
  };

  const handleCancelDelete = () => {
    setConfirmDelete({ isOpen: false, taskId: '', taskName: '' });
  };

  const handleViewResults = (taskId: string | undefined) => {
    if (taskId) {
      setShowResults(taskId);
    }
  };
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(tasks.map(task => task.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectTask = (taskId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedTasks, taskId]);
    } else {
      onSelectionChange(selectedTasks.filter(id => id !== taskId));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'success';
      case 'paused':
        return 'warning';
      case 'completed':
        return 'info';
      case 'failed':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待启动';
      case 'running':
        return '运行中';
      case 'paused':
        return '已暂停';
      case 'completed':
        return '已完成';
      case 'failed':
        return '已失败';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string | Date) => {
    if (!dateString) return 'N/A';
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleString('zh-CN');
  };

  const calculateProgress = (task: Task) => {
    if (task.stats.total === 0) return 0;
    return Math.round((task.stats.processed / task.stats.total) * 100);
  };

  if (loading && tasks.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">加载任务列表...</p>
        </div>
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className="text-center py-12">
        <Globe className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无爬虫任务</h3>
        <p className="text-gray-500 mb-4">创建第一个爬虫任务开始抓取数据</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 表头 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            {/* <input
              type="checkbox"
              checked={tasks.length > 0 && selectedTasks.length === tasks.length}
              onChange={(e) => handleSelectAll(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            /> */}
            {/* <span className="ml-2 text-sm text-gray-700">全选</span> */}
          </label>
          <span className="text-sm text-gray-500">
            共 {tasks.length} 个任务
          </span>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="space-y-3">
        {tasks.map((task) => (
          <Card key={task.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                {/* 左侧信息 */}
                <div className="flex items-start space-x-3 flex-1">
                  {/* <input
                    type="checkbox"
                    checked={selectedTasks.includes(task.id)}
                    onChange={(e) => handleSelectTask(task.id, e.target.checked)}
                    className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  /> */}
                  
                  <div className="flex-1 min-w-0">
                    {/* 任务名称和状态 */}
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-lg font-medium text-gray-900 truncate">
                        {task.name}
                      </h4>
                      <StatusBadge status={getStatusColor(task.status)}>
                        {getStatusText(task.status)}
                      </StatusBadge>
                    </div>

                    {/* 任务统计 */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-4 w-4 text-blue-500" />
                        <span className="text-sm text-gray-600">
                          进度: {task.stats.processed}/{task.stats.total || '?'} ({calculateProgress(task)}%)
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-gray-600">
                          队列: {task.stats.inQueue}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <AlertCircle className="h-4 w-4 text-red-500" />
                        <span className="text-sm text-gray-600">
                          错误: {task.stats.errors}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Globe className="h-4 w-4 text-purple-500" />
                        <span className="text-sm text-gray-600">
                          URL: {task.urls.length}
                        </span>
                      </div>
                    </div>

                    {/* 进度条 */}
                    {task.status === 'running' && task.stats.total > 0 && (
                      <div className="mb-3">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${calculateProgress(task)}%` }}
                          />
                        </div>
                      </div>
                    )}

                    {/* 配置信息 */}
                    <div className="flex flex-wrap gap-2 mb-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                        深度: {task.config.maxDepth}
                      </span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                        并发: {task.config.maxConcurrency}
                      </span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                        延迟: {task.config.delayMs}ms
                      </span>
                      {task.config.enableProxy && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                          代理
                        </span>
                      )}
                      {task.config.enableAI && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                          AI
                        </span>
                      )}
                    </div>

                    {/* URL 列表（显示前3个） */}
                    <div className="mb-3">
                      <div className="text-sm text-gray-600">
                        {task.urls.slice(0, 3).map((url, index) => (
                          <div key={index} className="flex items-center space-x-2 mb-1">
                            <ExternalLink className="h-3 w-3 text-gray-400" />
                            <span className="truncate">{url}</span>
                          </div>
                        ))}
                        {task.urls.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{task.urls.length - 3} 个更多URL
                          </span>
                        )}
                      </div>
                    </div>

                    {/* 时间信息 */}
                    <div className="text-xs text-gray-500 space-y-1">
                      <div>创建时间: {formatDate(task.createdAt)}</div>
                      {task.startedAt && (
                        <div>开始时间: {formatDate(task.startedAt)}</div>
                      )}
                      {task.completedAt && (
                        <div>完成时间: {formatDate(task.completedAt)}</div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 右侧操作按钮 */}
                <div className="flex flex-col space-y-2 ml-4">
                  {task.status === 'pending' || task.status === 'paused' ? (
                    <Button
                      onClick={() => onStartTask(task.id)}
                      variant="success"
                      size="sm"
                      icon={<Play className="h-4 w-4" />}
                    >
                      启动
                    </Button>
                  ) : task.status === 'running' ? (
                    <>
                      <Button
                        onClick={() => onPauseTask(task.id)}
                        variant="warning"
                        size="sm"
                        icon={<Pause className="h-4 w-4" />}
                      >
                        暂停
                      </Button>
                      <Button
                        onClick={() => onStopTask(task.id)}
                        variant="secondary"
                        size="sm"
                        icon={<Square className="h-4 w-4" />}
                      >
                        停止
                      </Button>
                    </>
                  ) : null}
                  
                  {/* 完成任务查看结果按钮 */}
                  {task.status === 'completed' && (
                    <Button
                      onClick={() => handleViewResults(task.id)}
                      variant="primary"
                      size="sm"
                      icon={<FileText className="h-4 w-4" />}
                    >
                      查看结果
                    </Button>
                  )}
                  
                  {(task.status === 'completed' || task.status === 'failed' || task.status === 'pending') && (
                    <Button
                      onClick={() => handleDeleteClick(task.id, task.name)}
                      variant="danger"
                      size="sm"
                      icon={<Trash2 className="h-4 w-4" />}
                    >
                      删除
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* 任务结果查看弹框 */}
      {showResults && (
        <TaskResultsModal
          isOpen={!!showResults}
          taskId={showResults}
          taskName={tasks.find(t => t.id === showResults)?.name || ''}
          onClose={() => setShowResults(null)}
        />
      )}
      
      {/* 删除确认弹框 */}
      <ConfirmDialog
        isOpen={confirmDelete.isOpen}
        title="确认删除任务"
        message={`确定要删除任务 "${confirmDelete.taskName}" 吗？\n\n此操作不可撤销，任务及其所有数据将被永久删除。`}
        confirmText="删除"
        cancelText="取消"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        variant="danger"
      />
    </div>
  );
};