/**
 * 爬虫管理器
 */

import { EventEmitter } from 'events';
import { FetcherManager } from '../fetcher';
import { ParserManager } from '../parser';
import { StorageManager } from '../storage';
import { NodexConfig } from '@/interfaces/config';
import { FetcherType } from '@/types/common';
import { StorageItem, TraversalStrategy } from '@/interfaces';
import { HttpRequestOptions } from '@/interfaces/fetcher';
import { getLogger, logger } from '@/utils';

/**
 * 爬虫状态枚举
 */
export enum CrawlerState {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error',
}

/**
 * 爬虫统计信息接口
 */
export interface CrawlerStats {
  state: CrawlerState;
  isRunning: boolean;
  totalUrls: number;
  processedUrls: number;
  failedUrls: number;
  queueSize: number;
  currentDepth: number;
  startTime?: Date;
  uptime: number;
  successRate: number;
  avgProcessingTime: number;
}

/**
 * URL队列项接口
 */
interface QueueItem {
  url: string;
  depth: number;
  parentUrl?: string;
  metadata?: Record<string, any>;
}

/**
 * 爬虫管理器类
 */
export class CrawlerManager extends EventEmitter {
  private fetcherManager: FetcherManager;
  private parserManager: ParserManager;
  private storageManager: StorageManager;
  private config: NodexConfig;

  private state: CrawlerState = CrawlerState.IDLE;
  private urlQueue: QueueItem[] = [];
  private processedUrls: Set<string> = new Set();
  private failedUrls: Set<string> = new Set();
  private isRunning = false;
  private startTime: Date | null = null;
  private processingTimes: number[] = [];
  private logger = getLogger('CrawlerManager');

  // 智能并发控制
  private activeTasks: Set<Promise<void>> = new Set();
  private currentConcurrency = 1;
  private maxConcurrency = 5;
  private performanceMetrics = {
    successRate: 0,
    averageResponseTime: 0,
    errorRate: 0,
    lastAdjustmentTime: Date.now()
  };

  constructor(
    fetcherManager: FetcherManager,
    parserManager: ParserManager,
    storageManager: StorageManager,
    config: NodexConfig,
    private taskId?: string
  ) {
    super();
    this.fetcherManager = fetcherManager;
    this.parserManager = parserManager;
    this.storageManager = storageManager;
    this.config = config;

    // 初始化并发控制参数
    this.maxConcurrency = config.concurrency?.maxConcurrentRequests || 5;
    this.currentConcurrency = Math.min(2, this.maxConcurrency); // 从较低并发开始
  }

  /**
   * 启动爬虫
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      throw new Error('爬虫已在运行中');
    }

    try {
      this.setState(CrawlerState.RUNNING);
      this.isRunning = true;
      this.startTime = new Date();

      // 初始化种子URL
      this.initializeSeedUrls();

      // 开始处理队列
      await this.processQueue();

      this.emit('started');
    } catch (error) {
      this.setState(CrawlerState.ERROR);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 停止爬虫
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.setState(CrawlerState.STOPPING);
    this.isRunning = false;

    // 等待当前处理完成
    await this.waitForCompletion();

    this.setState(CrawlerState.STOPPED);
    this.emit('stopped');
  }

  /**
   * 暂停爬虫
   */
  public async pause(): Promise<void> {
    if (this.state !== CrawlerState.RUNNING) {
      throw new Error('只能暂停正在运行的爬虫');
    }

    this.setState(CrawlerState.PAUSED);
    this.emit('paused');
  }

  /**
   * 恢复爬虫
   */
  public async resume(): Promise<void> {
    if (this.state !== CrawlerState.PAUSED) {
      throw new Error('只能恢复已暂停的爬虫');
    }

    this.setState(CrawlerState.RUNNING);
    this.emit('resumed');

    // 继续处理队列
    await this.processQueue();
  }

  /**
   * 清空队列
   */
  public clearQueue(): void {
    this.logger.info('清空URL队列', {
      queueSize: this.urlQueue.length,
      taskId: this.taskId
    });

    this.urlQueue = [];
    this.emit('queue_cleared', {
      taskId: this.taskId,
      timestamp: new Date(),
      reason: 'manual'
    });
  }

  /**
   * 获取失败的URL列表
   */
  public getFailedUrls(): string[] {
    return Array.from(this.failedUrls);
  }

  /**
   * 重试失败的URL
   */
  public async retryFailedUrls(): Promise<number> {
    const failedUrls = Array.from(this.failedUrls);

    if (failedUrls.length === 0) {
      this.logger.info('没有失败的URL需要重试');
      return 0;
    }

    this.logger.info('开始重试失败的URL', {
      failedCount: failedUrls.length,
      taskId: this.taskId
    });

    // 清空失败列表
    this.failedUrls.clear();

    // 将失败的URL重新加入队列
    for (const url of failedUrls) {
      this.urlQueue.push({
        url,
        depth: 0, // 重试时重置深度
        metadata: { retried: true }
      });
    }

    this.emit('failed_urls_retried', {
      taskId: this.taskId,
      retriedCount: failedUrls.length,
      timestamp: new Date(),
      reason: 'manual'
    });

    // 如果爬虫正在运行，立即处理新加入的URL
    if (this.state === CrawlerState.RUNNING) {
      this.processQueue().catch(error => {
        this.logger.error('重试处理队列失败', error);
      });
    }

    return failedUrls.length;
  }

  /**
   * 获取队列大小
   */
  public getQueueSize(): number {
    return this.urlQueue.length;
  }

  /**
   * 获取处理中的任务数量
   */
  public getActiveTasksCount(): number {
    return this.activeTasks.size;
  }

  /**
   * 获取爬虫统计信息
   */
  public getStats(): CrawlerStats {
    const uptime = this.startTime ? Date.now() - this.startTime.getTime() : 0;
    const totalUrls = this.processedUrls.size + this.failedUrls.size;
    const successRate = totalUrls > 0 ? this.processedUrls.size / totalUrls : 0;
    const avgProcessingTime =
      this.processingTimes.length > 0
        ? this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length
        : 0;

    return {
      state: this.state,
      isRunning: this.isRunning,
      totalUrls: this.processedUrls.size + this.failedUrls.size + this.urlQueue.length,
      processedUrls: this.processedUrls.size,
      failedUrls: this.failedUrls.size,
      queueSize: this.urlQueue.length,
      currentDepth: this.getCurrentDepth(),
      startTime: this.startTime || undefined,
      uptime,
      successRate,
      avgProcessingTime,
    };
  }

  /**
   * 添加URL到队列
   */
  public addUrl(
    url: string,
    depth: number = 0,
    parentUrl?: string,
    metadata?: Record<string, any>
  ): void {
    // 解析相对URL为绝对URL
    let resolvedUrl = url;
    if (parentUrl && (url.startsWith('/') || url.startsWith('./') || url.startsWith('../'))) {
      try {
        resolvedUrl = new URL(url, parentUrl).toString();
      } catch (error) {
        // 如果URL解析失败，跳过此URL
        this.logger?.warn(`Failed to resolve relative URL: ${url} with base ${parentUrl}`);
        return;
      }
    }

    // 检查是否已处理或在队列中 (使用解析后的URL)
    if (this.processedUrls.has(resolvedUrl) || this.urlQueue.some(item => item.url === resolvedUrl)) {
      return;
    }

    // 检查深度限制
    if (depth > this.config.depthControl?.global) {
      return;
    }

    // 检查域名限制 (使用解析后的URL)
    if (!this.isAllowedDomain(resolvedUrl)) {
      return;
    }

    this.urlQueue.push({ url: resolvedUrl, depth, parentUrl, metadata });

    // 根据遍历策略排序
    this.sortQueue();
  }

  /**
   * 初始化种子URL
   */
  private initializeSeedUrls(): void {
    if (!this.config.task.seedUrls || this.config.task.seedUrls.length === 0) {
      throw new Error('未配置种子URL');
    }

    for (const url of this.config.task.seedUrls) {
      this.addUrl(url, 0);
    }
  }

  /**
   * 处理URL队列（使用智能并发控制）
   */
  private async processQueue(): Promise<void> {
    let consecutiveFailures = 0;
    const maxConsecutiveFailures = 10;

    while (this.isRunning && (this.urlQueue.length > 0 || this.activeTasks.size > 0)) {
      // 检查是否暂停
      if (this.state === CrawlerState.PAUSED) {
        await this.waitForResume();
      }

      // 检查连续失败次数
      if (consecutiveFailures >= maxConsecutiveFailures) {
        this.logger?.warn(`连续失败次数达到 ${maxConsecutiveFailures}，暂停处理 30 秒`);
        await this.delay(30000);
        consecutiveFailures = 0;
      }

      // 动态调整并发数
      this.adjustConcurrency();

      // 启动新任务直到达到当前并发限制
      while (this.activeTasks.size < this.currentConcurrency && this.urlQueue.length > 0) {
        const item = this.urlQueue.shift();
        if (!item) break;

        const taskPromise = this.processUrlWithRetry(item)
          .then(() => {
            consecutiveFailures = 0; // 成功处理，重置失败计数
          })
          .catch((error) => {
            consecutiveFailures++;
            this.failedUrls.add(item.url);
            this.emit('urlFailed', {
              url: item.url,
              error,
              consecutiveFailures,
              depth: item.depth
            });

            this.logger?.error(`处理URL失败 (${consecutiveFailures}/${maxConsecutiveFailures}):`, {
              url: item.url,
              error: error instanceof Error ? error.message : String(error),
              depth: item.depth
            });
          })
          .finally(() => {
            this.activeTasks.delete(taskPromise);
          });

        this.activeTasks.add(taskPromise);
      }

      // 如果没有活跃任务且队列为空，退出
      if (this.activeTasks.size === 0 && this.urlQueue.length === 0) {
        break;
      }

      // 等待至少一个任务完成
      if (this.activeTasks.size > 0) {
        await Promise.race(Array.from(this.activeTasks));
      }

      // 控制请求频率
      await this.delay(this.config.concurrency?.delayBetweenRequestsMs || 100);
    }

    // 等待所有剩余任务完成
    if (this.activeTasks.size > 0) {
      await Promise.all(Array.from(this.activeTasks));
    }

    // 队列处理完成
    if (this.urlQueue.length === 0 && this.isRunning) {
      this.setState(CrawlerState.STOPPED);
      this.isRunning = false;
      this.emit('completed');
    }
  }

  /**
   * 动态调整并发数
   */
  private adjustConcurrency(): void {
    const now = Date.now();
    const timeSinceLastAdjustment = now - this.performanceMetrics.lastAdjustmentTime;

    // 每60秒调整一次
    if (timeSinceLastAdjustment < 60000) {
      return;
    }

    this.updatePerformanceMetrics();

    const { successRate, averageResponseTime, errorRate } = this.performanceMetrics;

    // 调整逻辑
    if (successRate > 0.95 && averageResponseTime < 3000 && errorRate < 0.05) {
      // 性能良好，可以增加并发
      if (this.currentConcurrency < this.maxConcurrency) {
        this.currentConcurrency = Math.min(this.currentConcurrency + 1, this.maxConcurrency);
        this.logger?.info(`增加并发数到: ${this.currentConcurrency}`);
      }
    } else if (successRate < 0.8 || averageResponseTime > 10000 || errorRate > 0.2) {
      // 性能不佳，减少并发
      if (this.currentConcurrency > 1) {
        this.currentConcurrency = Math.max(this.currentConcurrency - 1, 1);
        this.logger?.info(`减少并发数到: ${this.currentConcurrency}`);
      }
    }

    this.performanceMetrics.lastAdjustmentTime = now;
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(): void {
    const totalProcessed = this.processedUrls.size;
    const totalFailed = this.failedUrls.size;
    const totalAttempts = totalProcessed + totalFailed;

    if (totalAttempts > 0) {
      this.performanceMetrics.successRate = totalProcessed / totalAttempts;
      this.performanceMetrics.errorRate = totalFailed / totalAttempts;
    }

    if (this.processingTimes.length > 0) {
      this.performanceMetrics.averageResponseTime =
        this.processingTimes.reduce((sum, time) => sum + time, 0) / this.processingTimes.length;
    }
  }

  /**
   * 带重试机制的URL处理
   */
  private async processUrlWithRetry(item: QueueItem): Promise<void> {
    const maxRetries = this.config.concurrency?.maxRetries || 3;
    const retryDelay = this.config.concurrency?.retryDelayMs || 5000;

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.info(`processUrl:`, item)
        await this.processUrl(item);
        return; // 成功处理，返回
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        this.logger?.warn(`URL处理失败，尝试第 ${attempt}/${maxRetries} 次:`, {
          url: item.url,
          error: lastError.message,
          attempt
        });

        // 如果不是最后一次尝试，等待后重试
        if (attempt < maxRetries) {
          // 指数退避策略
          const backoffDelay = retryDelay * Math.pow(2, attempt - 1);
          await this.delay(Math.min(backoffDelay, 30000)); // 最大30秒
        }
      }
    }

    // 所有重试都失败，抛出最后的错误
    throw lastError;
  }

  /**
   * 处理单个URL
   */
  private async processUrl(item: QueueItem): Promise<void> {
    const startTime = Date.now();
    let response;
    let parseResult;

    try {
      // 下载页面（带超时控制）
      const fetchTimeout = this.config.concurrency?.requestTimeoutMs || 30000;

      // 构建请求选项
      const requestOptions: HttpRequestOptions = {
        headers: this.config.customHeaders || {},
        userAgent: this.config.userAgent,
        timeout: fetchTimeout,
      };

      // 处理cookies
      if (this.config.cookies) {
        requestOptions.cookies = this.parseCookieString(this.config.cookies);
      }

      // 根据任务配置确定下载器类型
      const preferredType = this.config.task.useBrowser ? 'browser' : 'http';
      
      response = await Promise.race([
        this.fetcherManager.fetch(item.url, requestOptions, preferredType),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Fetch timeout')), fetchTimeout)
        )
      ]) as any;

      if (!response || !response.content) {
        throw new Error('Empty response received');
      }

    } catch (error) {
      throw new Error(`Failed to fetch ${item.url}: ${error instanceof Error ? error.message : String(error)}`);
    }

    try {
      // 解析内容
      parseResult = await this.parserManager.parse({
        url: item.url,
        content: response.content,
        fetcherType: (response.fetcherType as FetcherType) || FetcherType.HTTP,
        headers: response.headers,
        statusCode: response.statusCode,
        responseTime: response.responseTime,
        depth: item.depth,
        parentUrl: item.parentUrl,
        contentType: response.headers['content-type'] || 'text/html',
        metadata: {
          ...item.metadata,
          depth: item.depth,
          parentUrl: item.parentUrl,
          statusCode: response.statusCode,
          headers: response.headers,
        },
      });

      if (parseResult.error) {
        throw new Error(`Parsing failed: ${parseResult.error.message}`);
      }

    } catch (error) {
      throw new Error(`Failed to parse ${item.url}: ${error instanceof Error ? error.message : String(error)}`);
    }

    try {
      // 存储数据
      if (parseResult.extractedData && Object.keys(parseResult.extractedData).length > 0) {
        const storageItem: StorageItem = {
          id: this.generateId(item.url),
          taskId: this.taskId,
          url: item.url,
          title: parseResult.extractedData.title || '',
          content: parseResult.extractedData.content || '',
          description: parseResult.extractedData.description || '',
          rawContent: response.content, // 保存原始HTML内容
          contentType: response.headers['content-type'] || 'text/html',
          crawledAt: new Date(),
          depth: item.depth,
          parentUrl: item.parentUrl,
          tags: parseResult.extractedData.tags ?? [],
          source: {
            fetcherType: response.fetcherType as 'http' | 'browser',
            parserName: parseResult.metadata?.parserName || 'unknown',
            userAgent: this.config.userAgent || 'Nodex-Crawler/1.0',
          },
          metadata: {
            responseTime: response.responseTime,
            responseSize: response.content?.length || 0,
            statusCode: response.statusCode,
            headers: response.headers,
          },
          data: parseResult.extractedData,
          status: 'success'
        };

        await this.storageManager.save(storageItem);
        this.emit('dataExtracted', storageItem);
      }

    } catch (error) {
      this.logger?.warn(`Failed to save data for ${item.url}:`, error);
      // 不抛出错误，继续处理其他URL
    }

    try {
      // 提取新的URL
      if (parseResult.newUrls && parseResult.newUrls.length > 0) {
        let addedUrls = 0;
        for (const link of parseResult.newUrls) {
          if (this.isValidUrl(link)) {
            this.addUrl(link, item.depth + 1, item.url);
            addedUrls++;
          }
        }
        this.logger?.debug(`从 ${item.url} 提取了 ${addedUrls} 个有效链接`);
      }

    } catch (error) {
      this.logger?.warn(`Failed to process links from ${item.url}:`, error);
      // 不抛出错误，继续处理
    }

    this.processedUrls.add(item.url);

    const processingTime = Date.now() - startTime;
    this.processingTimes.push(processingTime);

    // 保持最近1000次的处理时间
    if (this.processingTimes.length > 1000) {
      this.processingTimes.shift();
    }

    this.emit('urlProcessed', {
      url: item.url,
      depth: item.depth,
      processingTime,
      dataExtracted: !!parseResult.extractedData,
      linksFound: parseResult.newUrls?.length || 0,
    });
  }

  /**
   * 验证URL是否有效
   * 注意：此方法现在主要用于验证已解析的绝对URL
   */
  private isValidUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);

      // 检查协议
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return false;
      }

      return true;
    } catch {
      // 如果绝对URL解析失败，检查是否为相对URL
      // 相对URL应该在 addUrl 方法中被解析
      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
        return true; // 相对URL被认为是有效的，会在 addUrl 中解析
      }
      return false;
    }
  }

  /**
   * 设置爬虫状态
   */
  private setState(state: CrawlerState): void {
    const oldState = this.state;
    this.state = state;

    if (oldState !== state) {
      this.emit('stateChanged', { from: oldState, to: state });
    }
  }

  /**
   * 检查是否为允许的域名
   */
  private isAllowedDomain(url: string): boolean {
    if (
      !this.config.crawlScope.allowedDomains ||
      this.config.crawlScope.allowedDomains.length === 0
    ) {
      return true;
    }

    try {
      const domain = new URL(url).hostname;
      return this.config.crawlScope.allowedDomains.some(
        (allowedDomain: string) => domain === allowedDomain || domain.endsWith(`.${allowedDomain}`)
      );
    } catch {
      return false;
    }
  }

  /**
   * 根据遍历策略排序队列
   */
  private sortQueue(): void {
    if (this.config.task.traversalStrategy === TraversalStrategy.DFS) {
      // 深度优先：按深度降序排列
      this.urlQueue.sort((a, b) => b.depth - a.depth);
    } else {
      // 广度优先：按深度升序排列
      this.urlQueue.sort((a, b) => a.depth - b.depth);
    }
  }

  /**
   * 获取当前处理深度
   */
  private getCurrentDepth(): number {
    if (this.urlQueue.length === 0) {
      return 0;
    }
    return Math.min(...this.urlQueue.map(item => item.depth));
  }

  /**
   * 生成唯一ID
   */
  private generateId(url: string): string {
    return Buffer.from(url)
      .toString('base64')
      .replace(/[^a-zA-Z0-9]/g, '')
      // .substring(0, 50);
  }

  /**
   * 等待恢复
   */
  private async waitForResume(): Promise<void> {
    return new Promise(resolve => {
      const checkState = () => {
        if (this.state === CrawlerState.RUNNING) {
          resolve();
        } else {
          setTimeout(checkState, 100);
        }
      };
      checkState();
    });
  }

  /**
   * 等待完成
   */
  private async waitForCompletion(): Promise<void> {
    return new Promise(resolve => {
      const checkQueue = () => {
        if (this.urlQueue.length === 0) {
          resolve();
        } else {
          setTimeout(checkQueue, 100);
        }
      };
      checkQueue();
    });
  }

  /**
   * 延迟函数
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 解析Cookie字符串为键值对对象
   */
  private parseCookieString(cookieString: string): Record<string, string> {
    const cookies: Record<string, string> = {};
    if (!cookieString) return cookies;

    cookieString.split(';').forEach(cookie => {
      const [key, ...valueParts] = cookie.trim().split('=');
      if (key && valueParts.length > 0) {
        cookies[key.trim()] = valueParts.join('=').trim();
      }
    });

    return cookies;
  }
}
