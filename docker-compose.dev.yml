version: '3.8'

services:
  # Redis 服务 - 任务队列
  redis:
    image: redis:7-alpine
    container_name: nodex-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3

  # MongoDB 服务 - 数据存储
  mongodb:
    image: mongo:7
    container_name: nodex-mongodb-dev
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: nodex_dev
    volumes:
      - mongodb_dev_data:/data/db
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 3s
      retries: 3

  # Nodex 开发环境API服务器
  nodex-api-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: nodex-api-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
      - "9229:9229"  # Node.js 调试端口
    environment:
      NODE_ENV: development
      API_PORT: 3000
      API_HOST: 0.0.0.0
      REDIS_HOST: redis
      REDIS_PORT: 6379
      MONGODB_URI: *****************************************************************
      ENABLE_CORS: true
      CORS_ORIGIN: "*"
      DEBUG: "nodex:*"
    depends_on:
      redis:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    volumes:
      - ./src:/app/src:ro
      - ./nodex.config.json:/app/nodex.config.json:ro
      - ./logs:/app/logs
      - ./data:/app/data
      - /app/node_modules  # 防止本地node_modules覆盖容器内的
    command: npm run dev:api

  # 前端开发服务器（可选，也可以在本地运行）
  nodex-frontend-dev:
    build:
      context: ./src/frontend
      dockerfile: Dockerfile.dev
    container_name: nodex-frontend-dev
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3000
      NEXT_PUBLIC_API_VERSION: v1
      NEXT_PUBLIC_WS_URL: ws://localhost:3000
    volumes:
      - ./src/frontend/src:/app/src:ro
      - ./src/frontend/public:/app/public:ro
      - /app/node_modules
      - /app/.next
    command: npm run dev

volumes:
  redis_dev_data:
  mongodb_dev_data:
