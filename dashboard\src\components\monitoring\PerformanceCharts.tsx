import { useEffect, useState } from 'react';
import { TrendingUp, Activity } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import type { SystemStats } from '@/types/monitoring';

// Chart.js 相关导入
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

// 注册 Chart.js 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface PerformanceChartsProps {
  systemStats: SystemStats | null;
  loading?: boolean;
}

interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    tension: number;
    yAxisID?: string;
  }>;
}

const MAX_DATA_POINTS = 20;

export function PerformanceCharts({ systemStats, loading }: PerformanceChartsProps) {
  const [systemMetrics, setSystemMetrics] = useState<ChartData>({
    labels: [],
    datasets: [
      {
        label: '内存使用 (MB)',
        data: [],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.1,
      },
      {
        label: 'CPU使用率 (%)',
        data: [],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.1,
        yAxisID: 'y1',
      },
    ],
  });

  const [crawlerMetrics, setCrawlerMetrics] = useState<ChartData>({
    labels: [],
    datasets: [
      {
        label: 'RPS (请求/秒)',
        data: [],
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.1,
      },
      {
        label: '成功率 (%)',
        data: [],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.1,
        yAxisID: 'y1',
      },
      {
        label: '失败率 (%)',
        data: [],
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.1,
        yAxisID: 'y1',
      },
    ],
  });

  // 图表配置
  const systemChartOptions: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        title: {
          display: true,
          text: '内存 (MB)',
        },
        max: 1000,
        min: 0,
      },
      y1: {
        type: 'linear' as const,
        display: true,
        position: 'right' as const,
        title: {
          display: true,
          text: 'CPU (%)',
        },
        grid: {
          drawOnChartArea: false,
        },
        max: 100,
        min: 0,
      },
    },
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
  };

  const crawlerChartOptions: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        title: {
          display: true,
          text: 'RPS',
        },
        max: 100,
        min: 0,
      },
      y1: {
        type: 'linear' as const,
        display: true,
        position: 'right' as const,
        title: {
          display: true,
          text: '百分比 (%)',
        },
        grid: {
          drawOnChartArea: false,
        },
        max: 100,
        min: 0,
      },
    },
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
  };

  // 更新图表数据
  useEffect(() => {
    if (!systemStats) return;

    const timestamp = new Date().toLocaleTimeString();
    const memoryMB = Math.round(systemStats?.memory.heapUsed / 1024 / 1024);
    const cpuUsage = systemStats?.cpu?.usage || 0;

    // 计算爬虫指标
    const uptimeSeconds = systemStats?.uptime / 1000;
    const rps = uptimeSeconds > 0 ? systemStats?.urls.processed / uptimeSeconds : 0;
    const successRate = systemStats?.urls.successRate * 100;
    const failureRate = 100 - successRate;

    // 更新系统性能图表
    setSystemMetrics(prev => {
      const newLabels = [...prev.labels, timestamp];
      const newMemoryData = [...(prev.datasets[0]?.data || []), memoryMB];
      const newCpuData = [...(prev.datasets[1]?.data || []), cpuUsage];

      // 保持最大数据点数量
      if (newLabels.length > MAX_DATA_POINTS) {
        newLabels.shift();
        newMemoryData.shift();
        newCpuData.shift();
      }

      return {
        labels: newLabels,
        datasets: [
          {
            label: 'Memory (MB)',
            data: newMemoryData,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
          },
          {
            label: 'CPU (%)',
            data: newCpuData,
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
          },
        ],
      };
    });

    // 更新爬虫性能图表
    setCrawlerMetrics(prev => {
      const newLabels = [...prev.labels, timestamp];
      const newRpsData = [...(prev.datasets[0]?.data || []), rps];
      const newSuccessData = [...(prev.datasets[1]?.data || []), successRate];
      const newFailureData = [...(prev.datasets[2]?.data || []), failureRate];

      // 保持最大数据点数量
      if (newLabels.length > MAX_DATA_POINTS) {
        newLabels.shift();
        newRpsData.shift();
        newSuccessData.shift();
        newFailureData.shift();
      }

      return {
        labels: newLabels,
        datasets: [
          {
            label: 'RPS',
            data: newRpsData,
            borderColor: 'rgb(239, 68, 68)',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            tension: 0.4,
          },
          {
            label: 'Success Rate (%)',
            data: newSuccessData,
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            tension: 0.4,
          },
          {
            label: 'Failure Rate (%)',
            data: newFailureData,
            borderColor: 'rgb(251, 146, 60)',
            backgroundColor: 'rgba(251, 146, 60, 0.1)',
            tension: 0.4,
          },
        ],
      };
    });
  }, [systemStats]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.from({ length: 2 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded animate-pulse w-32"></div>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-100 rounded animate-pulse flex items-center justify-center">
                <LoadingSpinner size="lg" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 系统性能图表 */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Activity className="h-5 w-5 mr-2 text-green-500" />
            系统性能
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <Line data={systemMetrics} options={systemChartOptions} />
          </div>
        </CardContent>
      </Card>

      {/* 爬虫性能图表 */}
      <Card>
        <CardHeader>
          <CardTitle>
            <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
            爬虫性能
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <Line data={crawlerMetrics} options={crawlerChartOptions} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
