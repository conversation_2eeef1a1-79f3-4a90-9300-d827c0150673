
import puppeteer from 'puppeteer';
import { addExtra } from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';


// 使用 puppeteer-extra 和 stealth 插件
const puppeteerExtra = addExtra(puppeteer);
puppeteerExtra.use(StealthPlugin());

export async function googleSearch(query, userAgent) {
  try {
    const browser = await puppeteerExtra.launch();
    const page = await browser.newPage();
    await page.setUserAgent(userAgent);
    const response = await page.goto(
      `https://www.google.com.hk/search?q=${encodeURIComponent(query)}&hl=en&gl=us`);
    let results = [];
    if (response.status() === 200) {
      results = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('div.yuRUbf > a')).map(a => ({
          href: a.href,
          title: a.querySelector('h3') ? a.querySelector('h3').textContent : null,
          abstract: ''
        }));
      });
    } else {
      results.push({ href: null, title: null, abstract: 'HTTP Status : ' + response.status() });
    }
    await browser.close();
    return results;
  } catch (error) {
    console.error("An error occurred:", error);
    return [{ href: null, title: null, abstract: 'Error: ' + error.message }];
  }
}

export async function bingSearch(query, userAgent) {
  try {
    const browser = await puppeteerExtra.launch();
    const page = await browser.newPage();
    await page.setUserAgent(userAgent);
    const response = await page.goto(
      `https://www.bing.com/search?q=${encodeURIComponent(query)}&cc=US`
    );
    let results = [];
    if (response.status() === 200) {
      results = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('li.b_algo')).map(li => {
          const a = li.querySelector('a');
          const title = a ? a.textContent : null;
          const abstract = li.querySelector('.b_caption > p') ? li.querySelector('.b_caption > p').textContent : '';
          return {
            href: a ? a.href : null,
            title,
            abstract
          };
        });
      });
    } else {
      results.push({ href: null, title: null, abstract: 'HTTP Status : ' + response.status() });
    }
    await browser.close();
    return results;
  } catch (error) {
    console.error("An error occurred:", error);
    return [{ href: null, title: null, abstract: 'Error: ' + error.message }];
  }
}

export async function yahooSearch(query, userAgent) {
  try {
    const browser = await puppeteerExtra.launch();
    const page = await browser.newPage();
    await page.setUserAgent(userAgent);
    const response = await page.goto(
      `https://search.yahoo.com/search?p=${encodeURIComponent(query)}&ei=UTF-8&fr=fp-tts`
    );
    let results = [];
    if (response.status() === 200) {
      results = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('.d-ib.ls-05.fz-20.lh-26.td-hu.tc.va-bot.mxw-100p')).map(a => ({
          href: a.getAttribute('href'),
          title: a.textContent || null,
          abstract: ''
        }));
      });
    } else {
      results.push({ href: null, title: null, abstract: 'HTTP Status : ' + response.status() });
    }
    await browser.close();
    return results;
  } catch (error) {
    console.error("An error occurred:", error);
    return [{ href: null, title: null, abstract: 'Error: ' + error.message }];
  }
}

export async function duckduckgoSearch(query, userAgent) {
  try {
    const browser = await puppeteerExtra.launch();
    const page = await browser.newPage();
    await page.setUserAgent(userAgent);
    const response = await page.goto(
      `https://duckduckgo.com/html/?q=${encodeURIComponent(query)}&kl=hk-tzh&ia=web`
    );
    let results = [];
    if (response.status() === 200) {
      results = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('a.result__a')).map(a => ({
          href: a.href,
          title: a.textContent || null,
          abstract: ''
        }));
      });
    } else {
      results.push({ href: null, title: null, abstract: 'HTTP Status : ' + response.status() });
    }
    await browser.close();
    return results;
  } catch (error) {
    console.error("An error occurred:", error);
    return [{ href: null, title: null, abstract: 'Error: ' + error.message }];
  }
}

export async function givewaterSearch(query, userAgent) {
  try {
    const browser = await puppeteerExtra.launch();
    const page = await browser.newPage();
    await page.setUserAgent(userAgent);
    const response = await page.goto(
      `https://search.givewater.com/serp?q=${encodeURIComponent(query)}`
    );
    let results = [];
    if (response.status() === 200) {
      results = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('div.web-bing__result')).map(div => {
          const a = div.querySelector('a');
          return {
            href: a ? a.href : null,
            title: a ? a.textContent : null,
            abstract: ''
          };
        });
      });
    } else {
      results.push({ href: null, title: null, abstract: 'HTTP Status : ' + response.status() });
    }
    await browser.close();
    return results;
  } catch (error) {
    console.error("An error occurred:", error);
    return [{ href: null, title: null, abstract: 'Error: ' + error.message }];
  }
}

export async function ecosiaSearch(query, userAgent) {
  try {
    const browser = await puppeteerExtra.launch();
    const page = await browser.newPage();
    await page.setUserAgent(userAgent);
    const response = await page.goto(
      `https://www.ecosia.org/search?q=${encodeURIComponent(query)}`
    );
    let results = [];
    if (response.status() === 200) {
      results = await page.evaluate(() => {
        return Array.from(document.querySelectorAll('div.mainline__result-wrapper')).map(div => {
          const a = div.querySelector('a');
          return {
            href: a ? a.href : null,
            title: a ? a.textContent : null,
            abstract: ''
          };
        });
      });
    } else {
      results.push({ href: null, title: null, abstract: 'HTTP Status : ' + response.status() });
    }
    await browser.close();
    return results;
  } catch (error) {
    console.error("An error occurred:", error);
    return [{ href: null, title: null, abstract: 'Error: ' + error.message }];
  }
}
