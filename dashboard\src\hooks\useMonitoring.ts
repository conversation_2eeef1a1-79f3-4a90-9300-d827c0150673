'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSocket } from './useSocket';
import apiClient from '@/lib/api';
import { DataValidator, DataSanitizer, DataDefaults } from '@/lib/data-validation';
import type {
  SystemStats,
  QueueStats,
  ModuleStatus,
  CrawlSession,
  HealthCheckResult,
  LogEntry,
} from '@/types/monitoring';

/**
 * 系统监控数据管理 Hook
 */
export function useMonitoring() {
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [queueStats, setQueueStats] = useState<QueueStats[]>([]);
  const [moduleStatus, setModuleStatus] = useState<ModuleStatus[]>([]);
  const [sessions, setSessions] = useState<CrawlSession[]>([]);
  const [healthStatus, setHealthStatus] = useState<HealthCheckResult | null>(
    null
  );
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 使用单一 Socket 连接
  const { on, off, emit } = useSocket();

  // 设置 Socket 事件监听
  useEffect(() => {
    // 系统状态更新
    const handleStatusUpdate = (data: any) => {
      try {
        // 验证数据
        const validation = DataValidator.validateSystemStats(data);
        if (!validation.isValid) {
          console.error('系统状态数据验证失败:', validation.errors);
          // 使用默认数据
          setSystemStats(DataDefaults.getSystemStats());
          return;
        }

        // 清理和修复数据
        const sanitizedData = DataSanitizer.sanitizeSystemStats(data);
        setSystemStats(sanitizedData);
        setError(null);

        // 记录警告
        if (validation.warnings.length > 0) {
          console.warn('系统状态数据警告:', validation.warnings);
        }
      } catch (error) {
        console.error('处理系统状态更新时出错:', error);
        setSystemStats(DataDefaults.getSystemStats());
      }
    };

    // 队列状态更新
    const handleQueuesUpdate = (data: any) => {
      try {
        const validation = DataValidator.validateQueueStats(data);
        if (!validation.isValid) {
          console.error('队列状态数据验证失败:', validation.errors);
          setQueueStats(DataDefaults.getQueueStats());
          return;
        }

        const sanitizedData = DataSanitizer.sanitizeQueueStats(data);
        setQueueStats(sanitizedData);

        if (validation.warnings.length > 0) {
          console.warn('队列状态数据警告:', validation.warnings);
        }
      } catch (error) {
        console.error('处理队列状态更新时出错:', error);
        setQueueStats(DataDefaults.getQueueStats());
      }
    };

    // 模块状态更新
    const handleModulesUpdate = (data: any) => {
      try {
        const validation = DataValidator.validateModuleStatus(data);
        if (!validation.isValid) {
          console.error('模块状态数据验证失败:', validation.errors);
          setModuleStatus(DataDefaults.getModuleStatus());
          return;
        }

        const sanitizedData = DataSanitizer.sanitizeModuleStatus(data);
        setModuleStatus(sanitizedData);

        if (validation.warnings.length > 0) {
          console.warn('模块状态数据警告:', validation.warnings);
        }
      } catch (error) {
        console.error('处理模块状态更新时出错:', error);
        setModuleStatus(DataDefaults.getModuleStatus());
      }
    };

    // 会话状态更新
    const handleSessionsUpdate = (data: any) => {
      try {
        if (!Array.isArray(data)) {
          console.error('会话数据必须是数组:', data);
          setSessions(DataDefaults.getSessions());
          return;
        }

        // 简单验证和清理会话数据
        const sanitizedSessions = data.filter(session =>
          session && typeof session === 'object' && session.id
        );

        setSessions(sanitizedSessions);
      } catch (error) {
        console.error('处理会话状态更新时出错:', error);
        setSessions(DataDefaults.getSessions());
      }
    };

    // 系统事件
    const handleSystemEvent = (event: any) => {
      if (event.status && event.uptime !== undefined) {
        setSystemStats(event);
      }
      const logEntry: LogEntry = {
        level: 'info',
        message: `系统事件: ${event.type || 'status-update'}`,
        timestamp: new Date(event.timestamp || Date.now()),
        context: event.data || event,
      };
      setLogs(prev => [logEntry, ...prev].slice(0, 100));
    };

    // 会话事件
    const handleSessionEvent = (event: any) => {
      if (event.id && event.name) {
        setSessions(prev => {
          const index = prev.findIndex(s => s.id === event.id);
          if (index >= 0) {
            const updated = [...prev];
            updated[index] = event;
            return updated;
          } else {
            return [event, ...prev];
          }
        });
      }
      const logEntry: LogEntry = {
        level: 'info',
        message: `会话事件: ${event.type || 'session-update'} (${event.sessionId || event.id})`,
        timestamp: new Date(event.timestamp || Date.now()),
        context: event.data || event,
      };
      setLogs(prev => [logEntry, ...prev].slice(0, 100));
    };

    // 错误事件
    const handleError = (error: any) => {
      try {
        let errorMessage = '未知Socket.IO错误';

        if (error && typeof error === 'object') {
          errorMessage = error.message || error.error || JSON.stringify(error);
        } else if (typeof error === 'string') {
          errorMessage = error;
        }

        setError(errorMessage);

        const logEntry: LogEntry = {
          level: 'error',
          message: `Socket.IO错误: ${errorMessage}`,
          timestamp: new Date(),
          context: {
            source: 'socket',
            originalError: error,
            timestamp: new Date().toISOString(),
          },
        };

        setLogs(prev => [logEntry, ...prev].slice(0, 100));
        console.error('Socket.IO错误详情:', error);
      } catch (err) {
        console.error('处理Socket.IO错误时出错:', err);
        setError('处理错误信息时发生异常');
      }
    };

    // 注册所有事件监听器
    on('status-update', handleStatusUpdate);
    on('queues-update', handleQueuesUpdate);
    on('modules-update', handleModulesUpdate);
    on('sessions-update', handleSessionsUpdate);
    on('system-event', handleSystemEvent);
    on('session-event', handleSessionEvent);
    on('error', handleError);

    // 清理函数
    return () => {
      off('status-update', handleStatusUpdate);
      off('queues-update', handleQueuesUpdate);
      off('modules-update', handleModulesUpdate);
      off('sessions-update', handleSessionsUpdate);
      off('system-event', handleSystemEvent);
      off('session-event', handleSessionEvent);
      off('error', handleError);
    };
  }, [on, off]);

  // 初始数据加载
  const loadInitialData = useCallback(async () => {
    try {
      console.log("invoke: loadInitialData...")
      setLoading(true);
      setError(null);

      const [systemData, queuesData, modulesData, sessionsData, healthData] =
        await Promise.allSettled([
          apiClient.getSystemStats(),
          apiClient.getQueueStats(),
          apiClient.getModuleStatus(),
          apiClient.getSessions(),
          apiClient.healthCheck(),
        ]);

      // 直接使用后端返回的标准化数据，无需转换
      if (systemData.status === 'fulfilled') {
        setSystemStats(systemData.value);
      }
      if (queuesData.status === 'fulfilled') {
        setQueueStats(queuesData.value);
      }
      if (modulesData.status === 'fulfilled') {
        setModuleStatus(modulesData.value);
      }
      if (sessionsData.status === 'fulfilled') {
        setSessions(sessionsData.value);
      }
      if (healthData.status === 'fulfilled') {
        setHealthStatus(healthData.value);
      }

      // 检查是否有任何请求失败
      const failures = [
        systemData,
        queuesData,
        modulesData,
        sessionsData,
        healthData,
      ]
        .filter(result => result.status === 'rejected')
        .map(result => (result as PromiseRejectedResult).reason);

      if (failures.length > 0) {
        console.warn('部分数据加载失败:', failures);
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : '加载数据失败';
      setError(message);
      console.error('加载监控数据失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 刷新数据
  const refreshData = useCallback(() => {
    loadInitialData();
  }, [loadInitialData]);

  // 添加日志条目
  const addLog = useCallback((entry: Omit<LogEntry, 'timestamp'>) => {
    const logEntry: LogEntry = {
      ...entry,
      timestamp: new Date(),
    };
    setLogs(prev => [logEntry, ...prev].slice(0, 100));
  }, []);

  // 清空日志
  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  // 组件挂载时加载初始数据
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  return {
    // 数据状态
    systemStats,
    queueStats,
    moduleStatus,
    sessions,
    healthStatus,
    logs,
    loading,
    error,

    // 操作方法
    refreshData,
    addLog,
    clearLogs,
    emit, // Socket emit 函数

    // 计算属性
    isSystemHealthy: healthStatus?.status === 'healthy',
    totalActiveJobs: queueStats.reduce((sum, queue) => sum + queue.active, 0),
    totalWaitingJobs: queueStats.reduce((sum, queue) => sum + queue.waiting, 0),
    totalFailedJobs: queueStats.reduce((sum, queue) => sum + queue.failed, 0),
    activeSessions: sessions.filter(s => s.status === 'running').length,
  };
}

/**
 * 系统控制操作 Hook
 */
export function useSystemControl() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const executeAction = useCallback(
    async (action: () => Promise<any>, successMessage: string) => {
      try {
        setLoading(true);
        setError(null);
        await action();
        // 这里可以添加成功通知
        console.log(successMessage);
      } catch (err) {
        const message = err instanceof Error ? err.message : '操作失败';
        setError(message);
        console.error('系统控制操作失败:', err);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const pauseSystem = useCallback(() => {
    return executeAction(() => apiClient.pauseSystem(), '系统已暂停');
  }, [executeAction]);

  const resumeSystem = useCallback(() => {
    return executeAction(() => apiClient.resumeSystem(), '系统已恢复');
  }, [executeAction]);

  const restartSystem = useCallback(() => {
    return executeAction(() => apiClient.restartSystem(), '系统已重启');
  }, [executeAction]);

  const pauseQueue = useCallback(
    (queueName: string) => {
      return executeAction(
        () => apiClient.pauseQueue(queueName),
        `队列 ${queueName} 已暂停`
      );
    },
    [executeAction]
  );

  const resumeQueue = useCallback(
    (queueName: string) => {
      return executeAction(
        () => apiClient.resumeQueue(queueName),
        `队列 ${queueName} 已恢复`
      );
    },
    [executeAction]
  );

  const clearQueue = useCallback(
    (queueName: string) => {
      return executeAction(
        () => apiClient.clearQueue(queueName),
        `队列 ${queueName} 已清空`
      );
    },
    [executeAction]
  );

  const retryFailedJobs = useCallback(
    (queueName: string) => {
      return executeAction(
        () => apiClient.retryFailedJobs(queueName),
        `队列 ${queueName} 的失败任务已重试`
      );
    },
    [executeAction]
  );

  return {
    loading,
    error,
    pauseSystem,
    resumeSystem,
    restartSystem,
    pauseQueue,
    resumeQueue,
    clearQueue,
    retryFailedJobs,
  };
}
