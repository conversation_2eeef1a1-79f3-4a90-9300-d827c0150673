import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';

interface ProxyFormData {
  host: string;
  port: number;
  protocol: string;
  username: string;
  password: string;
  weight: number;
  priority: number;
  countryCode: string;
  cityName: string;
  asnName: string;
  asnNumber: number;
}

interface AddProxyModalProps {
  onAdd: (proxyData: any) => void;
  onClose: () => void;
}

export const AddProxyModal: React.FC<AddProxyModalProps> = ({ onAdd, onClose }) => {
  const [formData, setFormData] = useState<ProxyFormData>({
    host: '',
    port: 8080,
    protocol: 'http',
    username: '',
    password: '',
    weight: 1,
    priority: 0,
    countryCode: '',
    cityName: '',
    asnName: '',
    asnNumber: 0,
  });

  const [mode, setMode] = useState<'single' | 'batch'>('single');
  const [batchText, setBatchText] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (mode === 'single') {
      if (!formData.host.trim()) {
        newErrors.host = '主机地址不能为空';
      }
      if (formData.port < 1 || formData.port > 65535) {
        newErrors.port = '端口号必须在 1-65535 之间';
      }
      if (formData.weight < 1) {
        newErrors.weight = '权重必须大于 0';
      }
      if (formData.priority < 0) {
        newErrors.priority = '优先级不能为负数';
      }
    } else {
      if (!batchText.trim()) {
        newErrors.batch = '批量代理数据不能为空';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (mode === 'single') {
      const proxyConfig = {
        host: formData.host.trim(),
        port: formData.port,
        protocol: formData.protocol,
        ...(formData.username && formData.password && {
          auth: {
            username: formData.username,
            password: formData.password,
          },
        }),
        // 地域信息
        ...(formData.countryCode && { countryCode: formData.countryCode.trim() }),
        ...(formData.cityName && { cityName: formData.cityName.trim() }),
        ...(formData.asnName && { asnName: formData.asnName.trim() }),
        ...(formData.asnNumber && { asnNumber: formData.asnNumber }),
      };

      onAdd({
        ...proxyConfig,
        weight: formData.weight,
        priority: formData.priority,
      });
    } else {
      // 批量添加逻辑
      try {
        const lines = batchText.trim().split('\n');
        const proxies = lines.map(line => {
          const parts = line.trim().split(':');
          if (parts.length < 2) {
            throw new Error(`无效的代理格式: ${line}`);
          }

          const [host, port, username, password] = parts;
          const proxyConfig: any = {
            host: host.trim(),
            port: parseInt(port.trim()),
            protocol: 'http',
          };

          if (username && password) {
            proxyConfig.auth = {
              username: username.trim(),
              password: password.trim(),
            };
          }

          return proxyConfig;
        });

        onAdd({ proxies });
      } catch (error) {
        setErrors({ batch: error instanceof Error ? error.message : '批量数据格式错误' });
        return;
      }
    }
  };

  const handleInputChange = (field: keyof ProxyFormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">添加代理</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 模式切换 */}
        <div className="mb-6">
          <div className="flex space-x-4">
            <button
              onClick={() => setMode('single')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                mode === 'single'
                  ? 'bg-blue-100 text-blue-700 border border-blue-300'
                  : 'bg-gray-100 text-gray-700 border border-gray-300'
              }`}
            >
              单个添加
            </button>
            <button
              onClick={() => setMode('batch')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                mode === 'batch'
                  ? 'bg-blue-100 text-blue-700 border border-blue-300'
                  : 'bg-gray-100 text-gray-700 border border-gray-300'
              }`}
            >
              批量添加
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          {mode === 'single' ? (
            <div className="space-y-4">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    主机地址 *
                  </label>
                  <input
                    type="text"
                    value={formData.host}
                    onChange={(e) => handleInputChange('host', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                      errors.host ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="例: ***********"
                  />
                  {errors.host && <p className="mt-1 text-sm text-red-600">{errors.host}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    端口 *
                  </label>
                  <input
                    type="number"
                    value={formData.port}
                    onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 0)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                      errors.port ? 'border-red-300' : 'border-gray-300'
                    }`}
                    min="1"
                    max="65535"
                  />
                  {errors.port && <p className="mt-1 text-sm text-red-600">{errors.port}</p>}
                </div>
              </div>

              {/* 协议选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  协议
                </label>
                <select
                  value={formData.protocol}
                  onChange={(e) => handleInputChange('protocol', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="http">HTTP</option>
                  <option value="https">HTTPS</option>
                  <option value="socks4">SOCKS4</option>
                  <option value="socks5">SOCKS5</option>
                </select>
              </div>

              {/* 认证信息 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    用户名
                  </label>
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="可选"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    密码
                  </label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="可选"
                  />
                </div>
              </div>

              {/* 高级设置 */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    权重
                  </label>
                  <input
                    type="number"
                    value={formData.weight}
                    onChange={(e) => handleInputChange('weight', parseInt(e.target.value) || 1)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                      errors.weight ? 'border-red-300' : 'border-gray-300'
                    }`}
                    min="1"
                  />
                  {errors.weight && <p className="mt-1 text-sm text-red-600">{errors.weight}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    优先级
                  </label>
                  <input
                    type="number"
                    value={formData.priority}
                    onChange={(e) => handleInputChange('priority', parseInt(e.target.value) || 0)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                      errors.priority ? 'border-red-300' : 'border-gray-300'
                    }`}
                    min="0"
                  />
                  {errors.priority && <p className="mt-1 text-sm text-red-600">{errors.priority}</p>}
                </div>
              </div>
            </div>
          ) : (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                批量代理数据 *
              </label>
              <textarea
                value={batchText}
                onChange={(e) => setBatchText(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.batch ? 'border-red-300' : 'border-gray-300'
                }`}
                rows={8}
                placeholder="每行一个代理，格式：host:port:username:password&#10;例如：&#10;***********:8080&#10;***********:8080:user:pass"
              />
              {errors.batch && <p className="mt-1 text-sm text-red-600">{errors.batch}</p>}
              <p className="mt-1 text-sm text-gray-500">
                格式：host:port 或 host:port:username:password
              </p>
            </div>
          )}

          {/* 按钮 */}
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              type="button"
              onClick={onClose}
              variant="secondary"
            >
              取消
            </Button>
            <Button
              type="submit"
              variant="primary"
            >
              {mode === 'single' ? '添加代理' : '批量添加'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
