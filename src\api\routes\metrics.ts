/**
 * 精简版监控指标路由
 * 
 * 提供系统性能监控的核心API端点
 */

import { Router } from 'express';
import { TaskManager } from '@/app/task-manager';
import { error } from '@/utils';

/**
 * 创建监控指标路由
 */
export function createMetricsRoutes(
  taskManager: TaskManager
): Router {
  const router = Router();

  /**
   * GET /metrics/system - 获取系统性能指标
   */
  router.get('/system', async (req, res) => {
    try {
      // 使用TaskManager获取统计数据
      const stats = taskManager.getCrawlerStats();
      const tasks = taskManager.getAllTasks();
      
      const systemMetrics = {
        cpu: {
          usage: process.cpuUsage(),
          loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0],
        },
        memory: {
          usage: process.memoryUsage(),
          total: require('os').totalmem(),
          free: require('os').freemem(),
        },
        uptime: {
          process: process.uptime(),
          system: require('os').uptime(),
        },
        tasks: {
          total: tasks.length,
          running: tasks.filter(t => t.status === 'running').length,
          completed: tasks.filter(t => t.status === 'completed').length,
          failed: tasks.filter(t => t.status === 'failed').length,
        },
        timestamp: new Date().toISOString(),
      };

      res.json({
        success: true,
        data: systemMetrics,
        stats,
      });
    } catch (err) {
      error('获取系统指标失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get system metrics',
      });
    }
  });

  /**
   * GET /metrics/crawler - 获取爬虫性能指标
   */
  router.get('/crawler', async (req, res) => {
    try {
      // 使用TaskManager获取爬虫统计数据
      const stats = taskManager.getCrawlerStats();
      const tasks = taskManager.getAllTasks();
      
      const crawlerMetrics = {
        tasks: {
          total: stats.totalUrls,
          processed: stats.processedUrls,
          failed: stats.failedUrls,
          inQueue: stats.queueSize,
        },
        performance: {
          averageResponseTime: (stats as any).averageResponseTime || 0,
          requestsPerSecond: (stats as any).requestsPerSecond || 0,
          successRate: stats.totalUrls > 0 ? (stats.processedUrls / stats.totalUrls) * 100 : 0,
        },
        modules: {
          crawler: true, // TaskManager包含爬虫功能
          fetcher: true, // TaskManager包含下载功能
          parser: true,  // TaskManager包含解析功能
          storage: true, // TaskManager包含存储功能
        },
        timestamp: new Date().toISOString(),
      };

      res.json({
        success: true,
        data: crawlerMetrics,
      });
    } catch (err) {
      error('获取爬虫指标失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get crawler metrics',
      });
    }
  });

  /**
   * GET /metrics/history - 获取历史性能数据
   */
  router.get('/history', async (req, res) => {
    try {
      const { hours = 1, interval = 5 } = req.query;
      
      // 模拟历史数据（实际实现中应该从数据库或缓存中获取）
      const now = Date.now();
      const hoursNum = parseInt(hours as string, 10);
      const intervalNum = parseInt(interval as string, 10);
      const points = Math.floor((hoursNum * 60) / intervalNum);
      
      const historyData = [];
      for (let i = points; i >= 0; i--) {
        const timestamp = new Date(now - (i * intervalNum * 60 * 1000));
        historyData.push({
          timestamp: timestamp.toISOString(),
          cpu: Math.random() * 100,
          memory: Math.random() * 100,
          requests: Math.floor(Math.random() * 1000),
          errors: Math.floor(Math.random() * 10),
        });
      }

      res.json({
        success: true,
        data: {
          period: `${hours} hours`,
          interval: `${interval} minutes`,
          points: historyData.length,
          metrics: historyData,
        },
      });
    } catch (err) {
      error('获取历史数据失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get history metrics',
      });
    }
  });

  return router;
}
