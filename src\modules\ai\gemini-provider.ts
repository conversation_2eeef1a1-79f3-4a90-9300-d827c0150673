/**
 * Google Gemini 提供商实现
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AIProvider, AIResponse, AIRequestOptions, AIModelConfig } from '@/interfaces';
import { info, error } from '@/utils';

/**
 * Gemini API 响应接口
 */
interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
      role: string;
    };
    finishReason: string;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

/**
 * Gemini 提供商实现类
 */
export class GeminiProvider implements IAIProvider {
  public readonly name: AIProvider = 'gemini';
  public readonly version = '1.0.0';

  private apiKey: string;
  private baseUrl: string;
  private defaultModel: AIModelConfig;
  private stats = {
    totalRequests: 0,
    totalTokens: 0,
    totalResponseTime: 0,
    errorCount: 0,
  };

  constructor(config: {
    apiKey: string;
    baseUrl?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
  }) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta';
    this.defaultModel = {
      model: config.model || 'gemini-pro',
      maxTokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.1,
    };
  }

  /**
   * 初始化提供商
   */
  public async initialize(): Promise<void> {
    try {
      await this.testConnection();
      info('Gemini provider initialized successfully');
    } catch (err) {
      error('Failed to initialize Gemini provider:', err);
      throw err;
    }
  }

  /**
   * 发送聊天请求
   */
  public async chat(
    messages: Array<{
      role: 'system' | 'user' | 'assistant';
      content: string;
    }>,
    options: AIRequestOptions = {}
  ): Promise<AIResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      const model = options.model || this.defaultModel;
      const timeout = options.timeout || 30000;

      // 转换消息格式为 Gemini 格式
      const geminiMessages = this.convertMessagesToGeminiFormat(messages);

      const requestBody: any = {
        contents: geminiMessages,
        generationConfig: {
          maxOutputTokens: model.maxTokens,
          temperature: model.temperature,
          topP: 0.8,
          topK: 40,
        },
        safetySettings: [
          {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
        ],
      };

      const response = await this.makeRequest(
        `/models/${model.model}:generateContent`,
        requestBody,
        timeout
      );

      const responseTime = Date.now() - startTime;
      this.stats.totalResponseTime += responseTime;

      if (response.usageMetadata) {
        this.stats.totalTokens += response.usageMetadata.totalTokenCount;
      }

      if (!response.candidates || response.candidates.length === 0) {
        throw new Error('No candidates in Gemini response');
      }

      const candidate = response.candidates[0];
      const content = candidate?.content.parts.map(part => part.text).join('') || '';

      return {
        content,
        tokensUsed: {
          prompt: response.usageMetadata?.promptTokenCount || 0,
          completion: response.usageMetadata?.candidatesTokenCount || 0,
          total: response.usageMetadata?.totalTokenCount || 0,
        },
        responseTime,
        model: model.model,
        finishReason: this.mapFinishReason(candidate?.finishReason),
        rawResponse: response,
      };
    } catch (error) {
      this.stats.errorCount++;
      throw this.handleError(error);
    }
  }

  /**
   * 发送完成请求
   */
  public async complete(prompt: string, options: AIRequestOptions = {}): Promise<AIResponse> {
    const messages = [{ role: 'user' as const, content: prompt }];

    return await this.chat(messages, options);
  }

  /**
   * 检查健康状态
   */
  public async healthCheck(): Promise<boolean> {
    try {
      await this.testConnection();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取使用统计
   */
  public getUsageStats(): {
    totalRequests: number;
    totalTokens: number;
    averageResponseTime: number;
    errorRate: number;
  } {
    return {
      totalRequests: this.stats.totalRequests,
      totalTokens: this.stats.totalTokens,
      averageResponseTime:
        this.stats.totalRequests > 0 ? this.stats.totalResponseTime / this.stats.totalRequests : 0,
      errorRate:
        this.stats.totalRequests > 0 ? this.stats.errorCount / this.stats.totalRequests : 0,
    };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalRequests: 0,
      totalTokens: 0,
      totalResponseTime: 0,
      errorCount: 0,
    };
  }

  /**
   * 测试连接
   */
  private async testConnection(): Promise<void> {
    try {
      await this.complete('Hello', {
        model: {
          model: this.defaultModel.model,
          maxTokens: 10,
          temperature: 0,
        },
        timeout: 10000,
      });
    } catch (error) {
      throw new Error(
        `Gemini connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 发送 HTTP 请求
   */
  private async makeRequest(endpoint: string, body: any, timeout: number): Promise<GeminiResponse> {
    const url = `${this.baseUrl}${endpoint}?key=${this.apiKey}`;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `Gemini API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`
        );
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }

      throw error;
    }
  }

  /**
   * 转换消息格式为 Gemini 格式
   */
  private convertMessagesToGeminiFormat(
    messages: Array<{
      role: 'system' | 'user' | 'assistant';
      content: string;
    }>
  ): Array<{
    role: string;
    parts: Array<{ text: string }>;
  }> {
    const geminiMessages: Array<{
      role: string;
      parts: Array<{ text: string }>;
    }> = [];

    let systemMessage = '';

    for (const message of messages) {
      if (message.role === 'system') {
        systemMessage += message.content + '\n';
      } else {
        const role = message.role === 'assistant' ? 'model' : 'user';

        // 如果是第一个用户消息且有系统消息，合并它们
        if (role === 'user' && systemMessage && geminiMessages.length === 0) {
          geminiMessages.push({
            role: 'user',
            parts: [{ text: systemMessage + message.content }],
          });
          systemMessage = '';
        } else {
          geminiMessages.push({
            role,
            parts: [{ text: message.content }],
          });
        }
      }
    }

    return geminiMessages;
  }

  /**
   * 映射完成原因
   */
  private mapFinishReason(
    reason?: string
  ): 'stop' | 'length' | 'content_filter' | 'function_call' | undefined {
    if (!reason) return undefined;

    switch (reason) {
      case 'STOP':
        return 'stop';
      case 'MAX_TOKENS':
        return 'length';
      case 'SAFETY':
        return 'content_filter';
      case 'RECITATION':
        return 'content_filter';
      default:
        return undefined; // 返回undefined而不是'other'
    }
  }

  /**
   * 处理错误
   */
  private handleError(error: any): Error {
    if (error instanceof Error) {
      return error;
    }

    if (typeof error === 'string') {
      return new Error(error);
    }

    return new Error('Unknown Gemini API error');
  }
}
