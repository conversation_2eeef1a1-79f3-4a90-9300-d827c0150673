import { useState, useCallback } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Navigation } from '@/components/layout/Navigation';
import { MonitoringPage, ProxyPage, CrawlerPage, ApiFetchPage } from '@/pages';
import { NotificationProvider } from '@/contexts/NotificationContext';

function App() {
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // 使用 useCallback 稳定回调函数引用
  const handleSystemUpdate = useCallback(() => {
    setLastUpdate(new Date());
  }, []);

  return (
    <NotificationProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Navigation lastUpdate={lastUpdate} />
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <Routes>
              <Route path="/" element={<MonitoringPage onSystemUpdate={handleSystemUpdate} />} />
              <Route path="/crawler" element={<CrawlerPage />} />
              <Route path="/proxy" element={<ProxyPage />} />
              <Route path="/fetch" element={<ApiFetchPage />} />
            </Routes>
          </main>
        </div>
      </Router>
    </NotificationProvider>
  );
}

export default App;