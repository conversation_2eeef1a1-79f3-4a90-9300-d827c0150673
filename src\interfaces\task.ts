/**
 * 任务相关接口定义
 */

import { UrlMetadata, ErrorInfo, FetcherType } from '@/types/common';
import { QueueStats, TaskStatus } from '@/shared-types';

/**
 * 爬取任务接口
 */
export interface CrawlTask {
  /** 任务唯一标识符 */
  id: string;
  /** URL 元数据 */
  url: UrlMetadata;
  /** 任务状态 */
  status: TaskStatus;
  /** 创建时间 */
  createdAt: Date;
  /** 开始时间 */
  startedAt?: Date;
  /** 完成时间 */
  completedAt?: Date;
  /** 错误信息 */
  error?: ErrorInfo;
  /** 任务优先级 */
  priority: number;
  /** 重试次数 */
  retryCount: number;
  /** 最大重试次数 */
  maxRetries: number;
  /** 任务标签 */
  tags?: string[];
  /** 任务上下文数据 */
  context?: Record<string, any>;
}

/**
 * 下载任务接口
 */
export interface FetchTask extends CrawlTask {
  /** 指定的下载器类型 */
  fetcherType?: FetcherType;
  /** 下载器特定选项 */
  fetcherOptions?: Record<string, any>;
  /** 代理配置 */
  proxyConfig?: {
    enabled: boolean;
    proxyId?: string;
  };
}

/**
 * 解析任务接口
 */
export interface ParseTask {
  /** 任务唯一标识符 */
  id: string;
  /** 原始下载任务ID */
  fetchTaskId: string;
  /** URL */
  url: string;
  /** 页面内容 */
  content: string;
  /** 内容类型 */
  contentType: string;
  /** HTTP 响应头 */
  headers: Record<string, string>;
  /** HTTP 状态码 */
  statusCode: number;
  /** 下载器类型 */
  fetcherType: FetcherType;
  /** 响应时间 */
  responseTime: number;
  /** 页面深度 */
  depth: number;
  /** 父页面URL */
  parentUrl?: string;
  /** 创建时间 */
  createdAt: Date;
  /** 任务状态 */
  status: TaskStatus;
  /** 错误信息 */
  error?: ErrorInfo;
  /** 任务上下文数据 */
  context?: Record<string, any>;
}

/**
 * 任务结果接口
 */
export interface TaskResult {
  /** 任务ID */
  taskId: string;
  /** 任务类型 */
  taskType: 'fetch' | 'parse';
  /** 是否成功 */
  success: boolean;
  /** 处理时间 (毫秒) */
  processingTime: number;
  /** 结果数据 */
  data?: any;
  /** 错误信息 */
  error?: ErrorInfo;
  /** 统计信息 */
  stats?: {
    /** 提取的数据条数 */
    extractedDataCount?: number;
    /** 发现的新URL数量 */
    discoveredUrlsCount?: number;
    /** 响应大小 (字节) */
    responseSize?: number;
  };
  /** 完成时间 */
  completedAt: Date;
}

/**
 * 任务队列统计信息 (从 shared-types 重新导出)
 */
export type { QueueStats } from '@/shared-types';

/**
 * 任务调度器接口
 */
export interface ITaskScheduler {
  /**
   * 添加下载任务
   * @param task 下载任务
   */
  addFetchTask(task: FetchTask): Promise<void>;

  /**
   * 添加解析任务
   * @param task 解析任务
   */
  addParseTask(task: ParseTask): Promise<void>;

  /**
   * 批量添加下载任务
   * @param tasks 下载任务列表
   */
  addFetchTasks(tasks: FetchTask[]): Promise<void>;

  /**
   * 获取下一个下载任务
   */
  getNextFetchTask(): Promise<FetchTask | null>;

  /**
   * 获取下一个解析任务
   */
  getNextParseTask(): Promise<ParseTask | null>;

  /**
   * 完成任务
   * @param taskId 任务ID
   * @param result 任务结果
   */
  completeTask(taskId: string, result: TaskResult): Promise<void>;

  /**
   * 任务失败
   * @param taskId 任务ID
   * @param error 错误信息
   */
  failTask(taskId: string, error: ErrorInfo): Promise<void>;

  /**
   * 重试任务
   * @param taskId 任务ID
   */
  retryTask(taskId: string): Promise<void>;

  /**
   * 获取队列统计信息
   */
  getQueueStats(): Promise<QueueStats[]>;

  /**
   * 暂停队列
   * @param queueName 队列名称
   */
  pauseQueue(queueName: string): Promise<void>;

  /**
   * 恢复队列
   * @param queueName 队列名称
   */
  resumeQueue(queueName: string): Promise<void>;

  /**
   * 清空队列
   * @param queueName 队列名称
   */
  clearQueue(queueName: string): Promise<void>;

  /**
   * 启动调度器
   */
  start(): Promise<void>;

  /**
   * 停止调度器
   */
  stop(): Promise<void>;
}

/**
 * 任务监听器接口
 */
export interface ITaskListener {
  /**
   * 任务开始事件
   * @param task 任务
   */
  onTaskStarted?(task: CrawlTask): void;

  /**
   * 任务完成事件
   * @param task 任务
   * @param result 任务结果
   */
  onTaskCompleted?(task: CrawlTask, result: TaskResult): void;

  /**
   * 任务失败事件
   * @param task 任务
   * @param error 错误信息
   */
  onTaskFailed?(task: CrawlTask, error: ErrorInfo): void;

  /**
   * 任务重试事件
   * @param task 任务
   */
  onTaskRetried?(task: CrawlTask): void;

  /**
   * 队列状态变化事件
   * @param stats 队列统计信息
   */
  onQueueStatsChanged?(stats: QueueStats[]): void;
}

/**
 * 任务过滤器接口
 */
export interface ITaskFilter {
  /**
   * 过滤下载任务
   * @param task 下载任务
   * @returns 是否应该处理该任务
   */
  shouldProcessFetchTask(task: FetchTask): boolean;

  /**
   * 过滤解析任务
   * @param task 解析任务
   * @returns 是否应该处理该任务
   */
  shouldProcessParseTask(task: ParseTask): boolean;

  /**
   * 过滤URL
   * @param url URL
   * @param depth 深度
   * @param parentUrl 父URL
   * @returns 是否应该抓取该URL
   */
  shouldCrawlUrl(url: string, depth: number, parentUrl?: string): boolean;
}
