/**
 * JSON 解析器实现
 */

import {
  IParser,
  ParserInput,
  ParserOutput,
} from '@/interfaces';

/**
 * JSON 解析器选项接口
 */
export interface JsonParserOptions {
  /** 数据提取路径映射 */
  pathMappings: Record<string, string>;
  /** 是否提取嵌套对象 */
  extractNested?: boolean;
  /** 最大嵌套深度 */
  maxDepth?: number;
  /** 是否提取数组元素 */
  extractArrays?: boolean;
  /** 链接提取配置 */
  linkExtraction?: {
    enabled: boolean;
    pathPatterns: string[];
    urlFields: string[];
  };
  /** 数据转换规则 */
  transformRules?: Record<string, (value: any) => any>;
}

/**
 * JSON 解析器实现类
 */
export class JsonParser implements IParser {
  public readonly name = 'json-parser';
  public readonly version = '1.0.0';
  public readonly description = 'JSON content parser with path-based extraction';

  private options: JsonParserOptions;

  constructor(options: JsonParserOptions) {
    this.options = {
      extractNested: true,
      maxDepth: 10,
      extractArrays: true,
      linkExtraction: {
        enabled: true,
        pathPatterns: ['*.url', '*.link', '*.href'],
        urlFields: ['url', 'link', 'href', 'src', 'endpoint'],
      },
      transformRules: {},
      ...options,
    };
  }

  /**
   * 判断此解析器是否适用于该页面
   */
  public canParse(input: ParserInput): boolean {
    // 检查内容类型
    const contentType = input.headers?.['content-type'] || '';
    if (contentType.includes('application/json') || contentType.includes('text/json')) {
      return true;
    }

    // 检查URL路径
    if (input.url.includes('.json') || input.url.includes('/api/') || input.url.includes('/json/')) {
      return true;
    }

    // 尝试解析内容
    try {
      JSON.parse(input.content);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 执行解析
   */
  public async parse(input: ParserInput): Promise<ParserOutput> {
    const startTime = Date.now();
    
    try {
      // 解析JSON内容
      const jsonData = JSON.parse(input.content);
      
      // 提取数据
      const extractedData = this.extractData(jsonData);
      
      // 提取链接
      const newUrls = this.options.linkExtraction?.enabled 
        ? this.extractLinks(jsonData, input.url) 
        : [];
      
      const parseTime = Date.now() - startTime;
      
      return {
        extractedData,
        newUrls,
        metadata: {
          parserName: this.name,
          parseTime,
          extractedFieldsCount: extractedData ? Object.keys(extractedData).length : 0,
          discoveredUrlsCount: newUrls.length,
          // jsonStructure: this.analyzeJsonStructure(jsonData), // 移除不支持的字段
        },
      };
      
    } catch (error) {
      return {
        extractedData: null,
        newUrls: [],
        error: {
          message: error instanceof Error ? error.message : 'JSON parsing failed',
          code: 'JSON_PARSE_ERROR',
          details: error,
        },
      };
    }
  }

  /**
   * 提取数据
   */
  private extractData(jsonData: any): Record<string, any> | null {
    const data: Record<string, any> = {};
    
    // 使用路径映射提取数据
    for (const [field, path] of Object.entries(this.options.pathMappings)) {
      try {
        const value = this.getValueByPath(jsonData, path);
        
        // 应用转换规则
        if (this.options.transformRules?.[field]) {
          data[field] = this.options.transformRules[field](value);
        } else {
          data[field] = value;
        }
      } catch (error) {
        console.warn(`Failed to extract field '${field}' with path '${path}':`, error);
        data[field] = null;
      }
    }

    // 如果没有配置路径映射，尝试智能提取
    if (Object.keys(this.options.pathMappings).length === 0) {
      return this.smartExtract(jsonData);
    }

    return Object.keys(data).length > 0 ? data : null;
  }

  /**
   * 根据路径获取值
   */
  private getValueByPath(obj: any, path: string): any {
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (part === '*') {
        // 通配符处理
        if (Array.isArray(current)) {
          return current.map(item => this.getValueByPath(item, parts.slice(parts.indexOf(part) + 1).join('.')));
        } else if (typeof current === 'object' && current !== null) {
          const results: any[] = [];
          for (const key in current) {
            const result = this.getValueByPath(current[key], parts.slice(parts.indexOf(part) + 1).join('.'));
            if (result !== undefined) {
              results.push(result);
            }
          }
          return results.length > 0 ? results : undefined;
        }
        return undefined;
      }
      
      if (current === null || current === undefined) {
        return undefined;
      }
      
      // 数组索引处理
      if (Array.isArray(current) && /^\d+$/.test(part)) {
        current = current[parseInt(part, 10)];
      } else if (typeof current === 'object') {
        current = current[part];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  /**
   * 智能提取数据
   */
  private smartExtract(jsonData: any, depth = 0): Record<string, any> | null {
    if (depth > (this.options.maxDepth || 10)) {
      return null;
    }

    const data: Record<string, any> = {};

    if (Array.isArray(jsonData)) {
      // 处理数组
      if (this.options.extractArrays) {
        data.items = jsonData.map((item, index) => {
          if (typeof item === 'object' && item !== null) {
            return this.smartExtract(item, depth + 1);
          }
          return item;
        }).filter(item => item !== null);
      }
    } else if (typeof jsonData === 'object' && jsonData !== null) {
      // 处理对象
      for (const [key, value] of Object.entries(jsonData)) {
        if (typeof value === 'object' && value !== null && this.options.extractNested) {
          const nestedData = this.smartExtract(value, depth + 1);
          if (nestedData && Object.keys(nestedData).length > 0) {
            data[key] = nestedData;
          }
        } else {
          data[key] = value;
        }
      }
    }

    return Object.keys(data).length > 0 ? data : null;
  }

  /**
   * 提取链接
   */
  private extractLinks(jsonData: any, baseUrl: string): string[] {
    const links: string[] = [];
    
    if (!this.options.linkExtraction?.enabled) {
      return links;
    }

    const { pathPatterns, urlFields } = this.options.linkExtraction;
    
    // 使用路径模式提取链接
    for (const pattern of pathPatterns) {
      const urls = this.getValueByPath(jsonData, pattern);
      if (urls) {
        if (Array.isArray(urls)) {
          links.push(...urls.filter(url => this.isValidUrl(url)));
        } else if (this.isValidUrl(urls)) {
          links.push(urls);
        }
      }
    }

    // 递归搜索URL字段
    this.searchUrlFields(jsonData, urlFields, links, baseUrl);

    // 去重并转换为绝对URL
    return [...new Set(links)].map(url => this.resolveUrl(url, baseUrl)).filter(Boolean) as string[];
  }

  /**
   * 递归搜索URL字段
   */
  private searchUrlFields(obj: any, urlFields: string[], links: string[], baseUrl: string, depth = 0): void {
    if (depth > 5 || obj === null || typeof obj !== 'object') {
      return;
    }

    if (Array.isArray(obj)) {
      for (const item of obj) {
        this.searchUrlFields(item, urlFields, links, baseUrl, depth + 1);
      }
    } else {
      for (const [key, value] of Object.entries(obj)) {
        if (urlFields.includes(key.toLowerCase()) && typeof value === 'string' && this.isValidUrl(value)) {
          links.push(value);
        } else if (typeof value === 'object') {
          this.searchUrlFields(value, urlFields, links, baseUrl, depth + 1);
        }
      }
    }
  }

  /**
   * 检查是否为有效URL
   */
  private isValidUrl(url: string): boolean {
    if (typeof url !== 'string' || url.length === 0) {
      return false;
    }

    try {
      new URL(url);
      return true;
    } catch {
      // 检查相对URL
      return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');
    }
  }

  /**
   * 解析URL为绝对URL
   */
  private resolveUrl(url: string, baseUrl: string): string | null {
    try {
      return new URL(url, baseUrl).toString();
    } catch {
      return null;
    }
  }

  /**
   * 分析JSON结构
   */
  private analyzeJsonStructure(jsonData: any): {
    type: string;
    depth: number;
    arrayCount: number;
    objectCount: number;
    totalFields: number;
  } {
    let depth = 0;
    let arrayCount = 0;
    let objectCount = 0;
    let totalFields = 0;

    const analyze = (obj: any, currentDepth: number): void => {
      depth = Math.max(depth, currentDepth);

      if (Array.isArray(obj)) {
        arrayCount++;
        for (const item of obj) {
          analyze(item, currentDepth + 1);
        }
      } else if (typeof obj === 'object' && obj !== null) {
        objectCount++;
        totalFields += Object.keys(obj).length;
        for (const value of Object.values(obj)) {
          analyze(value, currentDepth + 1);
        }
      }
    };

    analyze(jsonData, 0);

    return {
      type: Array.isArray(jsonData) ? 'array' : typeof jsonData,
      depth,
      arrayCount,
      objectCount,
      totalFields,
    };
  }

  /**
   * 创建路径映射建议
   */
  public suggestPathMappings(jsonData: any): Record<string, string> {
    const suggestions: Record<string, string> = {};
    
    const commonFields = [
      'id', 'title', 'name', 'description', 'content', 'text',
      'url', 'link', 'href', 'src', 'image', 'thumbnail',
      'price', 'cost', 'amount', 'value',
      'date', 'time', 'timestamp', 'created', 'updated',
      'author', 'user', 'creator', 'owner',
      'category', 'tag', 'type', 'status',
    ];

    const findPaths = (obj: any, currentPath = ''): void => {
      if (typeof obj === 'object' && obj !== null) {
        if (Array.isArray(obj)) {
          if (obj.length > 0) {
            findPaths(obj[0], `${currentPath}[0]`);
          }
        } else {
          for (const [key, value] of Object.entries(obj)) {
            const newPath = currentPath ? `${currentPath}.${key}` : key;
            
            if (commonFields.includes(key.toLowerCase())) {
              suggestions[key] = newPath;
            }
            
            if (typeof value === 'object' && value !== null) {
              findPaths(value, newPath);
            }
          }
        }
      }
    };

    findPaths(jsonData);
    return suggestions;
  }
}
