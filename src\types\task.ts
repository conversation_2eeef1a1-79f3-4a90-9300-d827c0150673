/**
 * 后端任务相关类型定义
 *
 * 导入并重新导出共享类型，确保前后端一致
 */

// 导入共享类型
export type {
  TaskConfig,
  TaskStats,
  Task,
  TaskLog,
  TaskEvent,
  SystemStats,
  QueueStats,
  HealthCheckResult,
  ProxyConfig,
  ProxyTestResult,
  ApiResponse,
  PaginatedResponse,
  BulkOperationResult,
  WebSocketEvent,
  CreateTaskRequest,
  CreateTaskResponse,
  CreateApiTaskRequest,
  CreateCrawlerTaskRequest,
  TaskListQuery,
  PageResult,
  LinkInfo,
  ApiResult,
  TaskOperationResult,
  CrawlerStats,
} from '@/shared-types';

// 重新导出枚举
export {
  TaskStatus,
  TaskEventType,
  TaskType,
  ProxyStatus,
  FetcherType,
  TraversalStrategy,
  isValidTaskStatus,
  isValidTaskType,
  toTaskStatus,
  toTaskType,
} from '@/shared-types';

// 导入Task接口用于扩展
import { Task } from '@/shared-types';

// 后端特有的类型可以在这里添加
export interface DatabaseTask extends Task {
  _id?: string;
  version?: number;
  metadata?: Record<string, any>;
}

export interface TaskProcessingContext {
  taskId: string;
  userId?: string;
  sessionId: string;
  startTime: Date;
  currentUrl?: string;
  retryCount: number;
}

export interface TaskExecution {
  id: string;
  taskId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: Date;
  completedAt?: Date;
  error?: Error;
  result?: any;
  context: TaskProcessingContext;
}

export interface TaskQueue {
  name: string;
  type: 'memory' | 'redis' | 'bullmq';
  maxConcurrency: number;
  retryLimit: number;
  retryDelay: number;
  priority: number;
}

export interface TaskSchedule {
  taskId: string;
  cron?: string;
  interval?: number;
  nextRun?: Date;
  lastRun?: Date;
  enabled: boolean;
}

export interface TaskMetrics {
  taskId: string;
  executionCount: number;
  successCount: number;
  failureCount: number;
  averageExecutionTime: number;
  lastExecutionTime: number;
  totalDataExtracted: number;
  totalUrlsProcessed: number;
}
