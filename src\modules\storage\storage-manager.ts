/**
 * 存储管理器实现
 */

import {
  IStorageManager,
  IStorage,
  StorageItem,
  QueryOptions,
  QueryResult,
  QueryCondition,
  BulkOperationResult,
  StorageType,
} from '@/interfaces';
import { StorageFactory, StorageConfig } from './storage-factory';
import { getLogger, info, error } from '@/utils';

/**
 * 存储管理器实现类
 */
export class StorageManager implements IStorageManager {
  private primaryStorage: IStorage | null = null;
  private logger = getLogger('StorageManager');
  private storages: Map<string, IStorage> = new Map();
  private storageFactory: StorageFactory;
  private isInitialized = false;
  private stats = {
    totalStored: 0,
    totalStorageTime: 0,
    lastStored: new Date(),
    errorCount: 0,
    cacheHits: 0,
  };

  constructor() {
    this.storageFactory = StorageFactory.getInstance();
  }

  /**
   * 注册存储
   */
  public registerStorage(storage: IStorage): void {
    this.storages.set(storage.name, storage);
    
    // 如果还没有主存储，将第一个注册的存储设为主存储
    if (!this.primaryStorage) {
      this.primaryStorage = storage;
    }
    
    info(`Registered storage: ${storage.name} (${storage.type})`);
  }

  /**
   * 获取主存储
   */
  public getPrimaryStorage(): IStorage {
    if (!this.primaryStorage) {
      throw new Error('No primary storage registered');
    }
    return this.primaryStorage;
  }

  /**
   * 保存数据
   */
  public async save(item: StorageItem): Promise<string> {
    const storage = this.getPrimaryStorage();

    try {
      // 保存到主存储
      const id = await storage.save(item);

      this.stats.totalStored++;
      this.stats.lastStored = new Date();

      return id;
    } catch (error) {
      this.stats.errorCount++;
      this.logger.error('保存数据项失败', {
        error: error instanceof Error ? error.message : error,
        itemId: item.id,
        itemType: typeof item.data,
      });
      throw error;
    }
  }

  /**
   * 批量保存数据
   */
  public async saveBatch(items: StorageItem[]): Promise<BulkOperationResult> {
    const storage = this.getPrimaryStorage();

    try {
      // 批量保存到主存储
      const result = await storage.saveBatch(items);

      this.stats.totalStored += result.successCount;
      this.stats.lastStored = new Date();

      return result;
    } catch (error) {
      this.stats.errorCount++;
      this.logger.error('批量保存数据失败', {
        error: error instanceof Error ? error.message : error,
        itemCount: items.length,
      });
      throw error;
    }
  }

  /**
   * 查询数据
   */
  public async query(options: QueryOptions): Promise<QueryResult> {
    const storage = this.getPrimaryStorage();
    return await storage.query(options);
  }

  /**
   * 获取失败的项目
   */
  public async getFailedItems(taskId?: string): Promise<StorageItem[]> {
    try {
      const conditions: QueryCondition[] = [
        { field: 'status', operator: 'eq', value: 'failed' }
      ];

      if (taskId) {
        conditions.push({ field: 'taskId', operator: 'eq', value: taskId });
      }

      const queryOptions: QueryOptions = {
        conditions
      };

      const result = await this.query(queryOptions);
      
      this.logger.info('获取失败项目', {
        taskId,
        failedCount: result.data.length
      });

      return result.data;
    } catch (error) {
      this.logger.error('获取失败项目失败', {
        taskId,
        error: error instanceof Error ? error.message : error
      });
      return [];
    }
  }

  /**
   * 获取指定状态的项目
   */
  public async getItemsByStatus(status: 'success' | 'failed' | 'pending', taskId?: string): Promise<StorageItem[]> {
    try {
      const conditions: QueryCondition[] = [
        { field: 'status', operator: 'eq', value: status }
      ];

      if (taskId) {
        conditions.push({ field: 'taskId', operator: 'eq', value: taskId });
      }

      const queryOptions: QueryOptions = {
        conditions
      };

      const result = await this.query(queryOptions);
      
      this.logger.info('获取指定状态项目', {
        status,
        taskId,
        count: result.data.length
      });

      return result.data;
    } catch (error) {
      this.logger.error('获取指定状态项目失败', {
        status,
        taskId,
        error: error instanceof Error ? error.message : error
      });
      return [];
    }
  }

  /**
   * 清空指定任务的数据
   */
  public async clearTaskData(taskId: string): Promise<number> {
    try {
      const storage = this.getPrimaryStorage();
      
      // 先查询要删除的项目数量
      const queryResult = await this.query({
        conditions: [{ field: 'taskId', operator: 'eq', value: taskId }]
      });

      const deleteCount = queryResult.data.length;

      // 删除项目
      for (const item of queryResult.data) {
        if (item.id) {
          await storage.delete(item.id);
        }
      }

      this.logger.info('清空任务数据完成', {
        taskId,
        deletedCount: deleteCount
      });

      return deleteCount;
    } catch (error) {
      this.logger.error('清空任务数据失败', {
        taskId,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }



  /**
   * 初始化所有存储
   */
  public async initialize(): Promise<void> {
    const initPromises: Promise<void>[] = [];

    // 初始化所有存储
    for (const storage of this.storages.values()) {
      initPromises.push(
        storage.initialize().catch(error => {
          error(`Failed to initialize storage ${storage.name}:`, error);
          throw error;
        })
      );
    }
    
    await Promise.all(initPromises);
    info('Storage manager initialized');
  }

  /**
   * 关闭所有存储连接
   */
  public async close(): Promise<void> {
    const closePromises: Promise<void>[] = [];

    // 关闭所有存储
    for (const storage of this.storages.values()) {
      closePromises.push(
        storage.close().catch(error => {
          error(`Failed to close storage ${storage.name}:`, error);
        })
      );
    }

    await Promise.all(closePromises);
    info('Storage manager closed');
  }

  /**
   * 获取存储统计信息
   */
  public async getStats(): Promise<Record<string, any>> {
    const stats: Record<string, any> = {};
    
    // 获取所有存储的统计信息
    for (const [name, storage] of this.storages) {
      try {
        stats[name] = await storage.getStats();
      } catch (err) {
        error(`Failed to get stats for storage ${name}:`, err);
        stats[name] = { error: err instanceof Error ? err.message : 'Unknown error' };
      }
    }
    
    // 添加管理器自身的统计信息
    stats.manager = {
      totalStored: this.stats.totalStored,
      totalStorageTime: this.stats.totalStorageTime,
      lastStored: this.stats.lastStored,
      errorCount: this.stats.errorCount,
      cacheHits: this.stats.cacheHits,
    };

    return stats;
  }

  /**
   * 设置主存储
   */
  public setPrimaryStorage(storageName: string): void {
    const storage = this.storages.get(storageName);
    if (!storage) {
      throw new Error(`Storage '${storageName}' not found`);
    }
    
    this.primaryStorage = storage;
    info(`Set primary storage to: ${storageName}`);
  }

  /**
   * 获取存储实例
   */
  public getStorage(name: string): IStorage | null {
    return this.storages.get(name) || null;
  }

  /**
   * 获取所有已注册的存储
   */
  public getAllStorages(): Map<string, IStorage> {
    return new Map(this.storages);
  }

  /**
   * 检查存储健康状态
   */
  public async checkStorageHealth(): Promise<Record<string, boolean>> {
    const healthStatus: Record<string, boolean> = {};
    
    for (const [name, storage] of this.storages) {
      try {
        healthStatus[name] = await storage.healthCheck();
      } catch (err) {
        error(`Health check failed for storage ${name}:`, err);
        healthStatus[name] = false;
      }
    }
    
    return healthStatus;
  }



  /**
   * 清空所有存储
   */
  public async clearAll(): Promise<void> {
    const clearPromises: Promise<void>[] = [];
    
    // 清空所有存储
    for (const storage of this.storages.values()) {
      clearPromises.push(
        storage.clear().catch(error => {
          error(`Failed to clear storage ${storage.name}:`, error);
        })
      );
    }
    
    await Promise.all(clearPromises);
    info('All storages cleared');
  }

  /**
   * 获取存储数量
   */
  public getStorageCount(): number {
    return this.storages.size;
  }

  /**
   * 从工厂创建并注册存储
   */
  public createAndRegisterStorage(config: StorageConfig): void {
    const storage = this.storageFactory.createStorage(config);
    this.registerStorage(storage);
  }

  /**
   * 从模板创建存储
   */
  public createStorageFromTemplate(templateName: string, customConfig?: any): void {
    const storage = this.storageFactory.createStorageFromTemplate(templateName, customConfig);
    this.registerStorage(storage);
  }

  /**
   * 获取存储模板
   */
  public getStorageTemplates(): any[] {
    return this.storageFactory.getStorageTemplates();
  }

  /**
   * 推荐存储配置
   */
  public recommendStorage(requirements: {
    dataVolume: 'small' | 'medium' | 'large';
    queryComplexity: 'simple' | 'complex';
    consistency: 'eventual' | 'strong';
    scalability: 'single' | 'distributed';
  }): any[] {
    return this.storageFactory.recommendStorage(requirements);
  }

  /**
   * 获取存储管理器统计信息
   */
  public getManagerStats(): {
    totalStored: number;
    averageStorageTime: number;
    errorCount: number;
    cacheHits: number;
    storageCount: number;
    lastStored: Date;
  } {
    return {
      totalStored: this.stats.totalStored,
      averageStorageTime: this.stats.totalStored > 0
        ? this.stats.totalStorageTime / this.stats.totalStored
        : 0,
      errorCount: this.stats.errorCount,
      cacheHits: this.stats.cacheHits,
      storageCount: this.storages.size,
      lastStored: this.stats.lastStored,
    };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalStored: 0,
      totalStorageTime: 0,
      lastStored: new Date(),
      errorCount: 0,
      cacheHits: 0,
    };
    info('Storage manager stats reset');
  }

  /**
   * 初始化默认存储
   */
  public async initializeDefaults(config?: {
    storage?: {
      type: StorageType;
      config?: any;
    };
  }): Promise<void> {
    try {
      // 初始化默认存储
      if (config?.storage) {
        this.createAndRegisterStorage({
          type: config.storage.type as any,
          config: config.storage.config || {},
          enabled: true,
          priority: 80,
        });
      } else {
        // 使用简单的内存存储作为默认
        info('No storage config provided, using default template');
      }

      // 初始化存储
      if (this.primaryStorage) {
        await this.primaryStorage.initialize();
      }

      this.isInitialized = true;
      info('Storage manager initialized with defaults');

    } catch (err) {
      error('Failed to initialize storage manager defaults:', err);
      throw err;
    }
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<{
    storage: Record<string, boolean>;
    overall: boolean;
  }> {
    const storageHealth: Record<string, boolean> = {};
    let overallHealthy = true;

    // 检查所有存储的健康状态
    for (const [name, storage] of this.storages) {
      try {
        const isHealthy = await storage.healthCheck();
        storageHealth[name] = isHealthy;
        if (!isHealthy) {
          overallHealthy = false;
        }
      } catch {
        storageHealth[name] = false;
        overallHealthy = false;
      }
    }

    return {
      storage: storageHealth,
      overall: overallHealthy,
    };
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    try {
      // 关闭所有存储连接
      const storageClosePromises = Array.from(this.storages.values()).map(storage =>
        storage.close().catch(error)
      );
      await Promise.all(storageClosePromises);

      // 清理工厂缓存
      this.storageFactory.clearCache();

      this.isInitialized = false;
      info('Storage manager cleaned up');

    } catch (err) {
      error('Error during storage manager cleanup:', err);
    }
  }

  /**
   * 获取工厂缓存统计
   */
  public getFactoryCacheStats(): any {
    return this.storageFactory.getCacheStats();
  }

  /**
   * 获取任务的抓取结果
   */
  public async getTaskResults(taskId: string): Promise<any[]> {
    if (!this.primaryStorage) {
      throw new Error('No primary storage registered');
    }

    try {
      // 先获取所有数据，调试查看数据结构
      const allDataResult = await this.primaryStorage.query({
        limit: 100
      });
      
      console.log('=== 调试信息 ===');
      console.log(`查询任务ID: ${taskId}`);
      console.log(`存储中总数据量: ${allDataResult.total}`);
      console.log('前3条数据样例:');
      allDataResult.data.slice(0, 3).forEach((item: any, index: number) => {
        console.log(`${index + 1}. ID: ${item.id}, URL: ${item.url}`);
        console.log(`   data字段:`, item.data);
        console.log(`   完整对象键值:`, Object.keys(item));
      });

      const queryOptions: QueryOptions = {
        conditions: [
          {
            field: 'taskId',
            operator: 'eq',
            value: taskId
          }
        ],
        sort: [
          {
            field: 'crawledAt',
            direction: 'desc'
          }
        ],
        limit: 10000 // 限制最大返回数量
      };

      const result = await this.primaryStorage.query(queryOptions);
      console.log(`按taskId查询结果数量: ${result.total}`);
      
      // 尝试其他查询条件
      const altQueryOptions: QueryOptions = {
        conditions: [
          {
            field: 'data.taskId',
            operator: 'eq',
            value: taskId
          }
        ],
        limit: 10000
      };
      
      const altResult = await this.primaryStorage.query(altQueryOptions);
      console.log(`按data.taskId查询结果数量: ${altResult.total}`);
      
      // 将 StorageItem 转换为任务结果格式
      const taskResults = result.data.map((item: any) => {
        return {
          ...item,
          id: item.id || '',
          url: item.url || '',
          title: item.title || '',
          content: item.content || '',
          extractedData: item.data || null,
          timestamp: item.crawledAt ? new Date(item.crawledAt).toISOString() : new Date().toISOString(),
          metadata: {
            responseTime: item.metadata?.responseTime || 0,
            statusCode: item.metadata?.statusCode || 0,
            contentType: item.contentType || 'text/html'
          }
        };
      });

      return taskResults;
    } catch (err) {
      this.logger.error(`获取任务结果失败: ${taskId}`, err);
      return [];
    }
  }

  /**
   * 验证配置
   */
  public validateStorageConfig(config: StorageConfig): boolean {
    return this.storageFactory.validateStorageConfig(config);
  }


}
