/**
 * 前端任务相关类型定义
 * 
 * 导入并重新导出共享类型，确保前后端一致
 */

// 先导入枚举和类型
import { 
  TaskStatus, 
  TaskEventType, 
  TaskType,
  ProxyStatus,
  FetcherType,
  TraversalStrategy,
  isValidTaskStatus,
  isValidTaskType,
  toTaskStatus,
  toTaskType,
  Task
} from '@/shared-types';

// 导入共享类型 - 使用路径别名
export type {
  TaskConfig,
  TaskStats,
  Task,
  TaskLog,
  TaskEvent,
  SystemStats,
  QueueStats,
  HealthCheckResult,
  ProxyConfig,
  ProxyTestResult,
  ApiResponse,
  PaginatedResponse,
  BulkOperationResult,
  WebSocketEvent,
  CreateTaskRequest,
  CreateTaskResponse,
  CreateApiTaskRequest,
  CreateCrawlerTaskRequest,
  TaskListQuery,
  PageResult,
  LinkInfo,
  ApiResult,
  TaskOperationResult,
  CrawlerStats
} from '@/shared-types';

// 重新导出枚举
export { 
  TaskStatus, 
  TaskEventType, 
  TaskType,
  ProxyStatus,
  FetcherType,
  TraversalStrategy,
  isValidTaskStatus,
  isValidTaskType,
  toTaskStatus,
  toTaskType
};

// 前端特有的类型可以在这里添加
export interface UIState {
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface TableColumn<T> {
  key: keyof T;
  title: string;
  width?: string;
  sortable?: boolean;
  render?: (value: any, record: T) => React.ReactNode;
}

export interface FilterOption {
  label: string;
  value: string | number;
  count?: number;
}

// 前端表单相关类型
export interface CreateTaskFormData {
  name: string;
  description: string;
  type: TaskType;
  urls: string;
  maxDepth: number;
  maxPages: number;
  concurrency: number;
  delay: number;
  timeout: number;
  retries: number;
  selectors: Record<string, string>;
  headers: Record<string, string>;
  cookies: string;
  proxyEnabled: boolean;
  proxyHost: string;
  proxyPort: number;
  proxyUsername: string;
  proxyPassword: string;
  storageType: 'memory' | 'mongodb' | 'file';
  scheduleEnabled: boolean;
  scheduleCron: string;
  scheduleInterval: number;
  notificationsEnabled: boolean;
  notificationEmail: string;
  notificationWebhook: string;
}

// UI组件状态类型
export interface TaskListState {
  tasks: Task[];
  filteredTasks: Task[];
  selectedTasks: string[];
  sortField: string;
  sortDirection: 'asc' | 'desc';
  filters: {
    status: TaskStatus[];
    type: TaskType[];
    dateRange: [Date | null, Date | null];
  };
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
  ui: UIState;
}

// 实时更新相关类型
export interface TaskUpdate {
  taskId: string;
  field: keyof Task;
  value: any;
  timestamp: string;
}

export interface NotificationMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  autoClose?: boolean;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}