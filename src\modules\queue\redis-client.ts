/**
 * Redis 客户端封装
 */

import Redis from 'ioredis';
import { RedisConfig, HealthCheckResult } from '@/interfaces';
import { error, info } from '@/utils';

/**
 * Redis 客户端管理器
 */
export class RedisClientManager {
  private client: Redis | null = null;
  private config: RedisConfig;
  private isConnected = false;

  constructor(config: RedisConfig) {
    this.config = config;
  }

  /**
   * 连接到 Redis
   */
  public async connect(): Promise<void> {
    if (this.client && this.isConnected) {
      return;
    }

    this.client = new Redis({
      host: this.config.host,
      port: this.config.port,
      password: this.config.password,
      db: this.config.db || 0,
      maxRetriesPerRequest: 3,
      // retryDelayOnFailover: 100, // 移除不支持的配置
      connectTimeout: this.config.connectTimeoutMs || 10000,
      commandTimeout: this.config.commandTimeoutMs || 5000,
      lazyConnect: true,
      // maxLoadingTimeout: 5000, // 移除不支持的配置
    });

    // 设置事件监听器
    this.client.on('connect', () => {
      info('Redis connected');
      this.isConnected = true;
    });

    this.client.on('error', (err) => {
      error('Redis connection error:', err);
      this.isConnected = false;
    });

    this.client.on('close', () => {
      info('Redis connection closed');
      this.isConnected = false;
    });

    this.client.on('reconnecting', () => {
      info('Redis reconnecting...');
    });

    try {
      await this.client.connect();
    } catch (err) {
      error('Failed to connect to Redis:', err);
      throw error;
    }
  }

  /**
   * 断开 Redis 连接
   */
  public async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.quit();
      this.client = null;
      this.isConnected = false;
    }
  }

  /**
   * 获取 Redis 客户端实例
   */
  public getClient(): Redis {
    if (!this.client || !this.isConnected) {
      throw new Error('Redis client is not connected');
    }
    return this.client;
  }

  /**
   * 检查连接状态
   */
  public isClientConnected(): boolean {
    return this.isConnected && this.client !== null;
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<HealthCheckResult> {
    try {
      if (!this.client) {
        return {
          status: 'unhealthy',
          timestamp: new Date(),
          services: {
            redis: {
              status: 'unhealthy',
              message: 'Redis client is not initialized',
              responseTime: 0,
            },
          },
        };
      }

      const start = Date.now();
      await this.client.ping();
      const responseTime = Date.now() - start;

      return {
        status: 'healthy',
        timestamp: new Date(),
        services: {
          redis: {
            status: 'healthy',
            message: 'Redis connection successful',
            responseTime,
          },
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date(),
        services: {
          redis: {
            status: 'unhealthy',
            message: error instanceof Error ? error.message : 'Unknown error',
            responseTime: 0,
          },
        },
      };
    }
  }

  /**
   * 获取 Redis 信息
   */
  public async getInfo(): Promise<Record<string, any>> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }

    const info = await this.client.info();
    const lines = info.split('\r\n');
    const result: Record<string, any> = {};

    for (const line of lines) {
      if (line && !line.startsWith('#')) {
        const [key, value] = line.split(':');
        if (key && value) {
          result[key] = value;
        }
      }
    }

    return result;
  }

  /**
   * 获取内存使用情况
   */
  public async getMemoryUsage(): Promise<{
    used: number;
    peak: number;
    total: number;
  }> {
    const info = await this.getInfo();
    return {
      used: parseInt(info.used_memory || '0', 10),
      peak: parseInt(info.used_memory_peak || '0', 10),
      total: parseInt(info.total_system_memory || '0', 10),
    };
  }

  /**
   * 获取连接数
   */
  public async getConnectionCount(): Promise<number> {
    const info = await this.getInfo();
    return parseInt(info.connected_clients || '0', 10);
  }

  /**
   * 清空数据库
   */
  public async flushDb(): Promise<void> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }
    await this.client.flushdb();
  }

  /**
   * 获取数据库大小
   */
  public async getDbSize(): Promise<number> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }
    return await this.client.dbsize();
  }

  /**
   * 设置键值对
   */
  public async set(key: string, value: string, ttl?: number): Promise<void> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }

    if (ttl) {
      await this.client.setex(key, ttl, value);
    } else {
      await this.client.set(key, value);
    }
  }

  /**
   * 获取值
   */
  public async get(key: string): Promise<string | null> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }
    return await this.client.get(key);
  }

  /**
   * 删除键
   */
  public async del(key: string): Promise<number> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }
    return await this.client.del(key);
  }

  /**
   * 检查键是否存在
   */
  public async exists(key: string): Promise<boolean> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }
    const result = await this.client.exists(key);
    return result === 1;
  }

  /**
   * 获取所有匹配的键
   */
  public async keys(pattern: string): Promise<string[]> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }
    return await this.client.keys(pattern);
  }

  /**
   * 设置过期时间
   */
  public async expire(key: string, seconds: number): Promise<boolean> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }
    const result = await this.client.expire(key, seconds);
    return result === 1;
  }

  /**
   * 获取剩余过期时间
   */
  public async ttl(key: string): Promise<number> {
    if (!this.client) {
      throw new Error('Redis client is not connected');
    }
    return await this.client.ttl(key);
  }
}
