import { Link, useLocation } from 'react-router-dom';
import { Activity, Wifi, WifiOff, Clock } from 'lucide-react';
import { StatusBadge } from '@/components/ui/Badge';
import { useSocket } from '@/hooks/useSocket';

interface NavigationProps {
  lastUpdate?: Date | null;
}

export function Navigation({ lastUpdate }: NavigationProps) {
  const { isConnected, connectionError } = useSocket();
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const getLinkClassName = (path: string) => {
    return `${
      isActive(path)
        ? 'border-blue-500 text-gray-900'
        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`;
  };

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo 和标题 */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg">
              <Activity className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                Nodex 监控面板
              </h1>
              <p className="text-sm text-gray-500">
                高性能智能爬虫系统监控
              </p>
            </div>
          </div>

          {/* 导航链接 */}
          <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
            <Link
              to="/"
              className={getLinkClassName('/')}
            >
              系统监控
            </Link>
            <Link
              to="/crawler"
              className={getLinkClassName('/crawler')}
            >
              爬虫管理
            </Link>
            <Link
              to="/fetch"
              className={getLinkClassName('/fetch')}
            >
              API抓取
            </Link>
            <Link
              to="/proxy"
              className={getLinkClassName('/proxy')}
            >
              代理管理
            </Link>
          </div>

          {/* 状态信息 */}
          <div className="flex items-center space-x-6">
            {/* 连接状态 */}
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <>
                  <Wifi className="h-4 w-4 text-green-500" />
                  <StatusBadge status="healthy" size="sm">
                    已连接
                  </StatusBadge>
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 text-red-500" />
                  <StatusBadge status="critical" size="sm">
                    连接断开
                  </StatusBadge>
                </>
              )}
            </div>

            {/* 最后更新时间 */}
            {lastUpdate && (
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <Clock className="h-4 w-4" />
                <span>
                  最后更新: {lastUpdate.toLocaleTimeString()}
                </span>
              </div>
            )}

            {/* 连接错误提示 */}
            {connectionError && (
              <div className="text-sm text-red-600 max-w-xs truncate" title={connectionError}>
                错误: {connectionError}
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}