/**
 * 代理管理路由
 *
 * 提供代理的增删改查和管理功能API端点
 */

import { Router } from 'express';
import multer from 'multer';
import { ProxyConfig } from '@/types/common';
import { error } from '@/utils';
import { TaskManager } from '@/app/task-manager';
import { ProxyStatus } from '@/shared-types';

// 设置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // 只允许JSON和文本文件
    if (
      file.mimetype === 'application/json' ||
      file.mimetype === 'text/plain' ||
      file.originalname.endsWith('.json') ||
      file.originalname.endsWith('.txt')
    ) {
      cb(null, true);
    } else {
      cb(new Error('只支持JSON和文本文件'));
    }
  },
});

/**
 * 创建代理管理路由 - 集成真实的ProxyManager
 */
export function createProxyRoutes(taskManager: TaskManager): Router {
  const router = Router();

  // 获取ProxyManager实例
  const getProxyManager = () => {
    const dependencies = taskManager.getDependencies();
    return dependencies.fetcherManager.getProxyManager();
  };

  /**
   * GET /proxy - 获取所有代理列表
   */
  router.get('/', async (_req, res) => {
    try {
      const proxyManager = getProxyManager();
      if (!proxyManager) {
        return res.json({
          success: true,
          data: {
            proxies: [],
            stats: {
              total: 0,
              available: 0,
              healthy: 0,
              failed: 0,
            },
          },
        });
      }

      const proxyStats = proxyManager.getProxyStats();
      const totalProxies = proxyManager.getAllProxies().length;
      const healthyProxies = proxyManager.getHealthyProxyCount();
      const failedProxies = proxyManager.getFailedProxyCount();
      const availableProxies = proxyManager.getAvailableProxyCount();

      const stats = {
        total: totalProxies,
        available: availableProxies,
        healthy: healthyProxies,
        failed: failedProxies,
      };

      res.json({
        success: true,
        data: {
          proxies: proxyStats,
          stats,
        },
      });
    } catch (err) {
      error('获取代理列表失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get proxy list',
      });
    }
  });

  /**
   * POST /proxy - 添加单个代理
   */
  router.post('/', async (req, res) => {
    try {
      const proxyConfig: ProxyConfig = req.body;

      // 验证代理配置
      if (!proxyConfig.host || !proxyConfig.port) {
        return res.status(400).json({
          success: false,
          error: 'Host and port are required',
        });
      }

      const proxyManager = getProxyManager();
      if (!proxyManager) {
        return res.status(500).json({
          success: false,
          error: 'Proxy manager not available',
        });
      }

      proxyManager.addProxy(proxyConfig);

      res.json({
        success: true,
        message: 'Proxy added successfully',
        data: { proxy: proxyConfig },
      });
    } catch (err) {
      error('添加代理失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to add proxy',
      });
    }
  });

  /**
   * POST /proxy/batch - 批量添加代理
   */
  router.post('/batch', async (req, res) => {
    try {
      const { proxies } = req.body;

      if (!Array.isArray(proxies)) {
        return res.status(400).json({
          success: false,
          error: 'Proxies must be an array',
        });
      }

      // 验证每个代理配置
      for (const proxy of proxies) {
        if (!proxy.host || !proxy.port) {
          return res.status(400).json({
            success: false,
            error: 'Each proxy must have host and port',
          });
        }
      }

      const proxyManager = getProxyManager();
      if (!proxyManager) {
        return res.status(500).json({
          success: false,
          error: 'Proxy manager not available',
        });
      }

      proxyManager.addProxies(proxies);

      res.json({
        success: true,
        message: `Successfully added ${proxies.length} proxies`,
        data: { count: proxies.length },
      });
    } catch (err) {
      error('批量添加代理失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to add proxies',
      });
    }
  });

  /**
   * PUT /proxy - 更新代理配置
   */
  router.put('/', async (req, res) => {
    try {
      const { oldProxy, newProxy } = req.body;

      // 验证代理配置
      if (!oldProxy.host || !oldProxy.port) {
        return res.status(400).json({
          success: false,
          error: 'Old proxy host and port are required',
        });
      }

      if (!newProxy.host || !newProxy.port) {
        return res.status(400).json({
          success: false,
          error: 'New proxy host and port are required',
        });
      }

      const proxyManager = getProxyManager();
      if (!proxyManager) {
        return res.status(500).json({
          success: false,
          error: 'Proxy manager not available',
        });
      }

      // 先删除旧的代理
      proxyManager.removeProxy(oldProxy);

      // 然后添加新的代理
      proxyManager.addProxy(newProxy);

      res.json({
        success: true,
        message: 'Proxy updated successfully',
        data: { oldProxy, newProxy },
      });
    } catch (err) {
      error('更新代理失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to update proxy',
      });
    }
  });

  /**
   * DELETE /proxy - 删除代理
   */
  router.delete('/', async (req, res) => {
    try {
      const proxyConfig: ProxyConfig = req.body;

      if (!proxyConfig.host || !proxyConfig.port) {
        return res.status(400).json({
          success: false,
          error: 'Host and port are required',
        });
      }

      const proxyManager = getProxyManager();
      if (!proxyManager) {
        return res.status(500).json({
          success: false,
          error: 'Proxy manager not available',
        });
      }

      proxyManager.removeProxy(proxyConfig);

      res.json({
        success: true,
        message: 'Proxy removed successfully',
        data: { proxy: proxyConfig },
      });
    } catch (err) {
      error('删除代理失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to remove proxy',
      });
    }
  });

  /**
   * DELETE /proxy/all - 清空所有代理
   */
  router.delete('/all', async (_req, res) => {
    try {
      const proxyManager = getProxyManager();
      if (!proxyManager) {
        return res.status(500).json({
          success: false,
          error: 'Proxy manager not available',
        });
      }

      proxyManager.clearAllProxies();

      res.json({
        success: true,
        message: 'All proxies cleared successfully',
      });
    } catch (err) {
      error('清空代理失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to clear proxies',
      });
    }
  });

  /**
   * POST /proxy/test - 测试所有代理
   */
  router.post('/test', async (_req, res) => {
    try {
      const proxyManager = getProxyManager();
      if (!proxyManager) {
        return res.status(500).json({
          success: false,
          error: 'Proxy manager not available',
        });
      }

      const testResults = await proxyManager.testAllProxies();

      let healthy = 0;
      let failed = 0;
      const results = [];

      for (const [proxyKey, isHealthy] of testResults) {
        if (isHealthy) {
          healthy++;
        } else {
          failed++;
        }
        results.push({
          proxy: proxyKey,
          healthy: isHealthy,
        });
      }

      res.json({
        success: true,
        message: 'Proxy test completed',
        data: {
          results,
          summary: {
            total: testResults.size,
            healthy,
            failed,
          },
        },
      });
    } catch (err) {
      error('测试代理失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to test proxies',
      });
    }
  });

  /**
   * POST /proxy/load - 从formdata文件加载代理
   */
  router.post('/load', upload.single('file'), async (req, res) => {
    try {
      const file = req.file;
      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded',
        });
      }

      const proxyManager = getProxyManager();
      if (!proxyManager) {
        return res.status(500).json({
          success: false,
          error: 'Proxy manager not available',
        });
      }

      let proxies: any[];
      try {
        const fileContent = file.buffer.toString('utf-8');
        proxies = JSON.parse(fileContent);
      } catch (parseError) {
        return res.status(400).json({
          success: false,
          error: '无法解析文件内容，请确保是有效的JSON格式',
        });
      }

      if (!Array.isArray(proxies)) {
        return res.status(400).json({
          success: false,
          error: '文件内容必须是一个数组',
        });
      }

      // 检测是否为Webshare格式
      const isWebshareFormat = proxies.length > 0 && 'proxy_address' in proxies[0];

      let loadedCount = 0;
      if (isWebshareFormat) {
        // 使用Webshare格式加载
        const validProxies = proxies.filter(
          proxy => proxy.valid && proxy.proxy_address && proxy.port
        );
        const standardProxies = validProxies.map(proxy => ({
          id: proxy.id || `${proxy.proxy_address}:${proxy.port}`,
          name: `${proxy.proxy_address}:${proxy.port}`,
          host: proxy.proxy_address,
          port: proxy.port,
          username: proxy.username,
          password: proxy.password,
          type: 'http' as const,
          protocol: 'http' as const,
          auth: {
            username: proxy.username,
            password: proxy.password,
          },
          status: ProxyStatus.INACTIVE,
          lastChecked: new Date().toISOString(),
          responseTime: 0,
          successRate: 0,
          totalRequests: 0,
          failedRequests: 0,
          createdAt: proxy.created_at || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          // 地域信息字段
          countryCode: proxy.country_code,
          cityName: proxy.city_name,
          asnName: proxy.asn_name,
          asnNumber: proxy.asn_number,
          highCountryConfidence: proxy.high_country_confidence,
        }));
        proxyManager.addProxies(standardProxies as ProxyConfig[]);
        loadedCount = standardProxies.length;
      } else {
        // 使用标准格式加载
        const validProxies = proxies.filter(proxy => proxy.host && proxy.port);
        proxyManager.addProxies(validProxies);
        loadedCount = validProxies.length;
      }

      res.json({
        success: true,
        message: `成功从文件加载了 ${loadedCount} 个代理`,
        data: {
          totalInFile: proxies.length,
          loadedCount,
          format: isWebshareFormat ? 'webshare' : 'standard',
        },
      });
    } catch (err) {
      error('从文件加载代理失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to load proxies from file',
      });
    }
  });

  return router;
}
