/**
 * HTML爬虫配置表单组件
 * 
 * 提供HTML爬虫任务的配置界面
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { useNotification } from '@/contexts/NotificationContext';

interface CrawlerTaskFormProps {
  onSubmit: (config: CrawlerTaskConfig) => void;
  loading?: boolean;
}

interface CrawlerTaskConfig {
  name: string;
  startUrls: string[];
  maxDepth: number;
  maxConcurrency: number;
  delayMs: number;
  allowedDomains?: string[];
  excludePatterns?: string[];
  includePatterns?: string[];
  enableAI: boolean;
  useProxy: boolean;
  useBrowser: boolean;
  respectRobots: boolean;
  userAgent: string;
  useReadability: boolean;
  includeRawHtml: boolean;
  customHeaders?: Record<string, string>;
  cookies?: string;
  randomUserAgent?: boolean;
  randomIP?: boolean;
}

export const CrawlerTaskForm: React.FC<CrawlerTaskFormProps> = ({ onSubmit, loading = false }) => {
  const { showError } = useNotification();
  const [config, setConfig] = useState<CrawlerTaskConfig>({
    name: 'forum.safe',
    startUrls: [''],
    maxDepth: 5,
    maxConcurrency: 5,
    delayMs: 1000,
    allowedDomains: [],
    excludePatterns: [],
    includePatterns: [],
    enableAI: false,
    useProxy: false,
    useBrowser: true,
    respectRobots: false,
    userAgent: 'Nodex-Crawler/1.0',
    useReadability: true,
    includeRawHtml: true,
    customHeaders: {},
    cookies: '',
    randomUserAgent: true,
    randomIP: true
  });

  const [urlsText, setUrlsText] = useState('https://forum.safe.global/');
  const [domainsText, setDomainsText] = useState('forum.safe.global\nsafe.global');
  const [excludeText, setExcludeText] = useState('');
  const [includeText, setIncludeText] = useState('');
  const [headersText, setHeadersText] = useState('');
  const [cookiesText, setCookiesText] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 解析URL列表
    const startUrls = urlsText.split('\n')
      .map(url => url.trim())
      .filter(url => url && url.startsWith('http'));

    if (startUrls.length === 0) {
      showError('输入验证错误', '请至少输入一个有效的起始URL');
      return;
    }

    // 解析域名限制
    const allowedDomains = domainsText.split('\n')
      .map(domain => domain.trim())
      .filter(domain => domain);

    // 解析排除模式
    const excludePatterns = excludeText.split('\n')
      .map(pattern => pattern.trim())
      .filter(pattern => pattern);

    // 解析包含模式
    const includePatterns = includeText.split('\n')
      .map(pattern => pattern.trim())
      .filter(pattern => pattern);

    // 解析自定义Headers
    const customHeaders: Record<string, string> = {};
    if (headersText.trim()) {
      headersText.split('\n').forEach(line => {
        const [key, ...valueParts] = line.split(':');
        if (key && valueParts.length > 0) {
          customHeaders[key.trim()] = valueParts.join(':').trim();
        }
      });
    }

    const finalConfig = {
      ...config,
      startUrls,
      allowedDomains: allowedDomains.length > 0 ? allowedDomains : undefined,
      excludePatterns: excludePatterns.length > 0 ? excludePatterns : undefined,
      includePatterns: includePatterns.length > 0 ? includePatterns : undefined,
      customHeaders: Object.keys(customHeaders).length > 0 ? customHeaders : undefined,
      cookies: cookiesText.trim() || undefined
    };

    onSubmit(finalConfig);
  };

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">HTML爬虫配置</h3>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* 任务名称 */}
        <div>
          <label className="block text-sm font-medium mb-2">任务名称 *</label>
          <input
            type="text"
            value={config.name}
            onChange={(e) => setConfig({ ...config, name: e.target.value })}
            placeholder="我的爬虫任务"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        {/* 起始URL */}
        <div>
          <label className="block text-sm font-medium mb-2">起始URL *（每行一个）</label>
          <textarea
            value={urlsText}
            onChange={(e) => setUrlsText(e.target.value)}
            placeholder="https://example.com
https://example.com/page1
https://example.com/page2"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={4}
            required
          />
        </div>

        {/* 爬取配置 */}
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">最大深度</label>
            <input
              type="number"
              value={config.maxDepth}
              onChange={(e) => setConfig({ ...config, maxDepth: parseInt(e.target.value) })}
              min="1"
              max="10"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">并发数</label>
            <input
              type="number"
              value={config.maxConcurrency}
              onChange={(e) => setConfig({ ...config, maxConcurrency: parseInt(e.target.value) })}
              min="1"
              max="20"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">延迟(毫秒)</label>
            <input
              type="number"
              value={config.delayMs}
              onChange={(e) => setConfig({ ...config, delayMs: parseInt(e.target.value) })}
              min="0"
              max="10000"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* 域名限制 */}
        <div>
          <label className="block text-sm font-medium mb-2">允许的域名（每行一个，留空则无限制）</label>
          <textarea
            value={domainsText}
            onChange={(e) => setDomainsText(e.target.value)}
            placeholder="example.com
subdomain.example.com"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
          />
        </div>

        {/* URL模式过滤 */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">排除模式（每行一个正则表达式）</label>
            <textarea
              value={excludeText}
              onChange={(e) => setExcludeText(e.target.value)}
              placeholder=".*\\.pdf$
.*\\.jpg$
/admin/.*"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">包含模式（每行一个正则表达式）</label>
            <textarea
              value={includeText}
              onChange={(e) => setIncludeText(e.target.value)}
              placeholder="/article/.*
/news/.*
/blog/.*"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
            />
          </div>
        </div>

        {/* 内容解析配置 */}
        <div>
          <label className="block text-sm font-medium mb-2">内容解析配置</label>
          <div className="space-y-3">
            <div className="bg-blue-50 p-3 rounded-md">
              <p className="text-sm text-blue-800 mb-2">
                <strong>智能内容解析</strong> - 使用 Mozilla Readability 自动提取文章主要内容
              </p>
              <p className="text-xs text-blue-600">
                自动识别标题、正文、摘要等，无需配置CSS选择器
              </p>
            </div>
          </div>
        </div>

        {/* User Agent */}
        <div>
          <label className="block text-sm font-medium mb-2">User Agent</label>
          <input
            type="text"
            value={config.userAgent}
            onChange={(e) => setConfig({ ...config, userAgent: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={config.randomUserAgent}
          />
          <label className="flex items-center mt-2">
            <input
              type="checkbox"
              checked={config.randomUserAgent}
              onChange={(e) => setConfig({ ...config, randomUserAgent: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">使用随机User-Agent</span>
            <span className="text-xs text-gray-500 ml-2">每次请求使用不同的常见浏览器User-Agent</span>
          </label>
        </div>

        {/* 自定义Headers */}
        <div>
          <label className="block text-sm font-medium mb-2">
            自定义Headers（可选）
            <span className="text-xs text-gray-500 ml-2">每行一个，格式：key:value</span>
          </label>
          <textarea
            value={headersText}
            onChange={(e) => setHeadersText(e.target.value)}
            placeholder="Authorization:Bearer token123
Content-Type:application/json
X-Custom-Header:value"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={4}
          />
        </div>

        {/* Cookies */}
        <div>
          <label className="block text-sm font-medium mb-2">
            Cookies（可选）
            <span className="text-xs text-gray-500 ml-2">浏览器Cookie格式</span>
          </label>
          <textarea
            value={cookiesText}
            onChange={(e) => setCookiesText(e.target.value)}
            placeholder="session=abc123; user_id=456; preferences=dark_mode"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={2}
          />
        </div>

        {/* 选项开关 */}
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.useBrowser}
              onChange={(e) => setConfig({ ...config, useBrowser: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">模拟浏览器 (Puppeteer/Playwright)</span>
            <span className="text-xs text-gray-500 ml-2">使用真实浏览器引擎处理JavaScript</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.useReadability}
              onChange={(e) => setConfig({ ...config, useReadability: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">使用智能内容解析 (推荐)</span>
            <span className="text-xs text-gray-500 ml-2">基于Mozilla Readability算法</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.includeRawHtml}
              onChange={(e) => setConfig({ ...config, includeRawHtml: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">包含原始HTML</span>
            <span className="text-xs text-gray-500 ml-2">便于定制化处理</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.enableAI}
              onChange={(e) => setConfig({ ...config, enableAI: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">启用AI内容解析</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.useProxy}
              onChange={(e) => setConfig({ ...config, useProxy: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">使用代理</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.randomIP}
              onChange={(e) => setConfig({ ...config, randomIP: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">随机IP</span>
            <span className="text-xs text-gray-500 ml-2">自动添加随机X-Forwarded-For头</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.respectRobots}
              onChange={(e) => setConfig({ ...config, respectRobots: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">遵守robots.txt</span>
          </label>
        </div>

        {/* 提交按钮 */}
        <Button
          type="submit"
          disabled={loading || !config.name || !urlsText.trim()}
          className="w-full"
        >
          {loading ? '创建中...' : '创建爬虫任务'}
        </Button>
      </form>
    </Card>
  );
};