import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface ProxyConfig {
  host: string;
  port: number;
  auth?: {
    username: string;
    password: string;
  };
  protocol?: 'http' | 'https' | 'socks4' | 'socks5';
}

interface ProxyHealthMetrics {
  proxy: ProxyConfig;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  responseTime: number;
  successRate: number;
  uptime: number;
  lastChecked: Date;
  healthScore: number;
  metrics: {
    connectivity: {
      score: number;
      latency: number;
      packetLoss: number;
      jitter: number;
    };
    performance: {
      score: number;
      throughput: number;
      concurrency: number;
      errorRate: number;
    };
    reliability: {
      score: number;
      uptime: number;
      mtbf: number; // Mean Time Between Failures
      mttr: number; // Mean Time To Recovery
    };
    security: {
      score: number;
      sslSupport: boolean;
      anonymityLevel: 'transparent' | 'anonymous' | 'elite';
      ipLeaks: boolean;
    };
  };
  recentTests: {
    timestamp: Date;
    success: boolean;
    responseTime: number;
    error?: string;
  }[];
  geolocation?: {
    country: string;
    city: string;
    region: string;
    timezone: string;
  };
}

interface ProxyHealthCheckerProps {
  proxies: ProxyConfig[];
  onHealthUpdate: (health: ProxyHealthMetrics[]) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
  testUrls?: string[];
  loading?: boolean;
}

export const ProxyHealthChecker: React.FC<ProxyHealthCheckerProps> = ({
  proxies,
  onHealthUpdate,
  autoRefresh = true,
  refreshInterval = 60000, // 1 minute
  testUrls = ['https://httpbin.org/ip', 'https://www.google.com', 'https://api.github.com'],
  loading = false,
}) => {
  const [healthMetrics, setHealthMetrics] = useState<ProxyHealthMetrics[]>([]);
  const [testing, setTesting] = useState(false);
  const [testProgress, setTestProgress] = useState(0);
  const [selectedProxy, setSelectedProxy] = useState<ProxyHealthMetrics | null>(null);
  const [sortBy, setSortBy] = useState<'health' | 'performance' | 'reliability'>('health');
  const [filterStatus, setFilterStatus] = useState<'all' | 'healthy' | 'degraded' | 'unhealthy'>('all');

  // 模拟健康检查函数
  const performHealthCheck = async (proxy: ProxyConfig): Promise<ProxyHealthMetrics> => {
    // 模拟网络测试
    const testResults = await Promise.all(
      testUrls.map(async (url) => {
        const testStart = Date.now();
        try {
          // 这里应该是真实的代理测试逻辑
          await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 200));
          
          const responseTime = Date.now() - testStart;
          const success = Math.random() > 0.1; // 90% success rate simulation
          
          return {
            timestamp: new Date(),
            success,
            responseTime,
            error: success ? undefined : 'Connection timeout',
          };
        } catch (error) {
          return {
            timestamp: new Date(),
            success: false,
            responseTime: Date.now() - testStart,
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      })
    );

    const successfulTests = testResults.filter(t => t.success);
    const successRate = (successfulTests.length / testResults.length) * 100;
    const averageResponseTime = testResults.reduce((sum, t) => sum + t.responseTime, 0) / testResults.length;
    
    // 计算各项指标
    const connectivityScore = Math.max(0, 100 - (averageResponseTime / 10)); // 响应时间越低分数越高
    const performanceScore = Math.min(100, successRate * 1.2); // 成功率影响性能分数
    const reliabilityScore = Math.max(0, successRate - (Math.random() * 20)); // 模拟可靠性评分
    const securityScore = 70 + Math.random() * 30; // 模拟安全评分

    const healthScore = (connectivityScore + performanceScore + reliabilityScore + securityScore) / 4;
    
    let status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
    if (healthScore >= 80) status = 'healthy';
    else if (healthScore >= 60) status = 'degraded';
    else if (healthScore >= 40) status = 'unhealthy';
    else status = 'unknown';

    return {
      proxy,
      status,
      responseTime: averageResponseTime,
      successRate,
      uptime: Math.random() * 100, // 模拟运行时间
      lastChecked: new Date(),
      healthScore,
      metrics: {
        connectivity: {
          score: connectivityScore,
          latency: averageResponseTime,
          packetLoss: Math.random() * 5, // 模拟丢包率
          jitter: Math.random() * 10, // 模拟抖动
        },
        performance: {
          score: performanceScore,
          throughput: Math.random() * 100, // 模拟吞吐量
          concurrency: Math.floor(Math.random() * 100) + 10, // 模拟并发数
          errorRate: 100 - successRate,
        },
        reliability: {
          score: reliabilityScore,
          uptime: Math.random() * 100,
          mtbf: Math.random() * 24 * 60, // 分钟
          mttr: Math.random() * 60, // 分钟
        },
        security: {
          score: securityScore,
          sslSupport: Math.random() > 0.3,
          anonymityLevel: ['transparent', 'anonymous', 'elite'][Math.floor(Math.random() * 3)] as 'transparent' | 'anonymous' | 'elite',
          ipLeaks: Math.random() > 0.8,
        },
      },
      recentTests: testResults,
      geolocation: {
        country: ['US', 'UK', 'DE', 'JP', 'SG'][Math.floor(Math.random() * 5)],
        city: ['New York', 'London', 'Berlin', 'Tokyo', 'Singapore'][Math.floor(Math.random() * 5)],
        region: 'Region',
        timezone: 'UTC+0',
      },
    };
  };

  const runHealthCheck = async () => {
    if (proxies.length === 0) return;
    
    setTesting(true);
    setTestProgress(0);
    
    try {
      const results: ProxyHealthMetrics[] = [];
      
      for (let i = 0; i < proxies.length; i++) {
        const proxy = proxies[i];
        const health = await performHealthCheck(proxy);
        results.push(health);
        
        setTestProgress(((i + 1) / proxies.length) * 100);
      }
      
      setHealthMetrics(results);
      onHealthUpdate(results);
    } catch (error) {
      console.error('Health check failed:', error);
    } finally {
      setTesting(false);
      setTestProgress(0);
    }
  };

  // 自动刷新
  useEffect(() => {
    if (autoRefresh && !testing) {
      const interval = setInterval(runHealthCheck, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, testing, proxies]);

  // 初始检查
  useEffect(() => {
    if (proxies.length > 0 && healthMetrics.length === 0) {
      runHealthCheck();
    }
  }, [proxies]);

  const filteredMetrics = healthMetrics.filter(metric => {
    if (filterStatus === 'all') return true;
    return metric.status === filterStatus;
  });

  const sortedMetrics = [...filteredMetrics].sort((a, b) => {
    switch (sortBy) {
      case 'health':
        return b.healthScore - a.healthScore;
      case 'performance':
        return b.metrics.performance.score - a.metrics.performance.score;
      case 'reliability':
        return b.metrics.reliability.score - a.metrics.reliability.score;
      default:
        return b.healthScore - a.healthScore;
    }
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'degraded': return 'text-yellow-600';
      case 'unhealthy': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy': return <Badge variant="success">健康</Badge>;
      case 'degraded': return <Badge variant="warning">降级</Badge>;
      case 'unhealthy': return <Badge variant="danger">不健康</Badge>;
      default: return <Badge variant="default">未知</Badge>;
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${Math.round(minutes)}分钟`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.round(minutes % 60);
    return `${hours}小时${remainingMinutes}分钟`;
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="sm" />
          <span className="ml-2 text-gray-500">初始化健康检查...</span>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">代理健康检查</h3>
          <div className="flex gap-2">
            <Button
              onClick={runHealthCheck}
              variant="primary"
              size="sm"
              disabled={testing || proxies.length === 0}
            >
              {testing ? '检查中...' : '立即检查'}
            </Button>
          </div>
        </div>
        
        {testing && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">检查进度</span>
              <span className="text-sm text-gray-600">{Math.round(testProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${testProgress}%` }}
              />
            </div>
          </div>
        )}

        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex gap-2">
            <span className="text-sm text-gray-500">状态过滤:</span>
            {(['all', 'healthy', 'degraded', 'unhealthy'] as const).map((status) => (
              <Button
                key={status}
                onClick={() => setFilterStatus(status)}
                variant={filterStatus === status ? 'primary' : 'secondary'}
                size="sm"
              >
                {status === 'all' ? '全部' : 
                 status === 'healthy' ? '健康' :
                 status === 'degraded' ? '降级' : '不健康'}
              </Button>
            ))}
          </div>
          
          <div className="flex gap-2">
            <span className="text-sm text-gray-500">排序:</span>
            <Button
              onClick={() => setSortBy('health')}
              variant={sortBy === 'health' ? 'primary' : 'secondary'}
              size="sm"
            >
              健康分数
            </Button>
            <Button
              onClick={() => setSortBy('performance')}
              variant={sortBy === 'performance' ? 'primary' : 'secondary'}
              size="sm"
            >
              性能
            </Button>
            <Button
              onClick={() => setSortBy('reliability')}
              variant={sortBy === 'reliability' ? 'primary' : 'secondary'}
              size="sm"
            >
              可靠性
            </Button>
          </div>
        </div>
      </Card>

      {/* 健康指标列表 */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">健康指标详情</h3>
        
        {sortedMetrics.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">暂无健康检查数据</p>
          </div>
        ) : (
          <div className="space-y-4">
            {sortedMetrics.map((metric) => (
              <div key={`${metric.proxy.host}:${metric.proxy.port}`} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="text-sm font-medium text-gray-900">
                      {metric.proxy.host}:{metric.proxy.port}
                    </div>
                    {getStatusBadge(metric.status)}
                    {metric.geolocation && (
                      <Badge variant="info">{metric.geolocation.country}</Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-gray-500">健康分数:</div>
                    <div className={`text-lg font-bold ${getStatusColor(metric.status)}`}>
                      {Math.round(metric.healthScore)}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-gray-500">连接性</div>
                    <div className="text-sm font-medium">{Math.round(metric.metrics.connectivity.score)}</div>
                    <div className="text-xs text-gray-400">延迟: {Math.round(metric.metrics.connectivity.latency)}ms</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">性能</div>
                    <div className="text-sm font-medium">{Math.round(metric.metrics.performance.score)}</div>
                    <div className="text-xs text-gray-400">错误率: {metric.metrics.performance.errorRate.toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">可靠性</div>
                    <div className="text-sm font-medium">{Math.round(metric.metrics.reliability.score)}</div>
                    <div className="text-xs text-gray-400">运行时间: {metric.metrics.reliability.uptime.toFixed(1)}%</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">安全性</div>
                    <div className="text-sm font-medium">{Math.round(metric.metrics.security.score)}</div>
                    <div className="text-xs text-gray-400">
                      {metric.metrics.security.sslSupport ? 'SSL支持' : '无SSL'}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-500">
                  <div>
                    <span className="font-medium">MTBF:</span> {formatDuration(metric.metrics.reliability.mtbf)}
                  </div>
                  <div>
                    <span className="font-medium">MTTR:</span> {formatDuration(metric.metrics.reliability.mttr)}
                  </div>
                  <div>
                    <span className="font-medium">匿名级别:</span> {metric.metrics.security.anonymityLevel}
                  </div>
                  <div>
                    <span className="font-medium">最后检查:</span> {metric.lastChecked.toLocaleTimeString()}
                  </div>
                </div>

                <div className="mt-3 pt-3 border-t border-gray-100">
                  <Button
                    onClick={() => setSelectedProxy(selectedProxy?.proxy.host === metric.proxy.host ? null : metric)}
                    variant="secondary"
                    size="sm"
                  >
                    {selectedProxy?.proxy.host === metric.proxy.host ? '隐藏详情' : '查看详情'}
                  </Button>
                </div>

                {selectedProxy?.proxy.host === metric.proxy.host && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">最近测试结果</h4>
                    <div className="space-y-2">
                      {metric.recentTests.map((test, testIndex) => (
                        <div key={testIndex} className="flex items-center justify-between text-xs">
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${test.success ? 'bg-green-500' : 'bg-red-500'}`} />
                            <span>{test.timestamp.toLocaleTimeString()}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span>{test.responseTime}ms</span>
                            {test.error && (
                              <Badge variant="danger">{test.error}</Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};