'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import socketManager from '@/lib/socket';
import type { SocketEvents as MonitoringSocketEvents } from '@/types/monitoring';
import type { TaskEvent } from '@/types/task';

// 合并所有可能的事件类型
type SocketEvents = MonitoringSocketEvents & {
  [key: string]: (...args: any[]) => void;
  // Socket.IO标准事件
  connect: () => void;
  disconnect: (reason: string) => void;
  connect_error: (error: Error) => void;
  // Task-specific events from the old useTaskSocket
  task_created: (event: TaskEvent) => void;
  task_status_changed: (event: TaskEvent) => void;
  task_stats_updated: (event: TaskEvent) => void;
  task_completed: (event: TaskEvent) => void;
  task_failed: (event: TaskEvent) => void;
  task_deleted: (event: TaskEvent) => void;
  url_processed: (data: any) => void;
  data_extracted: (data: any) => void;
};

type SocketEventHandlers = Partial<SocketEvents>;

/**
 * 统一的 Socket.IO Hook，用于管理连接状态和事件监听
 * @param eventHandlers - 一个事件名称到处理函数的回调映射，例如 { 'task_completed': (data) => console.log(data) }
 */
export function useSocket(eventHandlers?: SocketEventHandlers) {
  const [isConnected, setIsConnected] = useState(socketManager.isConnected());
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const handlersRef = useRef(eventHandlers);

  // 确保 eventHandlers 引用最新
  useEffect(() => {
    handlersRef.current = eventHandlers;
  }, [eventHandlers]);

  useEffect(() => {
    // 避免重复连接
    if (!socketManager.isConnected()) {
      socketManager.connect();
    }

    const handleConnect = () => {
      setIsConnected(true);
      setConnectionError(null);
    };

    const handleDisconnect = () => {
      setIsConnected(false);
    };

    const handleConnectError = (error: Error) => {
      setConnectionError(error.message);
      setIsConnected(false);
    };

    // 注册基础连接事件
    socketManager.on('connect', handleConnect);
    socketManager.on('disconnect', handleDisconnect);
    socketManager.on('connect_error', handleConnectError);

    // 动态注册用户传入的事件处理器
    if (handlersRef.current) {
      for (const event in handlersRef.current) {
        const handler = handlersRef.current[event as keyof SocketEventHandlers];
        if (handler) {
          socketManager.on(event as keyof SocketEvents, handler);
        }
      }
    }

    // 初始状态检查
    setIsConnected(socketManager.connected);

    return () => {
      // 清理基础连接事件
      socketManager.off('connect', handleConnect);
      socketManager.off('disconnect', handleDisconnect);
      socketManager.off('connect_error', handleConnectError);

      // 清理用户传入的事件处理器
      if (handlersRef.current) {
        for (const event in handlersRef.current) {
          const handler =
            handlersRef.current[event as keyof SocketEventHandlers];
          if (handler) {
            socketManager.off(event as keyof SocketEvents, handler);
          }
        }
      }
      // 注意：不要在这里断开连接，因为可能有其他组件在使用
      // socketManager.disconnect();
    };
  }, []); // 空依赖数组确保只在组件挂载和卸载时运行

  const emit = useCallback((event: string, data?: any) => {
    socketManager.emit(event, data);
  }, []);
  const on = useCallback((event: keyof MonitoringSocketEvents, data?: any) => {
    socketManager.on(event, data);
  }, []);
  const off = useCallback((event: keyof MonitoringSocketEvents, data?: any) => {
    socketManager.off(event, data);
  }, []);

  return {
    isConnected,
    connectionError,
    emit,
    on,
    off,
    socket: socketManager.getSocket(),
  };
}

/**
 * 轻量级的 Socket 事件监听 Hook - 用于监听单个事件
 * 当你只需要监听一个事件而不需要完整的连接状态时使用
 */
export function useSocketEvent<K extends keyof SocketEvents>(
  event: K,
  handler: SocketEvents[K],
  deps: React.DependencyList = []
) {
  useEffect(() => {
    socketManager.on(event, handler);

    return () => {
      socketManager.off(event, handler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [event, ...deps]);
}
