/**
 * 配置模块测试
 */

import { ConfigManager, ConfigValidator, ConfigFactory, defaultConfig } from './index';
import { TraversalStrategy, StorageType, LogLevel } from '@/interfaces';

describe('Config Module', () => {
  describe('ConfigValidator', () => {
    let validator: ConfigValidator;

    beforeEach(() => {
      validator = new ConfigValidator();
    });

    it('should validate default config successfully', () => {
      const result = validator.validateConfig(defaultConfig);
      expect(result.success).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    it('should fail validation for invalid seed URLs', () => {
      const invalidConfig = {
        ...defaultConfig,
        task: {
          ...defaultConfig.task,
          seedUrls: ['invalid-url', 'not-a-url'],
        },
      };

      const result = validator.validateConfig(invalidConfig);
      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors?.some((error: string) => error.includes('not a valid URL'))).toBe(true);
    });

    it('should fail validation for negative depth', () => {
      const invalidConfig = {
        ...defaultConfig,
        depthControl: {
          global: -1,
        },
      };

      const result = validator.validateConfig(invalidConfig);
      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors?.some((error: string) => error.includes('non-negative number'))).toBe(true);
    });

    it('should fail validation for invalid concurrency settings', () => {
      const invalidConfig = {
        ...defaultConfig,
        concurrency: {
          ...defaultConfig.concurrency,
          maxConcurrentRequests: 0,
        },
      };

      const result = validator.validateConfig(invalidConfig);
      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors?.some((error: string) => error.includes('positive number'))).toBe(true);
    });
  });

  describe('ConfigManager', () => {
    let manager: ConfigManager;

    beforeEach(() => {
      manager = new ConfigManager();
    });

    it('should initialize with default config', () => {
      const config = manager.getConfig();
      expect(config.task.traversalStrategy).toBe(TraversalStrategy.BFS);
      expect(config.storage.type).toBe(StorageType.MONGODB);
      expect(config.logging.level).toBe(LogLevel.INFO);
    });

    it('should update config successfully', () => {
      const updates = {
        task: {
          traversalStrategy: TraversalStrategy.DFS,
        },
        concurrency: {
          maxConcurrentRequests: 20,
        },
      };

      manager.updateConfig(updates);
      const config = manager.getConfig();

      expect(config.task.traversalStrategy).toBe(TraversalStrategy.DFS);
      expect(config.concurrency.maxConcurrentRequests).toBe(20);
    });

    it('should fail to update with invalid config', () => {
      const invalidUpdates = {
        depthControl: {
          global: -5,
        },
      };

      expect(() => {
        manager.updateConfig(invalidUpdates);
      }).toThrow('Configuration update validation failed');
    });

    it('should validate config correctly', () => {
      const validConfig = manager.getConfig();
      expect(manager.validateConfig(validConfig)).toBe(true);

      const invalidConfig = {
        ...validConfig,
        task: {
          ...validConfig.task,
          seedUrls: [],
        },
      };
      expect(manager.validateConfig(invalidConfig)).toBe(false);
    });

    it('should get specific config sections', () => {
      const taskConfig = manager.getTaskConfig();
      expect(taskConfig.traversalStrategy).toBe(TraversalStrategy.BFS);

      const storageConfig = manager.getStorageConfig();
      expect(storageConfig.type).toBe(StorageType.MONGODB);

      const aiConfig = manager.getAIConfig();
      expect(aiConfig.enabled).toBe(false);
    });

    it('should clone config correctly', () => {
      const original = manager.getConfig();
      const cloned = manager.cloneConfig();

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);

      // Modify cloned config
      cloned.task.traversalStrategy = TraversalStrategy.DFS;
      expect(original.task.traversalStrategy).toBe(TraversalStrategy.BFS);
    });

    it('should merge config correctly', () => {
      const updates = {
        task: {
          traversalStrategy: TraversalStrategy.DFS,
        },
      };

      const merged = manager.mergeConfig(updates);
      expect(merged.task.traversalStrategy).toBe(TraversalStrategy.DFS);
      expect(merged.storage.type).toBe(StorageType.MONGODB); // Should preserve other values
    });

    it('should reset to default config', () => {
      // Modify config
      manager.updateConfig({
        task: {
          traversalStrategy: TraversalStrategy.DFS,
        },
      });

      expect(manager.getConfig().task.traversalStrategy).toBe(TraversalStrategy.DFS);

      // Reset to default
      manager.resetToDefault();
      expect(manager.getConfig().task.traversalStrategy).toBe(TraversalStrategy.BFS);
    });
  });

  describe('ConfigFactory', () => {
    it('should create task config correctly', () => {
      const taskConfig = ConfigFactory.createTaskConfig({
        seedUrls: ['https://example.com'],
        strategy: TraversalStrategy.DFS,
        name: 'test-task',
      });

      expect(taskConfig.seedUrls).toEqual(['https://example.com']);
      expect(taskConfig.traversalStrategy).toBe(TraversalStrategy.DFS);
      expect(taskConfig.name).toBe('test-task');
    });

    it('should create crawl scope config correctly', () => {
      const scopeConfig = ConfigFactory.createCrawlScopeConfig({
        allowedDomains: ['example.com'],
        followSubdomains: true,
      });

      expect(scopeConfig.allowedDomains).toEqual(['example.com']);
      expect(scopeConfig.followSubdomains).toBe(true);
      expect(scopeConfig.ignoreWWW).toBe(true);
    });

    it('should create full config correctly', () => {
      const config = ConfigFactory.createFullConfig({
        seedUrls: ['https://example.com'],
        allowedDomains: ['example.com'],
        strategy: TraversalStrategy.DFS,
        maxDepth: 5,
        maxConcurrent: 10,
        enableAI: true,
        logLevel: LogLevel.DEBUG,
      });

      expect(config.task.seedUrls).toEqual(['https://example.com']);
      expect(config.task.traversalStrategy).toBe(TraversalStrategy.DFS);
      expect(config.crawlScope.allowedDomains).toEqual(['example.com']);
      expect(config.depthControl.global).toBe(5);
      expect(config.concurrency.maxConcurrentRequests).toBe(10);
      expect(config.ai.enabled).toBe(true);
      expect(config.logging.level).toBe(LogLevel.DEBUG);
    });

    it('should create development config correctly', () => {
      const config = ConfigFactory.createDevelopmentConfig(
        ['https://example.com'],
        ['example.com']
      );

      expect(config.depthControl.global).toBe(2);
      expect(config.concurrency.maxConcurrentRequests).toBe(3);
      expect(config.logging.level).toBe(LogLevel.DEBUG);
      expect(config.ai.enabled).toBe(false);
    });

    it('should create production config correctly', () => {
      const config = ConfigFactory.createProductionConfig(
        ['https://example.com'],
        ['example.com']
      );

      expect(config.depthControl.global).toBe(5);
      expect(config.concurrency.maxConcurrentRequests).toBe(10);
      expect(config.logging.level).toBe(LogLevel.INFO);
      expect(config.ai.enabled).toBe(true);
    });
  });
});
