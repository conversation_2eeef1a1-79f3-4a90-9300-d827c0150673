/**
 * 解析器相关接口定义
 */

import { FetcherType } from '@/types/common';

/**
 * 解析器输入接口
 */
export interface ParserInput {
  /** 页面内容 (HTML, JSON, etc.) */
  content: string;
  /** 页面URL */
  url: string;
  /** 下载方式 */
  fetcherType: FetcherType;
  /** HTTP 响应头 */
  headers?: Record<string, string>;
  /** HTTP 状态码 */
  statusCode?: number;
  /** 响应时间 (毫秒) */
  responseTime?: number;
  /** 页面深度 */
  depth: number;
  /** 父页面URL */
  parentUrl?: string;
  /** 额外的上下文信息 */
  context?: Record<string, any>;
  /** 内容类型 */
  contentType?: string;
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 解析器输出接口
 */
export interface ParserOutput {
  /** 提取的结构化数据 */
  extractedData: Record<string, any> | null;
  /** 发现的新URL列表 */
  newUrls: string[];
  /** 解析器元数据 */
  metadata?: {
    /** 解析器名称 */
    parserName: string;
    /** 解析时间 (毫秒) */
    parseTime: number;
    /** 提取的数据字段数量 */
    extractedFieldsCount: number;
    /** 发现的URL数量 */
    discoveredUrlsCount: number;
  };
  /** 错误信息 (如果解析失败) */
  error?: {
    message: string;
    code: string;
    details?: any;
  };
}

/**
 * 解析器接口
 */
export interface IParser {
  /** 解析器名称 */
  readonly name: string;
  /** 解析器版本 */
  readonly version: string;
  /** 解析器描述 */
  readonly description?: string;

  /**
   * 判断此解析器是否适用于该页面
   * @param input 解析器输入
   * @returns 是否可以解析
   */
  canParse(input: ParserInput): boolean | Promise<boolean>;

  /**
   * 执行解析
   * @param input 解析器输入
   * @returns 解析结果
   */
  parse(input: ParserInput): Promise<ParserOutput>;

  /**
   * 解析器初始化 (可选)
   */
  initialize?(): Promise<void>;

  /**
   * 解析器清理 (可选)
   */
  cleanup?(): Promise<void>;
}

/**
 * 解析器配置接口
 */
export interface ParserConfig {
  /** 解析器名称 */
  name: string;
  /** 是否启用 */
  enabled: boolean;
  /** 优先级 (数字越大优先级越高) */
  priority: number;
  /** 解析器特定配置 */
  options?: Record<string, any>;
  /** 适用的URL模式 (正则表达式) */
  urlPatterns?: RegExp[];
  /** 适用的域名列表 */
  domains?: string[];
  /** 适用的内容类型 */
  contentTypes?: string[];
}

/**
 * 解析器管理器接口
 */
export interface IParserManager {
  /**
   * 注册解析器
   * @param parser 解析器实例
   * @param config 解析器配置
   */
  registerParser(parser: IParser, config: ParserConfig): void;

  /**
   * 注销解析器
   * @param name 解析器名称
   */
  unregisterParser(name: string): void;

  /**
   * 获取适用的解析器
   * @param input 解析器输入
   * @returns 适用的解析器列表 (按优先级排序)
   */
  getApplicableParsers(input: ParserInput): Promise<IParser[]>;

  /**
   * 执行解析
   * @param input 解析器输入
   * @returns 解析结果
   */
  parse(input: ParserInput): Promise<ParserOutput>;

  /**
   * 获取所有已注册的解析器
   */
  getRegisteredParsers(): Map<string, { parser: IParser; config: ParserConfig }>;

  /**
   * 初始化所有解析器
   */
  initialize(): Promise<void>;

  /**
   * 清理所有解析器
   */
  cleanup(): Promise<void>;
}

/**
 * Cheerio 解析器选项
 */
export interface CheerioParserOptions {
  /** CSS 选择器映射 */
  selectors: Record<string, string>;
  /** 是否提取链接 */
  extractLinks?: boolean;
  /** 链接选择器 */
  linkSelector?: string;
  /** 链接属性名 */
  linkAttribute?: string;
  /** 是否清理文本 */
  cleanText?: boolean;
  /** 自定义处理函数 */
  customProcessors?: Record<string, (element: any) => any>;
}

/**
 * 正则表达式解析器选项
 */
export interface RegexParserOptions {
  /** 正则表达式映射 */
  patterns: Record<string, RegExp>;
  /** 是否提取链接 */
  extractLinks?: boolean;
  /** 链接正则表达式 */
  linkPattern?: RegExp;
  /** 是否多行匹配 */
  multiline?: boolean;
  /** 是否全局匹配 */
  global?: boolean;
}

/**
 * AI 解析器选项
 */
export interface AIParserOptions {
  /** 数据提取模式 (自然语言描述) */
  schema: Record<string, string>;
  /** AI 模型名称 */
  model?: string;
  /** 最大 token 数 */
  maxTokens?: number;
  /** 温度参数 */
  temperature?: number;
  /** 是否提取链接 */
  extractLinks?: boolean;
  /** 链接提取提示 */
  linkExtractionPrompt?: string;
}

/**
 * Mozilla Readability 解析器选项
 */
export interface ReadabilityParserOptions {
  /** 是否提取链接 */
  extractLinks?: boolean;
  /** 是否包含原始HTML */
  includeRawHtml?: boolean;
  /** 最小内容长度 */
  minContentLength?: number;
  /** 最小评分 */
  minScore?: number;
  /** 最大要解析的元素数量 */
  maxElemsToParse?: number;
  /** 顶级候选数量 */
  nbTopCandidates?: number;
  /** 要保留的CSS类名 */
  classesToPreserve?: string[];
  /** 是否保留类名 */
  keepClasses?: boolean;
}
