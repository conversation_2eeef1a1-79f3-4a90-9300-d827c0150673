import React, { useState, useEffect, useRef } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';
import { ProxyList } from './ProxyList';
import { ProxyStats } from './ProxyStats';
import { ProxyControls } from './ProxyControls';
import { AddProxyModal } from './AddProxyModal';
import { EditProxyModal } from './EditProxyModal';
import { ProxyPerformanceChart } from './ProxyPerformanceChart';
import { BulkOperations } from './BulkOperations';
import { useProxyManager } from '@/hooks/useProxyManager';
import { ProxyConfig } from '@/types/task';
import { logger } from '@/lib/utils';

interface ProxyManagerProps {
  className?: string;
}

// 类型转换函数 - 修复完整的ProxyConfig
const convertToSharedProxyConfig = (proxy: any): ProxyConfig => ({
  id: proxy.id || `${proxy.host}:${proxy.port}`,
  name: proxy.name || `${proxy.host}:${proxy.port}`,
  host: proxy.host || proxy.proxy_address,
  port: proxy.port,
  username: proxy.username || proxy.auth?.username,
  password: proxy.password || proxy.auth?.password,
  type: (proxy.protocol || proxy.type || 'http') as 'http' | 'https' | 'socks4' | 'socks5',
  protocol: proxy.protocol || proxy.type || 'http',
  auth: proxy.auth,
  status: proxy.status || 'INACTIVE',
  lastChecked: proxy.lastChecked || new Date().toISOString(),
  responseTime: proxy.responseTime || 0,
  successRate: proxy.successRate || 0,
  totalRequests: proxy.totalRequests || 0,
  failedRequests: proxy.failedRequests || 0,
  createdAt: proxy.createdAt || proxy.created_at || new Date().toISOString(),
  updatedAt: proxy.updatedAt || new Date().toISOString(),
  // 地域信息字段映射
  countryCode: proxy.countryCode || proxy.country_code,
  cityName: proxy.cityName || proxy.city_name,
  asnName: proxy.asnName || proxy.asn_name,
  asnNumber: proxy.asnNumber || proxy.asn_number,
  highCountryConfidence: proxy.highCountryConfidence || proxy.high_country_confidence,
});

export const ProxyManager: React.FC<ProxyManagerProps> = ({ className = '' }) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [editingProxy, setEditingProxy] = useState<ProxyConfig | null>(null);
  const [selectedProxies, setSelectedProxies] = useState<string[]>([]);
  const [isOperating, setIsOperating] = useState(false);
  const [testingAll, setTestingAll] = useState(false);
  const proxyListRef = useRef<{ resetSorting: () => void }>(null);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const {
    proxies,
    stats,
    loading,
    error,
    refreshData,
    addProxy,
    removeProxy,
    updateProxy,
    testAllProxies,
    clearAllProxies,
    loadProxiesFromFile,
  } = useProxyManager();

  // 优化的自动刷新：减少刷新频率，避免操作时刷新
  useEffect(() => {
    const scheduleRefresh = () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      // 如果正在进行操作，延迟刷新；代理检测频率可以稍低一些
      const delay = isOperating ? 45000 : 30000; // 操作时45秒，平时30秒

      refreshTimeoutRef.current = setTimeout(() => {
        if (!isOperating) {
          refreshData();
        }
        scheduleRefresh();
      }, delay);
    };

    scheduleRefresh();

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [refreshData, isOperating]);

  const handleAddProxy = async (proxyData: any) => {
    try {
      setIsOperating(true);
      await addProxy(proxyData);
      setShowAddModal(false);
      // 不立即刷新，添加后稍等片刻再更新UI
      setTimeout(() => {
        if (!isOperating) {
          refreshData();
        }
      }, 1000);
    } catch (error) {
      console.error('Failed to add proxy:', error);
    } finally {
      setIsOperating(false);
    }
  };

  const handleRemoveSelected = async () => {
    if (selectedProxies.length === 0) return;

    try {
      setIsOperating(true);
      // 批量删除时使用 Promise.allSettled 避免单个失败影响全部
      const removePromises = selectedProxies.map(async (proxyKey) => {
        const proxy = proxies.find(p => `${p.host}:${p.port}` === proxyKey);
        if (proxy) {
          return removeProxy(proxy);
        }
        return Promise.resolve();
      });

      await Promise.allSettled(removePromises);
      setSelectedProxies([]);

      // 延迟刷新避免界面闪烁
      setTimeout(() => {
        if (!isOperating) {
          refreshData();
        }
      }, 500);
    } catch (error) {
      console.error('Failed to remove proxies:', error);
    } finally {
      setIsOperating(false);
    }
  };

  const handleTestAll = async () => {
    try {
      setTestingAll(true);
      setIsOperating(true);
      await testAllProxies();
      // 测试完成后刷新数据
      setTimeout(() => {
        if (!isOperating) {
          refreshData();
        }
      }, 200);
    } catch (error) {
      console.error('Failed to test proxies:', error);
    } finally {
      setTestingAll(false);
      setIsOperating(false);
    }
  };

  const handleClearAll = async () => {
    // 显示确认对话框
    setShowClearConfirm(true);
  };

  const handleConfirmClearAll = async () => {
    setShowClearConfirm(false);

    try {
      setIsOperating(true);
      await clearAllProxies();
      setSelectedProxies([]);
      // 清空后立即更新UI
      setTimeout(() => {
        refreshData();
      }, 500);
    } catch (error) {
      logger.error('Failed to clear proxies', error);
    } finally {
      setIsOperating(false);
    }
  };

  const handleUpdateProxy = async (oldProxy: any, newProxy: any) => {
    try {
      setIsOperating(true);
      await updateProxy(convertToSharedProxyConfig(oldProxy), convertToSharedProxyConfig(newProxy));
      setEditingProxy(null);
      setTimeout(() => {
        if (!isOperating) {
          refreshData();
        }
      }, 500);
    } catch (error) {
      console.error('Failed to update proxy:', error);
    } finally {
      setIsOperating(false);
    }
  };

  const handleLoadFromFile = async (file: File) => {
    try {
      setIsOperating(true);
      await loadProxiesFromFile(file);
      // 加载完成后刷新数据
      setTimeout(() => {
        if (!isOperating) {
          refreshData();
        }
      }, 1000);
    } catch (error) {
      console.error('Failed to load proxies from file:', error);
    } finally {
      setIsOperating(false);
    }
  };

  // 手动刷新
  const handleManualRefresh = async () => {
    setIsOperating(true);
    try {
      await refreshData();
      if (proxyListRef.current) {
        proxyListRef.current.resetSorting();
      }
    } finally {
      setIsOperating(false);
    }
  };

  if (error && !proxies.length) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">代理管理器加载失败</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={handleManualRefresh} variant="primary" loading={loading}>
            重试
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 代理统计 */}
      <ProxyStats stats={stats} loading={loading && !proxies.length} />

      {/* 代理控制面板 */}
      <ProxyControls
        selectedCount={selectedProxies.length}
        totalCount={proxies.length}
        loading={isOperating || loading}
        testingAll={testingAll}
        onAddProxy={() => setShowAddModal(true)}
        onRemoveSelected={handleRemoveSelected}
        onTestAll={handleTestAll}
        onClearAll={handleClearAll}
        onLoadFromFile={handleLoadFromFile}
        onRefresh={handleManualRefresh}
      />

      {/* 代理列表 */}
      <ProxyList
        ref={proxyListRef}
        proxies={proxies}
        loading={loading && !proxies.length}
        selectedProxies={selectedProxies}
        onSelectionChange={setSelectedProxies}
        onRemoveProxy={async (proxy) => {
          try {
            await removeProxy(proxy);
            refreshData();
          } catch (error) {
            console.error('Failed to remove proxy:', error);
          }
        }}
        onUpdateProxy={(proxy) => setEditingProxy(proxy)}
      />

      {/* 添加代理模态框 */}
      {showAddModal && (
        <AddProxyModal
          onAdd={handleAddProxy}
          onClose={() => setShowAddModal(false)}
        />
      )}

      {/* 编辑代理模态框 */}
      {editingProxy && (
        <EditProxyModal
          proxy={editingProxy}
          onUpdate={(updatedProxy) => {
            handleUpdateProxy(editingProxy, updatedProxy);
          }}
          onClose={() => setEditingProxy(null)}
        />
      )}

      {/* 确认清空对话框 */}
      <ConfirmDialog
        isOpen={showClearConfirm}
        title="确认清空所有代理"
        message={`此操作将删除所有 ${proxies.length} 个代理，且不可撤销。请确认您要继续操作。`}
        confirmText="确认清空"
        cancelText="取消"
        onConfirm={handleConfirmClearAll}
        onCancel={() => setShowClearConfirm(false)}
        variant="danger"
      />
    </div>
  );
};
