// testEngines.js

const {
  googleSearch,
  bingSearch,
  yahooSearch,
  duckduckgoSearch,
  givewaterSearch,
  ecosiaSearch,
} = require('./engines');

async function testAllEngines(query) {
  console.log('测试 Google 搜索：');
  const googleResults = await googleSearch(query);
  console.log(googleResults);

  console.log('\n测试 Bing 搜索：');
  const bingResults = await bingSearch(query);
  console.log(bingResults);

  console.log('\n测试 Yahoo 搜索：');
  const yahooResults = await yahooSearch(query);
  console.log(yahooResults);

  console.log('\n测试 DuckDuckGo 搜索：');
  const duckResults = await duckduckgoSearch(query);
  console.log(duckResults);

  console.log('\n测试 givewaterSearch 搜索：');
  const giveResults = await givewaterSearch(query);
  console.log(giveResults);
  
  console.log('\n测试 ecosiaSearch 搜索：');
  const ecosiaResults = await ecosiaSearch(query);
  console.log(ecosiaResults);

  process.exit(0);
}

const query = 'EasyVoice';
testAllEngines(query);
