/**
 * Nodex API 实时监控示例
 * 
 * 演示如何实时监控爬取任务的进度和系统状态
 * 
 * 运行方法: node examples/real-time-monitor.js
 */

const API_BASE = 'http://localhost:3000';

class NodexMonitor {
  constructor() {
    this.isRunning = false;
    this.monitorInterval = null;
  }

  // 启动监控
  async start() {
    console.log('📊 启动 Nodex 实时监控');
    console.log('=======================');
    
    this.isRunning = true;
    
    // 创建一个长时间运行的任务用于监控
    const taskId = await this.createLongRunningTask();
    
    if (taskId) {
      await this.startTask(taskId);
      await this.monitorTask(taskId);
    }
    
    // 启动系统监控
    this.startSystemMonitor();
    
    // 设置退出处理
    process.on('SIGINT', () => {
      console.log('\n\n🛑 停止监控...');
      this.stop();
      process.exit(0);
    });
  }

  // 创建长时间运行的任务
  async createLongRunningTask() {
    try {
      const taskConfig = {
        name: '实时监控示例任务',
        description: '用于演示实时监控的长时间任务',
        config: {
          startUrls: [
            'https://httpbin.org/html',
            'https://httpbin.org/json',
            'https://httpbin.org/xml',
            'https://httpbin.org/delay/2',
            'https://httpbin.org/delay/3'
          ],
          maxDepth: 2,
          concurrency: 2,
          delay: 2000, // 增加延迟让任务运行更久
          parser: {
            type: 'cheerio',
            selectors: {
              title: 'title',
              content: 'body'
            }
          }
        }
      };

      const response = await fetch(`${API_BASE}/api/v1/tasks`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(taskConfig)
      });

      const result = await response.json();
      console.log(`✅ 任务已创建，ID: ${result.data.id}`);
      return result.data.id;

    } catch (error) {
      console.error('❌ 创建任务失败:', error.message);
      return null;
    }
  }

  // 启动任务
  async startTask(taskId) {
    try {
      await fetch(`${API_BASE}/api/v1/tasks/${taskId}/start`, {
        method: 'POST'
      });
      console.log('🚀 任务已启动，开始监控...\n');
    } catch (error) {
      console.error('❌ 启动任务失败:', error.message);
    }
  }

  // 监控特定任务
  async monitorTask(taskId) {
    let lastStatus = '';
    let lastResultCount = 0;

    const taskMonitor = setInterval(async () => {
      try {
        // 获取任务状态
        const taskResponse = await fetch(`${API_BASE}/api/v1/tasks/${taskId}`);
        const taskData = await taskResponse.json();
        const currentStatus = taskData.data.status;

        // 获取结果数量
        const resultsResponse = await fetch(`${API_BASE}/api/v1/results/${taskId}?limit=1`);
        const resultsData = await resultsResponse.json();
        const currentResultCount = resultsData.total || 0;

        // 只在状态或结果数量变化时输出
        if (currentStatus !== lastStatus || currentResultCount !== lastResultCount) {
          const timestamp = new Date().toLocaleTimeString();
          console.log(`[${timestamp}] 任务状态: ${currentStatus} | 已爬取: ${currentResultCount} 条`);
          
          lastStatus = currentStatus;
          lastResultCount = currentResultCount;
        }

        // 如果任务完成，停止监控
        if (currentStatus === 'completed' || currentStatus === 'failed') {
          clearInterval(taskMonitor);
          console.log(`\n✅ 任务监控结束，最终状态: ${currentStatus}`);
          
          // 显示最终结果
          if (currentStatus === 'completed') {
            await this.showFinalResults(taskId);
          }
        }

      } catch (error) {
        console.error('❌ 监控任务时出错:', error.message);
      }
    }, 2000); // 每2秒检查一次
  }

  // 启动系统监控
  startSystemMonitor() {
    console.log('\n📈 系统状态监控:');
    console.log('================');

    this.monitorInterval = setInterval(async () => {
      try {
        // 获取系统统计
        const statsResponse = await fetch(`${API_BASE}/api/v1/stats`);
        const stats = await statsResponse.json();

        // 获取任务列表
        const tasksResponse = await fetch(`${API_BASE}/api/v1/tasks?limit=100`);
        const tasks = await tasksResponse.json();

        // 统计各状态任务数量
        const statusCounts = {};
        tasks.data.forEach(task => {
          statusCounts[task.status] = (statusCounts[task.status] || 0) + 1;
        });

        const timestamp = new Date().toLocaleTimeString();
        console.log(`\n[${timestamp}] 系统状态:`);
        console.log(`  总任务数: ${tasks.data.length}`);
        Object.entries(statusCounts).forEach(([status, count]) => {
          console.log(`  ${status}: ${count}`);
        });

        if (stats.data) {
          console.log(`  内存使用: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
        }

      } catch (error) {
        console.error('❌ 获取系统状态失败:', error.message);
      }
    }, 10000); // 每10秒检查一次系统状态
  }

  // 显示最终结果
  async showFinalResults(taskId) {
    try {
      const response = await fetch(`${API_BASE}/api/v1/results/${taskId}?limit=5`);
      const results = await response.json();

      console.log('\n📋 最终结果预览:');
      console.log('================');
      
      if (results.data && results.data.length > 0) {
        results.data.forEach((item, index) => {
          console.log(`${index + 1}. ${item.url}`);
          console.log(`   标题: ${item.data?.title || 'N/A'}`);
          console.log(`   时间: ${new Date(item.timestamp).toLocaleString()}`);
          console.log('');
        });
      } else {
        console.log('暂无结果数据');
      }
    } catch (error) {
      console.error('❌ 获取最终结果失败:', error.message);
    }
  }

  // 停止监控
  stop() {
    this.isRunning = false;
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
    }
  }
}

// 主函数
async function main() {
  try {
    // 先检查服务器状态
    const healthResponse = await fetch(`${API_BASE}/health`);
    const health = await healthResponse.json();
    
    if (health.status !== 'ok') {
      throw new Error('服务器状态异常');
    }

    // 启动监控
    const monitor = new NodexMonitor();
    await monitor.start();

  } catch (error) {
    console.error('❌ 启动监控失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 提示: 请确保 Nodex API 服务器正在运行');
      console.log('启动命令: npm start');
    }
  }
}

// 运行示例
if (require.main === module) {
  main();
}
