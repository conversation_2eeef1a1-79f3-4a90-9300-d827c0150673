/**
 * 解析器工厂类
 */

import {
  <PERSON>ars<PERSON>,
  ParserConfig,
  ParserInput,
  CheerioParserOptions,
  AIParserOptions,
} from '@/interfaces';
import { CheerioParser } from './cheerio-parser';
import { AIParser } from './ai-parser';
import { JsonParser, JsonParserOptions } from './json-parser';
import { XmlParser, XmlParserOptions } from './xml-parser';

/**
 * 解析器类型枚举
 */
export enum ParserType {
  /** Cheerio HTML/XML 解析器 */
  CHEERIO = 'cheerio',
  /** AI 智能解析器 */
  AI = 'ai',
  /** JSON 解析器 */
  JSON = 'json',
  /** XML 解析器 */
  XML = 'xml',
  /** 自定义解析器 */
  CUSTOM = 'custom',
}

/**
 * 解析器创建配置接口
 */
export interface ParserCreationConfig {
  type: ParserType;
  name?: string;
  priority?: number;
  enabled?: boolean;
  contentTypes?: string[];
  urlPatterns?: string[];
  options?: any;
}

/**
 * 解析器模板接口
 */
export interface ParserTemplate {
  name: string;
  description: string;
  type: ParserType;
  defaultConfig: ParserCreationConfig;
  suggestedUseCase: string;
}

/**
 * 解析器工厂类
 */
export class ParserFactory {
  private static instance: ParserFactory;
  private parserCache: Map<string, IParser> = new Map();
  private templates: Map<string, ParserTemplate> = new Map();

  private constructor() {
    this.initializeTemplates();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ParserFactory {
    if (!ParserFactory.instance) {
      ParserFactory.instance = new ParserFactory();
    }
    return ParserFactory.instance;
  }

  /**
   * 创建解析器
   */
  public createParser(config: ParserCreationConfig, aiEngine?: any): { parser: IParser; config: ParserConfig } {
    const cacheKey = this.generateCacheKey(config);
    
    // 检查缓存
    if (this.parserCache.has(cacheKey)) {
      const cachedParser = this.parserCache.get(cacheKey)!;
      return {
        parser: cachedParser,
        config: this.createParserConfig(config),
      };
    }

    let parser: IParser;

    switch (config.type) {
      case ParserType.CHEERIO:
        parser = new CheerioParser(config.options as CheerioParserOptions);
        break;

      case ParserType.AI:
        if (!aiEngine) {
          throw new Error('AI engine is required for AI parser');
        }
        parser = new AIParser(aiEngine, config.options as AIParserOptions);
        break;

      case ParserType.JSON:
        parser = new JsonParser(config.options as JsonParserOptions);
        break;

      case ParserType.XML:
        parser = new XmlParser(config.options as XmlParserOptions);
        break;

      case ParserType.CUSTOM:
        if (!config.options?.parserClass) {
          throw new Error('Custom parser class is required');
        }
        parser = new config.options.parserClass(config.options.constructorArgs);
        break;

      default:
        throw new Error(`Unsupported parser type: ${config.type}`);
    }

    // 缓存解析器
    this.parserCache.set(cacheKey, parser);

    return {
      parser,
      config: this.createParserConfig(config),
    };
  }

  /**
   * 批量创建解析器
   */
  public createParsers(configs: ParserCreationConfig[], aiEngine?: any): Array<{ parser: IParser; config: ParserConfig }> {
    return configs.map(config => this.createParser(config, aiEngine));
  }

  /**
   * 根据内容推荐解析器
   */
  public recommendParser(input: ParserInput): ParserCreationConfig[] {
    const recommendations: ParserCreationConfig[] = [];
    
    // 分析内容类型
    const contentType = input.headers?.['content-type'] || '';
    const url = input.url.toLowerCase();
    const content = input.content.trim();

    // JSON 解析器推荐
    if (contentType.includes('json') || url.includes('.json') || url.includes('/api/')) {
      recommendations.push({
        type: ParserType.JSON,
        priority: 90,
        options: this.generateJsonParserOptions(input),
      });
    }

    // XML 解析器推荐
    if (contentType.includes('xml') || url.includes('.xml') || url.includes('.rss') || 
        content.startsWith('<?xml') || content.startsWith('<rss')) {
      recommendations.push({
        type: ParserType.XML,
        priority: 85,
        options: this.generateXmlParserOptions(input),
      });
    }

    // HTML 解析器推荐
    if (contentType.includes('html') || content.includes('<html') || content.includes('<!DOCTYPE')) {
      recommendations.push({
        type: ParserType.CHEERIO,
        priority: 80,
        options: this.generateCheerioParserOptions(input),
      });
    }

    // AI 解析器作为通用备选
    recommendations.push({
      type: ParserType.AI,
      priority: 50,
      options: this.generateAIParserOptions(input),
    });

    return recommendations.sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }

  /**
   * 获取解析器模板
   */
  public getTemplates(): ParserTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * 获取特定模板
   */
  public getTemplate(name: string): ParserTemplate | null {
    return this.templates.get(name) || null;
  }

  /**
   * 从模板创建解析器
   */
  public createFromTemplate(templateName: string, customOptions?: any, aiEngine?: any): { parser: IParser; config: ParserConfig } {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Template '${templateName}' not found`);
    }

    const config = {
      ...template.defaultConfig,
      options: { ...template.defaultConfig.options, ...customOptions },
    };

    return this.createParser(config, aiEngine);
  }

  /**
   * 初始化解析器模板
   */
  private initializeTemplates(): void {
    // HTML 新闻文章模板
    this.templates.set('news-article', {
      name: 'news-article',
      description: '新闻文章解析器，提取标题、内容、作者、发布时间等',
      type: ParserType.CHEERIO,
      suggestedUseCase: '新闻网站、博客文章',
      defaultConfig: {
        type: ParserType.CHEERIO,
        priority: 80,
        options: {
          selectors: {
            title: 'h1, .title, .headline, [class*="title"]',
            content: '.content, .article-body, .post-content, main',
            author: '.author, .byline, [class*="author"]',
            publishDate: '.date, .publish-date, time[datetime]',
            summary: '.summary, .excerpt, .description',
          },
          extractLinks: true,
          linkSelector: 'a[href]',
        },
      },
    });

    // 电商产品模板
    this.templates.set('ecommerce-product', {
      name: 'ecommerce-product',
      description: '电商产品解析器，提取商品名称、价格、描述、图片等',
      type: ParserType.CHEERIO,
      suggestedUseCase: '电商网站、产品页面',
      defaultConfig: {
        type: ParserType.CHEERIO,
        priority: 85,
        options: {
          selectors: {
            name: 'h1, .product-title, .product-name',
            price: '.price, .cost, [class*="price"]',
            description: '.description, .product-description',
            images: 'img[src], .product-images img',
            rating: '.rating, .stars, [class*="rating"]',
            availability: '.availability, .stock, [class*="stock"]',
          },
          extractLinks: true,
        },
      },
    });

    // JSON API 模板
    this.templates.set('json-api', {
      name: 'json-api',
      description: 'JSON API 解析器，智能提取结构化数据',
      type: ParserType.JSON,
      suggestedUseCase: 'REST API、JSON 数据源',
      defaultConfig: {
        type: ParserType.JSON,
        priority: 90,
        options: {
          pathMappings: {},
          extractNested: true,
          extractArrays: true,
          linkExtraction: {
            enabled: true,
            pathPatterns: ['*.url', '*.link', '*.href'],
            urlFields: ['url', 'link', 'href', 'endpoint'],
          },
        },
      },
    });

    // RSS Feed 模板
    this.templates.set('rss-feed', {
      name: 'rss-feed',
      description: 'RSS/Atom Feed 解析器',
      type: ParserType.XML,
      suggestedUseCase: 'RSS feeds、Atom feeds',
      defaultConfig: {
        type: ParserType.XML,
        priority: 95,
        options: {
          elementMappings: {
            title: 'channel.title',
            description: 'channel.description',
            items: 'channel.item',
          },
          linkExtraction: {
            enabled: true,
            linkElements: ['link', 'url'],
            linkAttributes: ['href'],
          },
        },
      },
    });

    // AI 智能解析模板
    this.templates.set('ai-smart', {
      name: 'ai-smart',
      description: 'AI 智能解析器，适用于复杂或非结构化内容',
      type: ParserType.AI,
      suggestedUseCase: '复杂页面、非结构化内容',
      defaultConfig: {
        type: ParserType.AI,
        priority: 70,
        options: {
          schema: {},
          extractLinks: true,
          model: 'gpt-3.5-turbo',
          maxTokens: 4000,
          temperature: 0.1,
        },
      },
    });
  }

  /**
   * 生成JSON解析器选项
   */
  private generateJsonParserOptions(input: ParserInput): JsonParserOptions {
    return {
      pathMappings: {},
      extractNested: true,
      extractArrays: true,
      linkExtraction: {
        enabled: true,
        pathPatterns: ['*.url', '*.link', '*.href'],
        urlFields: ['url', 'link', 'href', 'src', 'endpoint'],
      },
    };
  }

  /**
   * 生成XML解析器选项
   */
  private generateXmlParserOptions(input: ParserInput): XmlParserOptions {
    return {
      elementMappings: {},
      linkExtraction: {
        enabled: true,
        linkElements: ['link', 'url', 'loc'],
        linkAttributes: ['href', 'src'],
      },
    };
  }

  /**
   * 生成Cheerio解析器选项
   */
  private generateCheerioParserOptions(input: ParserInput): CheerioParserOptions {
    return {
      selectors: {
        title: 'h1, title, .title',
        content: '.content, .main, article',
        links: 'a[href]',
      },
      extractLinks: true,
      linkSelector: 'a[href]',
    };
  }

  /**
   * 生成AI解析器选项
   */
  private generateAIParserOptions(input: ParserInput): AIParserOptions {
    return {
      schema: {},
      extractLinks: true,
      model: 'gpt-3.5-turbo',
      maxTokens: 4000,
      temperature: 0.1,
    };
  }

  /**
   * 创建解析器配置
   */
  private createParserConfig(config: ParserCreationConfig): ParserConfig {
    return {
      name: config.name || `${config.type}-parser`,
      enabled: config.enabled !== false,
      priority: config.priority || 50,
      contentTypes: config.contentTypes || [],
      urlPatterns: (config.urlPatterns || []).map(pattern =>
        typeof pattern === 'string' ? new RegExp(pattern) : pattern
      ),
    };
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(config: ParserCreationConfig): string {
    return `${config.type}-${JSON.stringify(config.options || {})}`;
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.parserCache.clear();
  }

  /**
   * 获取缓存统计
   */
  public getCacheStats(): {
    totalCached: number;
    cacheKeys: string[];
  } {
    return {
      totalCached: this.parserCache.size,
      cacheKeys: Array.from(this.parserCache.keys()),
    };
  }

  /**
   * 验证解析器配置
   */
  public validateConfig(config: ParserCreationConfig): boolean {
    if (!config.type || !Object.values(ParserType).includes(config.type)) {
      return false;
    }

    // 根据类型验证特定选项
    switch (config.type) {
      case ParserType.CHEERIO:
        return config.options?.selectors && typeof config.options.selectors === 'object';
      
      case ParserType.JSON:
        return true; // JSON 解析器选项都是可选的
      
      case ParserType.XML:
        return true; // XML 解析器选项都是可选的
      
      case ParserType.AI:
        return config.options?.schema !== undefined;
      
      case ParserType.CUSTOM:
        return config.options?.parserClass !== undefined;
      
      default:
        return false;
    }
  }
}
