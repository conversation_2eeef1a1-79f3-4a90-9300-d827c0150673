/**
 * 精简版系统控制路由
 * 
 * 提供系统状态查询、健康检查、重启、配置等核心API端点
 */

import { Router } from 'express';
import { TaskManager } from '@/app/task-manager';
import { Server as SocketIOServer } from 'socket.io';
import { error } from '@/utils';

/**
 * 系统状态接口
 */
export interface SystemStatus {
  status: 'running' | 'stopped' | 'error';
  uptime: number;
  stats: {
    totalTasks: number;
    activeTasks: number;
    totalUrls: number;
    processedUrls: number;
  };
  modules: {
    crawler: boolean;
    storage: boolean;
    ai: boolean;
  };
  timestamp: string;
}

/**
 * 创建精简版系统控制路由
 */
export function createSystemRoutes(
  taskManager: TaskManager,
  io: SocketIOServer
): Router {
  const router = Router();

  /**
   * GET /system/status - 获取系统状态
   */
  router.get('/status', async (req, res) => {
    try {
      const stats = taskManager.getCrawlerStats();
      const tasks = taskManager.getAllTasks();
      
      const systemStatus: SystemStatus = {
        status: 'running', // TaskManager总是运行状态
        uptime: process.uptime(),
        stats: {
          totalTasks: tasks.length,
          activeTasks: tasks.filter(t => t.status === 'running').length,
          totalUrls: stats.totalUrls || 0,
          processedUrls: stats.processedUrls || 0,
        },
        modules: {
          crawler: true, // TaskManager包含爬虫功能
          storage: true, // TaskManager包含存储功能
          ai: true, // TaskManager包含AI功能
        },
        timestamp: new Date().toISOString(),
      };

      res.json({
        success: true,
        data: systemStatus,
      });
    } catch (err) {
      error('获取系统状态失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get system status',
      });
    }
  });

  /**
   * GET /system/health - 系统健康检查
   */
  router.get('/health', async (req, res) => {
    try {
      const tasks = taskManager.getAllTasks();
      const stats = taskManager.getCrawlerStats();
      const memoryUsage = process.memoryUsage();
      const memoryLimit = 1024 * 1024 * 1024; // 1GB
      
      // 计算系统健康状态
      const isMemoryHealthy = memoryUsage.heapUsed < memoryLimit;
      const isUptimeHealthy = process.uptime() > 0;
      const hasActiveTasks = tasks.length > 0;
      
      let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      
      if (!isMemoryHealthy || !isUptimeHealthy) {
        overallStatus = 'unhealthy';
      } else if (!hasActiveTasks) {
        overallStatus = 'degraded';
      }
      
      const healthResult = {
        status: overallStatus,
        timestamp: new Date(),
        services: {
          application: {
            status: 'healthy' as const,
            message: 'Application is running normally',
            responseTime: 0,
          },
          memory: {
            status: isMemoryHealthy ? 'healthy' as const : 'unhealthy' as const,
            message: `Memory usage: ${(memoryUsage.heapUsed / memoryLimit * 100).toFixed(1)}%`,
            responseTime: 0,
          },
          uptime: {
            status: isUptimeHealthy ? 'healthy' as const : 'unhealthy' as const,
            message: `Uptime: ${process.uptime().toFixed(0)} seconds`,
            responseTime: 0,
          },
          tasks: {
            status: hasActiveTasks ? 'healthy' as const : 'unhealthy' as const,
            message: `Active tasks: ${tasks.filter(t => t.status === 'running').length}/${tasks.length}`,
            responseTime: 0,
          },
        },
      };

      const statusCode = overallStatus === 'healthy' ? 200 : 
                        overallStatus === 'degraded' ? 200 : 503;
      
      res.status(statusCode).json(healthResult);
    } catch (err) {
      error('健康检查失败:', err);
      res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date(),
        services: {
          system: {
            status: 'unhealthy',
            message: err instanceof Error ? err.message : 'Health check failed',
            responseTime: 0,
          },
        },
      });
    }
  });

  /**
   * POST /system/pause - 暂停系统
   */
  router.post('/pause', async (req, res) => {
    try {
      const runningTasks = taskManager.getAllTasks().filter(t => t.status === 'running');
      
      // 暂停所有运行中的任务
      for (const task of runningTasks) {
        await taskManager.pauseTask(task.id);
      }
      
      // 通过WebSocket推送系统暂停事件
      io.emit('system-paused', {
        timestamp: new Date().toISOString(),
        pausedTasks: runningTasks.length,
        message: 'System paused successfully',
      });

      res.json({
        success: true,
        message: 'System paused successfully',
        pausedTasks: runningTasks.length,
        timestamp: new Date().toISOString(),
      });
    } catch (err) {
      error('系统暂停失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to pause system',
      });
    }
  });

  /**
   * POST /system/resume - 恢复系统
   */
  router.post('/resume', async (req, res) => {
    try {
      const pausedTasks = taskManager.getAllTasks().filter(t => t.status === 'paused');
      
      // 恢复所有暂停的任务
      for (const task of pausedTasks) {
        await taskManager.resumeTask(task.id);
      }
      
      // 通过WebSocket推送系统恢复事件
      io.emit('system-resumed', {
        timestamp: new Date().toISOString(),
        resumedTasks: pausedTasks.length,
        message: 'System resumed successfully',
      });

      res.json({
        success: true,
        message: 'System resumed successfully',
        resumedTasks: pausedTasks.length,
        timestamp: new Date().toISOString(),
      });
    } catch (err) {
      error('系统恢复失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to resume system',
      });
    }
  });

  /**
   * POST /system/restart - 重启系统 (在新架构中不再需要)
   */
  router.post('/restart', async (req, res) => {
    try {
      // 新架构不需要重启，TaskManager是持续运行的
      // 只需要停止所有运行中的任务，然后通知前端
      const runningTasks = taskManager.getAllTasks().filter(t => t.status === 'running');
      
      for (const task of runningTasks) {
        await taskManager.stopTask(task.id);
      }
      
      // 通过WebSocket推送系统重启事件
      io.emit('system_restarted', {
        timestamp: new Date().toISOString(),
        message: 'All tasks stopped and system refreshed',
      });

      res.json({
        success: true,
        message: 'System refreshed successfully - all tasks stopped',
        timestamp: new Date().toISOString(),
      });
    } catch (err) {
      error('系统重启失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to restart system',
      });
    }
  });

  /**
   * GET /system/config - 获取系统配置
   */
  router.get('/config', async (req, res) => {
    try {
      // 返回TaskManager的默认配置信息
      const safeConfig = {
        crawling: {
          maxConcurrency: 3,
          requestDelay: 1000,
          timeout: 30000,
        },
        storage: {
          type: 'memory',
        },
        ai: {
          enabled: true,
          provider: 'openai',
          model: 'gpt-3.5-turbo',
        },
        monitoring: {
          enabled: true,
          interval: 5000,
        },
      };

      res.json({
        success: true,
        data: safeConfig,
      });
    } catch (err) {
      error('获取配置失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get system config',
      });
    }
  });

  return router;
}
