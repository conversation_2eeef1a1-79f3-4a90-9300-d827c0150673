# 多阶段构建 Dockerfile for Nodex
FROM node:18-alpine AS frontend-builder

# 设置工作目录
WORKDIR /app/frontend

# 复制前端 package 文件
COPY src/frontend/package*.json ./

# 安装前端依赖
RUN npm ci && npm cache clean --force

# 复制前端源代码
COPY src/frontend ./

# 构建前端
RUN npm run build

# 后端构建阶段
FROM node:18-alpine AS backend-builder

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装依赖
RUN npm ci && npm cache clean --force

# 复制源代码（排除前端）
COPY src ./src
COPY tsconfig.json ./
COPY nodex.config.json ./

# 构建后端
RUN npm run build:backend

# 生产环境镜像
FROM node:18-alpine AS production

# 安装必要的系统依赖
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodex -u 1001

# 设置工作目录
WORKDIR /app

# 复制构建产物和依赖
COPY --from=backend-builder --chown=nodex:nodejs /app/dist ./dist
COPY --from=backend-builder --chown=nodex:nodejs /app/node_modules ./node_modules
COPY --from=backend-builder --chown=nodex:nodejs /app/package*.json ./
COPY --from=frontend-builder --chown=nodex:nodejs /app/frontend/dist ./frontend/dist

# 设置 Puppeteer 环境变量
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# 切换到非 root 用户
USER nodex

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "console.log('Health check')" || exit 1

# 启动 Nodex API 服务器
CMD ["node", "dist/index.js"]
