/**
 * 任务管理器
 *
 * 负责管理多个爬虫任务的生命周期，每个任务独立运行
 */

import { EventEmitter } from 'events';
import {
  Task,
  TaskConfig,
  TaskStatus,
  TaskType,
  CreateTaskRequest,
  TaskOperationResult,
  CrawlerStats,
} from '@/types/task';
import { getLogger } from '@/utils';
import { CrawlerManager } from '@/modules/crawler';
import { FetcherManager } from '@/modules/fetcher';
import { ParserManager } from '@/modules/parser';
import { StorageManager } from '@/modules/storage';
import { AIEngine } from '@/modules/ai';
import { NodexConfig } from '@/interfaces/config';
import { TraversalStrategy } from '@/shared-types';

/**
 * 单个爬虫任务实例
 */
class CrawlerTask extends EventEmitter {
  private task: Task;
  private logger = getLogger('CrawlerTask');
  private crawlerManager: CrawlerManager | null = null;
  private fetcherManager: FetcherManager | null = null;
  private parserManager: ParserManager | null = null;
  private storageManager: StorageManager | null = null;
  private aiEngine: AIEngine | null = null;
  private statsUpdateInterval: NodeJS.Timeout | null = null;

  constructor(
    config: CreateTaskRequest,
    dependencies: {
      fetcherManager: FetcherManager;
      parserManager: ParserManager;
      storageManager: StorageManager;
      aiEngine?: AIEngine;
    }
  ) {
    super();

    const taskId = `task-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    // 保存依赖
    this.fetcherManager = dependencies.fetcherManager;
    this.parserManager = dependencies.parserManager;
    this.storageManager = dependencies.storageManager;
    this.aiEngine = dependencies.aiEngine || null;

    // 创建任务对象
    this.task = {
      id: taskId,
      name: config.name,
      type: config.type || TaskType.HTML_CRAWLER,
      status: TaskStatus.PENDING,
      urls: config.urls,
      config: this.normalizeConfig(config.config || {}),
      stats: {
        processed: 0,
        total: config.urls.length,
        errors: 0,
        inQueue: config.urls.length,
        success: 0,
        failed: 0,
        // Legacy properties for compatibility
        totalUrls: config.urls.length,
        processedUrls: 0,
        failedUrls: 0,
        queueSize: config.urls.length,
        successRate: 0,
        averageProcessingTime: 0,
        extractedItems: 0,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * 标准化任务配置
   */
  private normalizeConfig(config: Partial<TaskConfig>): TaskConfig {
    return {
      name: config.name || this.task?.name || 'Unnamed Task',
      description: config.description,
      type: config.type || TaskType.HTML_CRAWLER,
      urls: config.urls || this.task?.urls || [],
      startUrls: config.startUrls,
      maxDepth: config.maxDepth || 3,
      maxPages: config.maxPages,
      concurrency: config.concurrency,
      maxConcurrency: config.maxConcurrency || 5,
      delay: config.delay,
      delayMs: config.delayMs || 1000,
      timeout: config.timeout,
      retries: config.retries,
      useProxy: config.useProxy,
      enableProxy: config.enableProxy || false,
      enableAI: config.enableAI ?? true,
      useBrowser: config.useBrowser ?? true,
      respectRobots: config.respectRobots,
      respectRobotsTxt: config.respectRobotsTxt,
      userAgent: config.userAgent || 'Nodex-Crawler/2.0',
      customHeaders: config.customHeaders || {},
      allowedDomains: config.allowedDomains || [],
      useReadability: config.useReadability ?? true,
      includeRawHtml: config.includeRawHtml ?? true,
      selectors: config.selectors,
      headers: config.headers,
      cookies: config.cookies || '',
      proxy: config.proxy,
      storage: config.storage,
      schedule: config.schedule,
      notifications: config.notifications,
    };
  }

  /**
   * 创建NodexConfig用于爬虫管理器
   */
  private createNodexConfig(): NodexConfig {
    const taskConfig = this.task.config;

    return {
      task: {
        seedUrls: this.task.urls,
        traversalStrategy: TraversalStrategy.BFS,
        name: taskConfig.name,
        description: `Crawler task: ${taskConfig.name}`,
        maxDepth: taskConfig.maxDepth || 3,
        maxConcurrency: taskConfig.maxConcurrency || 5,
        delayMs: taskConfig.delayMs || 1000,
        enableProxy: taskConfig.enableProxy || false,
        enableAI: taskConfig.enableAI ?? true,
        useBrowser: taskConfig.useBrowser ?? true,
        allowedDomains: taskConfig.allowedDomains || [],
        useReadability: taskConfig.useReadability ?? true,
        includeRawHtml: taskConfig.includeRawHtml ?? true,
      },
      crawlScope: {
        allowedDomains: taskConfig.allowedDomains || [],
        disallowedPatterns: [],
        followSubdomains: true,
        ignoreWWW: true,
        allowedExtensions: ['html', 'htm', 'php', 'asp', 'aspx', 'jsp'],
        maxFileSize: 50 * 1024 * 1024, // 50MB
      },
      depthControl: {
        global: taskConfig.maxDepth || 3,
      },
      concurrency: {
        maxConcurrentRequests: taskConfig.maxConcurrency || 5,
        delayBetweenRequestsMs: taskConfig.delayMs || 1000,
        requestTimeoutMs: 30000,
        maxRetries: 3,
        retryDelayMs: 5000,
      },
      proxy: {
        enabled: taskConfig.enableProxy || false,
        proxies: [],
        strategy: 'round-robin' as any,
        healthCheck: {
          enabled: false,
          intervalMs: 60000,
          testUrl: 'https://httpbin.org/ip',
        },
      },
      storage: {
        type: 'memory' as any,
        batchSize: 100,
        writeIntervalMs: 5000,
      },
      queue: {
        redis: {
          host: 'localhost',
          port: 6379,
        },
      },
      ai: {
        enabled: taskConfig.enableAI ?? true,
        defaultProvider: 'openai' as any,
      },
      logging: {
        level: 'info' as any,
        console: true,
      },
      monitoring: {
        enabled: true,
        statsIntervalMs: 10000,
      },
      userAgent: taskConfig.userAgent || 'Nodex-Crawler/2.0',
      respectRobotsTxt: true,
      requestTimeoutMs: 30000,
      customHeaders: taskConfig.customHeaders || {},
      cookies: taskConfig.cookies || '',
    };
  }

  /**
   * 启动任务
   */
  async start(): Promise<void> {
    if (this.task.status === TaskStatus.RUNNING) {
      throw new Error('Task is already running');
    }

    try {
      this.logger.info(`启动任务: ${this.task.id}`);

      // 验证依赖
      if (!this.fetcherManager || !this.parserManager || !this.storageManager) {
        throw new Error('Required managers not initialized');
      }

      // 创建爬虫管理器配置
      const config = this.createNodexConfig();

      // 初始化爬虫管理器
      this.crawlerManager = new CrawlerManager(
        this.fetcherManager,
        this.parserManager,
        this.storageManager,
        config,
        this.task.id
      );

      // 设置爬虫事件监听
      this.setupCrawlerEvents();

      // 更新任务状态
      this.task.status = TaskStatus.RUNNING;
      this.task.startedAt = new Date().toISOString();

      this.emit('statusChanged', this.task);

      // 启动爬虫
      await this.crawlerManager.start();

      // 开始统计更新
      this.startStatsUpdate();

      this.logger.info(`任务启动成功: ${this.task.id}`);
    } catch (error) {
      this.task.status = TaskStatus.FAILED;
      this.task.error = error instanceof Error ? error.message : String(error);
      this.emit('statusChanged', this.task);
      this.logger.error(`任务启动失败: ${this.task.id}`, error);
      throw error;
    }
  }

  /**
   * 设置爬虫事件监听
   */
  private setupCrawlerEvents(): void {
    if (!this.crawlerManager) {
      return;
    }

    // 监听URL处理事件
    this.crawlerManager.on('urlProcessed', data => {
      this.updateStatsFromCrawler();
      this.emit('urlProcessed', {
        taskId: this.task.id,
        ...data,
      });
    });

    // 监听数据提取事件
    this.crawlerManager.on('dataExtracted', data => {
      this.updateStatsFromCrawler();
      this.emit('dataExtracted', {
        taskId: this.task.id,
        ...data,
      });
    });

    // 监听错误事件
    this.crawlerManager.on('urlFailed', data => {
      this.updateStatsFromCrawler();
      this.emit('error', {
        taskId: this.task.id,
        error: data.error,
        url: data.url,
      });
    });

    // 监听完成事件
    this.crawlerManager.on('completed', () => {
      this.task.status = TaskStatus.COMPLETED;
      this.task.completedAt = new Date().toISOString();
      this.stopStatsUpdate();
      this.emit('statusChanged', this.task);
      this.emit('completed', this.task);
      this.logger.info(`任务完成: ${this.task.id}`);
    });

    // 监听爬虫错误事件
    this.crawlerManager.on('error', error => {
      this.task.status = TaskStatus.FAILED;
      this.task.error = error instanceof Error ? error.message : String(error);
      this.stopStatsUpdate();
      this.emit('statusChanged', this.task);
      this.logger.error(`任务失败: ${this.task.id}`, error);
    });
  }

  /**
   * 从爬虫管理器更新统计信息
   */
  private updateStatsFromCrawler(): void {
    if (!this.crawlerManager) {
      return;
    }

    const crawlerStats = this.crawlerManager.getStats();

    this.task.stats = {
      processed: crawlerStats.processedUrls,
      total: crawlerStats.totalUrls,
      errors: crawlerStats.failedUrls,
      inQueue: crawlerStats.queueSize,
      success: crawlerStats.processedUrls,
      failed: crawlerStats.failedUrls,
      // Legacy properties for compatibility
      totalUrls: crawlerStats.totalUrls,
      processedUrls: crawlerStats.processedUrls,
      failedUrls: crawlerStats.failedUrls,
      queueSize: crawlerStats.queueSize,
      successRate:
        crawlerStats.totalUrls > 0
          ? (crawlerStats.processedUrls / crawlerStats.totalUrls) * 100
          : 0,
      averageProcessingTime: 0,
      extractedItems: (crawlerStats as any).extractedItems || 0,
    };

    this.emit('statsUpdated', this.task);
  }

  /**
   * 开始统计更新
   */
  private startStatsUpdate(): void {
    if (this.statsUpdateInterval) {
      clearInterval(this.statsUpdateInterval);
    }

    this.statsUpdateInterval = setInterval(() => {
      if (this.task.status === TaskStatus.RUNNING) {
        this.updateStatsFromCrawler();
      }
    }, 5000); // 每5秒更新统计
  }

  /**
   * 停止统计更新
   */
  private stopStatsUpdate(): void {
    if (this.statsUpdateInterval) {
      clearInterval(this.statsUpdateInterval);
      this.statsUpdateInterval = null;
    }
  }

  /**
   * 停止任务
   */
  async stop(): Promise<void> {
    if (this.task.status !== TaskStatus.RUNNING && this.task.status !== TaskStatus.PAUSED) {
      throw new Error('Task is not running or paused');
    }

    try {
      this.logger.info(`停止任务: ${this.task.id}`);

      // 停止爬虫管理器
      if (this.crawlerManager) {
        await this.crawlerManager.stop();
      }

      // 停止统计更新
      this.stopStatsUpdate();

      this.task.status = TaskStatus.STOPPED;
      this.task.stoppedAt = new Date().toISOString();

      this.emit('statusChanged', this.task);
      this.logger.info(`任务停止成功: ${this.task.id}`);
    } catch (error) {
      this.logger.error(`任务停止失败: ${this.task.id}`, error);
      throw error;
    }
  }

  /**
   * 暂停任务
   */
  async pause(): Promise<void> {
    if (this.task.status !== TaskStatus.RUNNING) {
      throw new Error('Task is not running');
    }

    try {
      this.logger.info(`暂停任务: ${this.task.id}`);

      // 暂停爬虫管理器
      if (this.crawlerManager) {
        await this.crawlerManager.pause();
      }

      this.task.status = TaskStatus.PAUSED;
      this.emit('statusChanged', this.task);
      this.logger.info(`任务暂停成功: ${this.task.id}`);
    } catch (error) {
      this.logger.error(`任务暂停失败: ${this.task.id}`, error);
      throw error;
    }
  }

  /**
   * 恢复任务
   */
  async resume(): Promise<void> {
    if (this.task.status !== TaskStatus.PAUSED) {
      throw new Error('Task is not paused');
    }

    try {
      this.logger.info(`恢复任务: ${this.task.id}`);

      // 恢复爬虫管理器
      if (this.crawlerManager) {
        await this.crawlerManager.resume();
      }

      this.task.status = TaskStatus.RUNNING;
      this.emit('statusChanged', this.task);
      this.logger.info(`任务恢复成功: ${this.task.id}`);

      // 重新开始统计更新
      this.startStatsUpdate();
    } catch (error) {
      this.logger.error(`任务恢复失败: ${this.task.id}`, error);
      throw error;
    }
  }

  /**
   * 获取任务信息
   */
  getTask(): Task {
    return { ...this.task };
  }

  /**
   * 获取任务ID
   */
  getId(): string {
    return this.task.id;
  }

  /**
   * 获取任务状态
   */
  getStatus(): TaskStatus {
    return this.task.status;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    try {
      // 停止统计更新
      this.stopStatsUpdate();

      // 清理爬虫管理器
      if (this.crawlerManager) {
        try {
          await this.crawlerManager.stop();
        } catch (error) {
          this.logger.warn(`停止爬虫管理器时出错: ${error}`);
        }
        this.crawlerManager = null;
      }

      if (this.task.status === TaskStatus.RUNNING || this.task.status === TaskStatus.PAUSED) {
        this.task.status = TaskStatus.STOPPED;
        this.task.stoppedAt = new Date().toISOString();
      }

      this.removeAllListeners();
      this.logger.info(`任务资源清理完成: ${this.task.id}`);
    } catch (error) {
      this.logger.error(`清理任务资源失败: ${this.task.id}`, error);
    }
  }
}

/**
 * 任务管理器依赖接口
 */
export interface TaskManagerDependencies {
  fetcherManager: FetcherManager;
  parserManager: ParserManager;
  storageManager: StorageManager;
  aiEngine?: AIEngine;
}

/**
 * 任务管理器
 */
export class TaskManager extends EventEmitter {
  private tasks: Map<string, CrawlerTask> = new Map();
  private logger = getLogger('TaskManager');
  private dependencies: TaskManagerDependencies;

  constructor(dependencies: TaskManagerDependencies) {
    super();
    this.dependencies = dependencies;
    this.logger.info('TaskManager 初始化完成');
  }

  /**
   * 创建新任务
   */
  async createTask(config: CreateTaskRequest): Promise<string> {
    try {
      // 验证配置
      await this.validateTaskConfig(config);

      const task = new CrawlerTask(config, this.dependencies);
      const taskId = task.getId();

      // 设置事件监听
      this.setupTaskEvents(task);

      // 添加到任务列表
      this.tasks.set(taskId, task);

      // 立即发出任务创建事件
      this.emit('taskCreated', task.getTask());
      this.logger.info(`任务创建成功: ${taskId}`);

      // 异步启动任务（不阻塞响应）
      setImmediate(async () => {
        try {
          await task.start();
          this.logger.info(`任务启动成功: ${taskId}`);
        } catch (startError) {
          this.logger.error(`任务启动失败: ${taskId}`, startError);
          // 更新任务状态为失败
          const taskData = task.getTask();
          taskData.status = TaskStatus.FAILED;
          this.emit('taskStatusChanged', taskData);
        }
      });

      return taskId;
    } catch (error) {
      this.logger.error('创建任务失败', error);
      throw error;
    }
  }

  /**
   * 获取任务
   */
  getTask(taskId: string): Task | null {
    const task = this.tasks.get(taskId);
    return task ? task.getTask() : null;
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): Task[] {
    return Array.from(this.tasks.values()).map(task => task.getTask());
  }

  /**
   * 获取依赖项
   */
  public getDependencies(): TaskManagerDependencies {
    return this.dependencies;
  }

  /**
   * 启动任务
   */
  async startTask(taskId: string): Promise<TaskOperationResult> {
    const task = this.tasks.get(taskId);
    if (!task) {
      return { success: false, taskId, error: 'Task not found' };
    }

    try {
      await task.start();
      return { success: true, taskId, message: 'Task started successfully' };
    } catch (error) {
      return {
        success: false,
        taskId,
        error: error instanceof Error ? error.message : 'Failed to start task',
      };
    }
  }

  /**
   * 停止任务
   */
  async stopTask(taskId: string): Promise<TaskOperationResult> {
    const task = this.tasks.get(taskId);
    if (!task) {
      return { success: false, taskId, error: 'Task not found' };
    }

    try {
      await task.stop();
      return { success: true, taskId, message: 'Task stopped successfully' };
    } catch (error) {
      return {
        success: false,
        taskId,
        error: error instanceof Error ? error.message : 'Failed to stop task',
      };
    }
  }

  /**
   * 暂停任务
   */
  async pauseTask(taskId: string): Promise<TaskOperationResult> {
    const task = this.tasks.get(taskId);
    if (!task) {
      return { success: false, taskId, error: 'Task not found' };
    }

    try {
      await task.pause();
      return { success: true, taskId, message: 'Task paused successfully' };
    } catch (error) {
      return {
        success: false,
        taskId,
        error: error instanceof Error ? error.message : 'Failed to pause task',
      };
    }
  }

  /**
   * 恢复任务
   */
  async resumeTask(taskId: string): Promise<TaskOperationResult> {
    const task = this.tasks.get(taskId);
    if (!task) {
      return { success: false, taskId, error: 'Task not found' };
    }

    try {
      await task.resume();
      return { success: true, taskId, message: 'Task resumed successfully' };
    } catch (error) {
      return {
        success: false,
        taskId,
        error: error instanceof Error ? error.message : 'Failed to resume task',
      };
    }
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId: string): Promise<TaskOperationResult> {
    const task = this.tasks.get(taskId);
    if (!task) {
      return { success: false, taskId, error: 'Task not found' };
    }

    const taskStatus = task.getStatus();
    if (taskStatus === TaskStatus.RUNNING || taskStatus === TaskStatus.PAUSED) {
      return {
        success: false,
        taskId,
        error: 'Cannot delete running or paused task. Please stop the task first.',
      };
    }

    try {
      await task.cleanup();
      this.tasks.delete(taskId);
      this.emit('taskDeleted', { taskId });
      return { success: true, taskId, message: 'Task deleted successfully' };
    } catch (error) {
      return {
        success: false,
        taskId,
        error: error instanceof Error ? error.message : 'Failed to delete task',
      };
    }
  }

  /**
   * 获取爬虫统计
   */
  getCrawlerStats(): CrawlerStats {
    const tasks = this.getAllTasks();

    const stats: CrawlerStats = {
      totalTasks: tasks.length,
      runningTasks: tasks.filter(t => t.status === TaskStatus.RUNNING).length,
      pausedTasks: tasks.filter(t => t.status === TaskStatus.PAUSED).length,
      completedTasks: tasks.filter(t => t.status === TaskStatus.COMPLETED).length,
      failedTasks: tasks.filter(t => t.status === TaskStatus.FAILED).length,
      stoppedTasks: tasks.filter(t => t.status === TaskStatus.STOPPED).length,
      totalUrls: tasks.reduce((sum, t) => sum + t.stats.total, 0),
      processedUrls: tasks.reduce((sum, t) => sum + t.stats.processed, 0),
      failedUrls: tasks.reduce((sum, t) => sum + t.stats.errors, 0),
      queuedUrls: tasks.reduce((sum, t) => sum + (t.stats.inQueue || 0), 0),
      queueSize: tasks.reduce((sum, t) => sum + (t.stats.inQueue || 0), 0),
      errorUrls: tasks.reduce((sum, t) => sum + t.stats.errors, 0),
      extractedItems: tasks.reduce((sum, t) => sum + (t.stats.extractedItems || 0), 0),
      successRate: 0,
      averageSpeed: 0,
    };

    // 计算成功率
    const totalProcessed = stats.processedUrls + stats.errorUrls;
    if (totalProcessed > 0) {
      stats.successRate = (stats.processedUrls / totalProcessed) * 100;
    }

    // 计算平均速度（简单估算）
    const runningTasks = tasks.filter(t => t.status === TaskStatus.RUNNING);
    if (runningTasks.length > 0) {
      const totalRuntime = runningTasks.reduce((sum, task) => {
        if (task.startedAt) {
          return sum + (Date.now() - new Date(task.startedAt).getTime());
        }
        return sum;
      }, 0);
      const avgRuntimeMinutes = totalRuntime / (1000 * 60 * runningTasks.length);
      if (avgRuntimeMinutes > 0) {
        stats.averageSpeed = stats.processedUrls / avgRuntimeMinutes;
      }
    }

    return stats;
  }

  /**
   * 验证任务配置
   */
  private async validateTaskConfig(config: CreateTaskRequest): Promise<void> {
    // 基础验证
    if (!config.name || config.name.trim() === '') {
      throw new Error('Task name is required and cannot be empty');
    }

    if (config.name.length > 100) {
      throw new Error('Task name cannot exceed 100 characters');
    }

    if (!config.urls || !Array.isArray(config.urls) || config.urls.length === 0) {
      throw new Error('URLs are required and must be a non-empty array');
    }

    if (config.urls.length > 1000) {
      throw new Error('Cannot process more than 1000 URLs in a single task');
    }

    // 验证URL格式和可达性
    const invalidUrls: string[] = [];
    const unreachableUrls: string[] = [];

    for (const url of config.urls) {
      try {
        const parsedUrl = new URL(url);

        // 验证协议
        if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
          invalidUrls.push(`${url} (unsupported protocol: ${parsedUrl.protocol})`);
          continue;
        }

        // 验证域名不为空
        if (!parsedUrl.hostname) {
          invalidUrls.push(`${url} (missing hostname)`);
          continue;
        }

        // 可选：检查URL可达性（仅对前几个URL进行快速检查）
        // 注意：为了避免阻塞任务创建，这里使用更快的超时时间
        if (config.urls.indexOf(url) < 3) {
          try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

            const response = await fetch(url, {
              method: 'HEAD',
              signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (!response.ok && response.status >= 400) {
              unreachableUrls.push(`${url} (HTTP ${response.status})`);
            }
          } catch (error) {
            // 网络错误不阻止任务创建，只记录警告
            this.logger.warn(`URL 可达性检查失败: ${url}`, error);
          }
        }
      } catch (error) {
        invalidUrls.push(`${url} (invalid format)`);
      }
    }

    if (invalidUrls.length > 0) {
      throw new Error(`Invalid URLs found: ${invalidUrls.join(', ')}`);
    }

    if (unreachableUrls.length > 0) {
      this.logger.warn(`Some URLs may be unreachable: ${unreachableUrls.join(', ')}`);
    }

    // 验证任务配置
    if (config.config) {
      const taskConfig = config.config;

      if (taskConfig.maxDepth !== undefined) {
        if (taskConfig.maxDepth < 1 || taskConfig.maxDepth > 10) {
          throw new Error('maxDepth must be between 1 and 10');
        }
      }

      if (taskConfig.maxConcurrency !== undefined) {
        if (taskConfig.maxConcurrency < 1 || taskConfig.maxConcurrency > 20) {
          throw new Error('maxConcurrency must be between 1 and 20');
        }
      }

      if (taskConfig.delayMs !== undefined) {
        if (taskConfig.delayMs < 100 || taskConfig.delayMs > 60000) {
          throw new Error('delayMs must be between 100 and 60000 milliseconds');
        }
      }

      if (taskConfig.allowedDomains && taskConfig.allowedDomains.length > 0) {
        // 验证域名格式
        const invalidDomains = taskConfig.allowedDomains.filter(domain => {
          try {
            // 简单的域名格式验证
            return (
              !/^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$/.test(domain) &&
              !/^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$/.test(
                domain
              )
            );
          } catch {
            return true;
          }
        });

        if (invalidDomains.length > 0) {
          throw new Error(`Invalid domain names: ${invalidDomains.join(', ')}`);
        }
      }

      // 验证Readability配置
      if (
        taskConfig.useReadability !== undefined &&
        typeof taskConfig.useReadability !== 'boolean'
      ) {
        throw new Error('useReadability must be a boolean value');
      }

      if (
        taskConfig.includeRawHtml !== undefined &&
        typeof taskConfig.includeRawHtml !== 'boolean'
      ) {
        throw new Error('includeRawHtml must be a boolean value');
      }
    }

    // 验证系统资源
    if (this.tasks.size >= 50) {
      throw new Error('Maximum number of concurrent tasks (50) reached');
    }

    // 检查是否有同名任务正在运行
    const existingTask = Array.from(this.tasks.values()).find(
      task =>
        task.getTask().name === config.name &&
        [TaskStatus.RUNNING, TaskStatus.PAUSED].includes(task.getTask().status)
    );

    if (existingTask) {
      throw new Error(`A task with name '${config.name}' is already running or paused`);
    }
  }

  /**
   * 设置任务事件监听
   */
  private setupTaskEvents(task: CrawlerTask): void {
    task.on('statusChanged', (taskData: Task) => {
      this.emit('taskStatusChanged', taskData);
    });

    task.on('statsUpdated', (taskData: Task) => {
      this.emit('taskStatsUpdated', taskData);
    });

    task.on('urlProcessed', (data: any) => {
      this.emit('urlProcessed', data);
    });

    task.on('dataExtracted', (data: any) => {
      this.emit('dataExtracted', data);
    });

    task.on('error', (data: any) => {
      this.emit('taskError', data);
    });

    task.on('completed', (taskData: Task) => {
      this.emit('taskCompleted', taskData);
    });
  }

  /**
   * 获取任务的抓取结果
   */
  async getTaskResults(taskId: string): Promise<any[]> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error('Task not found');
    }

    try {
      // 从存储管理器获取结果
      const results = await this.dependencies.storageManager.getTaskResults(taskId);
      return results || [];
    } catch (error) {
      this.logger.error(`获取任务结果失败: ${taskId}`, error);
      return [];
    }
  }

  /**
   * 清理所有任务
   */
  async cleanup(): Promise<void> {
    this.logger.info('清理所有任务...');

    const cleanupPromises = Array.from(this.tasks.values()).map(async task => {
      try {
        await task.cleanup();
      } catch (error) {
        this.logger.error(`清理任务失败: ${task.getId()}`, error);
      }
    });

    await Promise.all(cleanupPromises);
    this.tasks.clear();
    this.removeAllListeners();

    this.logger.info('所有任务清理完成');
  }
}
