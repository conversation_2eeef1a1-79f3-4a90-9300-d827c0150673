/**
 * 结果查询路由
 * 
 * 提供爬虫结果数据的查询、导出等API端点
 */

import { Router } from 'express';
import { TaskManager } from '@/app/task-manager';
import { error } from '@/utils';

/**
 * 结果查询参数接口
 */
export interface ResultQuery {
  taskId?: string;
  limit?: number;
  offset?: number;
  format?: 'json' | 'csv' | 'xml';
  fields?: string[];
  filter?: Record<string, any>;
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  };
}

/**
 * 结果数据接口
 */
export interface ResultData {
  id: string;
  taskId: string;
  url: string;
  extractedAt: Date;
  data: Record<string, any>;
  metadata?: {
    depth: number;
    responseTime: number;
    statusCode: number;
    contentType: string;
  };
}

/**
 * 创建结果查询路由
 */
export function createResultRoutes(
  taskManager: TaskManager,
  activeTasks: Map<string, any>
): Router {
  const router = Router();

  /**
   * GET /results/:taskId - 获取特定任务的爬取结果
   */
  router.get('/:taskId', async (req, res) => {
    try {
      const { taskId } = req.params;
      const { 
        limit = 10, 
        offset = 0, 
        format = 'json',
        fields,
        sort 
      } = req.query as any;

      // 验证任务是否存在
      const task = activeTasks.get(taskId);
      if (!task) {
        return res.status(404).json({
          success: false,
          error: 'Task not found',
        });
      }

      // 使用TaskManager的依赖获取存储管理器
      const dependencies = taskManager.getDependencies();
      const storageManager = dependencies.storageManager;

      if (!storageManager) {
        return res.status(503).json({
          success: false,
          error: 'Storage manager not available',
        });
      }

      // 构建查询条件
      const query: any = { taskId };
      
      // 字段过滤
      let selectedFields: string[] | undefined;
      if (fields && typeof fields === 'string') {
        selectedFields = fields.split(',').map(f => f.trim());
      }

      // 排序
      let sortOptions: any = { extractedAt: -1 }; // 默认按提取时间倒序
      if (sort && typeof sort === 'string') {
        try {
          const sortObj = JSON.parse(sort);
          if (sortObj.field && sortObj.order) {
            sortOptions = { [sortObj.field]: sortObj.order === 'desc' ? -1 : 1 };
          }
        } catch (error) {
          // 忽略无效的排序参数
        }
      }

      // 模拟数据获取（实际实现需要根据存储管理器的API调整）
      const mockResults: ResultData[] = [
        {
          id: '1',
          taskId,
          url: 'https://example.com/page1',
          extractedAt: new Date(),
          data: {
            title: 'Sample Page 1',
            content: 'This is sample content from page 1',
            price: '$99.99',
          },
          metadata: {
            depth: 1,
            responseTime: 1200,
            statusCode: 200,
            contentType: 'text/html',
          },
        },
        {
          id: '2',
          taskId,
          url: 'https://example.com/page2',
          extractedAt: new Date(),
          data: {
            title: 'Sample Page 2',
            content: 'This is sample content from page 2',
            price: '$149.99',
          },
          metadata: {
            depth: 1,
            responseTime: 980,
            statusCode: 200,
            contentType: 'text/html',
          },
        },
      ];

      // 应用分页
      const total = mockResults.length;
      const paginatedResults = mockResults.slice(
        Number(offset), 
        Number(offset) + Number(limit)
      );

      // 字段过滤
      let filteredResults = paginatedResults;
      if (selectedFields) {
        filteredResults = paginatedResults.map(result => {
          const filtered: any = { id: result.id, taskId: result.taskId, url: result.url };
          selectedFields.forEach(field => {
            if (result.data[field] !== undefined) {
              filtered[field] = result.data[field];
            }
          });
          return filtered;
        });
      }

      // 根据格式返回数据
      switch (format) {
        // case 'csv':
        //   return res.csv(filteredResults);
        
        // case 'xml':
        //   return res.xml(filteredResults);
        
        default:
          return res.json({
            taskId,
            count: filteredResults.length,
            total,
            data: filteredResults,
            pagination: {
              limit: Number(limit),
              offset: Number(offset),
              hasMore: Number(offset) + Number(limit) < total,
            },
          });
      }
    } catch (err) {
      error('获取结果数据失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get results',
      });
    }
  });

  /**
   * GET /results/:taskId/export - 导出任务结果
   */
  router.get('/:taskId/export', async (req, res) => {
    try {
      const { taskId } = req.params;
      const { format = 'json' } = req.query as any;

      // 验证任务是否存在
      const task = activeTasks.get(taskId);
      if (!task) {
        return res.status(404).json({
          success: false,
          error: 'Task not found',
        });
      }

      // 设置响应头
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${taskId}-results-${timestamp}`;

      switch (format) {
        case 'csv':
          res.setHeader('Content-Type', 'text/csv');
          res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
          break;
        
        case 'xml':
          res.setHeader('Content-Type', 'application/xml');
          res.setHeader('Content-Disposition', `attachment; filename="${filename}.xml"`);
          break;
        
        default:
          res.setHeader('Content-Type', 'application/json');
          res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
      }

      // 获取所有结果数据（这里需要实际的存储管理器实现）
      const allResults = [
        {
          id: '1',
          taskId,
          url: 'https://example.com/page1',
          title: 'Sample Page 1',
          content: 'This is sample content from page 1',
          price: '$99.99',
          extractedAt: new Date().toISOString(),
        },
        {
          id: '2',
          taskId,
          url: 'https://example.com/page2',
          title: 'Sample Page 2',
          content: 'This is sample content from page 2',
          price: '$149.99',
          extractedAt: new Date().toISOString(),
        },
      ];

      // 根据格式返回数据
      switch (format) {
        case 'csv':
          const csvHeader = Object.keys(allResults[0] as any).join(',');
          const csvRows = allResults.map(row => 
            Object.values(row).map(value => 
              typeof value === 'string' && value.includes(',') 
                ? `"${value}"` 
                : value
            ).join(',')
          );
          res.send([csvHeader, ...csvRows].join('\n'));
          break;
        
        case 'xml':
          const xmlData = `<?xml version="1.0" encoding="UTF-8"?>
<results>
${allResults.map(result => `
  <result>
${Object.entries(result).map(([key, value]) => `    <${key}>${value}</${key}>`).join('\n')}
  </result>`).join('')}
</results>`;
          res.send(xmlData);
          break;
        
        default:
          res.json({
            taskId,
            exportedAt: new Date().toISOString(),
            count: allResults.length,
            data: allResults,
          });
      }
    } catch (err) {
      error('导出结果数据失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to export results',
      });
    }
  });

  /**
   * GET /results/:taskId/stats - 获取任务结果统计信息
   */
  router.get('/:taskId/stats', async (req, res) => {
    try {
      const { taskId } = req.params;

      // 验证任务是否存在
      const task = activeTasks.get(taskId);
      if (!task) {
        return res.status(404).json({
          success: false,
          error: 'Task not found',
        });
      }

      // 模拟统计数据（实际实现需要从存储管理器获取）
      const stats = {
        taskId,
        totalItems: 150,
        uniqueUrls: 145,
        duplicateUrls: 5,
        dataFields: {
          title: 150,
          content: 148,
          price: 120,
          image: 95,
        },
        extractionRate: 0.97,
        averageItemSize: 1024,
        totalDataSize: 153600,
        timeRange: {
          firstExtracted: new Date(Date.now() - 3600000).toISOString(),
          lastExtracted: new Date().toISOString(),
        },
        statusCodes: {
          200: 145,
          404: 3,
          500: 2,
        },
        domains: {
          'example.com': 150,
        },
      };

      return res.json({
        success: true,
        stats,
      });
    } catch (err) {
      error('获取结果统计失败:', err);
      return res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to get result stats',
      });
    }
  });

  /**
   * POST /results/search - 全文搜索爬取数据
   */
  router.post('/search', async (req, res) => {
    try {
      const { query, taskId, limit = 10, offset = 0 } = req.body;

      if (!query) {
        return res.status(400).json({
          success: false,
          error: 'Search query is required',
        });
      }

      // 搜索功能暂时使用简化实现
      // 模拟搜索结果（实际实现需要集成搜索引擎）
      const searchResults = [
        {
          id: 'result-1',
          taskId: taskId || 'task-123',
          url: 'https://example.com/page1',
          title: 'Example Page 1',
          content: 'This is example content that matches the search query',
          score: 0.95,
          extractedAt: new Date().toISOString(),
        },
        {
          id: 'result-2',
          taskId: taskId || 'task-123',
          url: 'https://example.com/page2',
          title: 'Example Page 2',
          content: 'Another example with relevant content',
          score: 0.87,
          extractedAt: new Date().toISOString(),
        },
      ];

      res.json({
        success: true,
        data: {
          query,
          results: searchResults.slice(offset, offset + limit),
          total: searchResults.length,
          pagination: {
            limit,
            offset,
            hasMore: offset + limit < searchResults.length,
          },
        },
      });
    } catch (err) {
      error('搜索失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to search results',
      });
    }
  });

  /**
   * DELETE /results/:taskId - 清理任务数据
   */
  router.delete('/:taskId', async (req, res) => {
    try {
      const { taskId } = req.params;

      // 验证任务是否存在
      const task = activeTasks.get(taskId);
      if (!task) {
        return res.status(404).json({
          success: false,
          error: 'Task not found',
        });
      }

      // 使用TaskManager的依赖获取存储管理器
      const dependencies = taskManager.getDependencies();
      const storageManager = dependencies.storageManager;

      if (!storageManager) {
        return res.status(503).json({
          success: false,
          error: 'Storage manager not available',
        });
      }

      // 清理任务相关的所有数据
      // 这里应该实现实际的数据清理逻辑
      console.log(`清理任务 ${taskId} 的数据`);

      res.json({
        success: true,
        message: `Task ${taskId} data cleaned successfully`,
        taskId,
        cleanedAt: new Date().toISOString(),
      });
    } catch (err) {
      error('清理数据失败:', err);
      res.status(500).json({
        success: false,
        error: err instanceof Error ? err.message : 'Failed to clean task data',
      });
    }
  });

  return router;
}
