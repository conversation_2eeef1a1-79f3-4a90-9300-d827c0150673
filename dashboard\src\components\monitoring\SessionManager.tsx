import { Users } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';
import { StatusBadge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { formatTimestamp, formatPercentage, formatNumber } from '@/lib/utils';
import type { CrawlSession } from '@/types/monitoring';

interface SessionManagerProps {
  sessions: CrawlSession[];
  loading?: boolean;
}

interface SessionRowProps {
  session: CrawlSession;
}

function SessionRow({ session }: SessionRowProps) {
  const progress = session.stats
    ? session.stats.processedUrls / Math.max(session.stats.totalUrls, 1)
    : 0;

  const progressPercentage = Math.round(progress * 100);

  return (
    <tr className="border-b border-gray-100 hover:bg-gray-50">
      <td className="px-4 py-3">
        <div className="font-mono text-sm text-gray-600">
          {session.id.substring(0, 8)}...
        </div>
      </td>
      <td className="px-4 py-3">
        <div className="font-medium text-gray-900">{session.name}</div>
      </td>
      <td className="px-4 py-3">
        <StatusBadge status={session.status} size="sm" />
      </td>
      <td className="px-4 py-3">
        <div className="text-sm text-gray-600">
          {formatTimestamp(session.createdAt)}
        </div>
      </td>
      <td className="px-4 py-3">
        {session.stats ? (
          <div className="space-y-1">
            <div className="flex items-center justify-between text-sm">
              <span>{session.stats.processedUrls} / {session.stats.totalUrls}</span>
              <span className="text-gray-500">{progressPercentage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>
        ) : (
          <span className="text-gray-400">--</span>
        )}
      </td>
      <td className="px-4 py-3">
        {session.stats ? (
          <span className="text-sm font-medium">
            {formatPercentage(session.stats.successRate)}
          </span>
        ) : (
          <span className="text-gray-400">--</span>
        )}
      </td>
    </tr>
  );
}

export function SessionManager({ sessions, loading }: SessionManagerProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Users className="h-5 w-5 mr-2 text-indigo-500" />
            抓取会话
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!sessions || sessions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Users className="h-5 w-5 mr-2 text-indigo-500" />
            抓取会话
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">暂无会话数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 计算会话统计
  const sessionStats = sessions.reduce(
    (acc, session) => {
      acc.total++;
      acc[session.status] = (acc[session.status] || 0) + 1;
      
      if (session.stats) {
        acc.totalUrls += session.stats.totalUrls;
        acc.processedUrls += session.stats.processedUrls;
        acc.failedUrls += session.stats.failedUrls;
      }
      
      return acc;
    },
    {
      total: 0,
      running: 0,
      completed: 0,
      failed: 0,
      paused: 0,
      totalUrls: 0,
      processedUrls: 0,
      failedUrls: 0,
    }
  );

  const overallSuccessRate = sessionStats.totalUrls > 0
    ? (sessionStats.processedUrls - sessionStats.failedUrls) / sessionStats.totalUrls
    : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Users className="h-5 w-5 mr-2 text-indigo-500" />
          抓取会话
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 会话统计概览 */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {sessionStats.running}
            </div>
            <div className="text-sm text-blue-700">运行中</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {sessionStats.completed}
            </div>
            <div className="text-sm text-green-700">已完成</div>
          </div>
          <div className="bg-red-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {sessionStats.failed}
            </div>
            <div className="text-sm text-red-700">失败</div>
          </div>
          <div className="bg-yellow-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {sessionStats.paused}
            </div>
            <div className="text-sm text-yellow-700">暂停</div>
          </div>
        </div>

        {/* 总体进度 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">总体进度</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {formatNumber(sessionStats.processedUrls)}
              </div>
              <div className="text-sm text-gray-500">已处理URL</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {formatNumber(sessionStats.totalUrls)}
              </div>
              <div className="text-sm text-gray-500">总URL数</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900">
                {formatPercentage(overallSuccessRate)}
              </div>
              <div className="text-sm text-gray-500">总体成功率</div>
            </div>
          </div>
        </div>

        {/* 会话列表 */}
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  会话ID
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  名称
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建时间
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  进度
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  成功率
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sessions.map((session) => (
                <SessionRow key={session.id} session={session} />
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
