/**
 * Nodex API Express 集成示例
 * 
 * 演示如何在 Express 应用中集成 Nodex 爬虫服务
 * 
 * 安装依赖: npm install express
 * 运行方法: node examples/express-integration.js
 */

const express = require('express');

const API_BASE = 'http://localhost:3000';
const PORT = 3001;

const app = express();
app.use(express.json());

// Nodex API 客户端封装
class NodexClient {
  static async createTask(config) {
    const response = await fetch(`${API_BASE}/api/v1/tasks`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(config)
    });
    return response.json();
  }

  static async startTask(taskId) {
    const response = await fetch(`${API_BASE}/api/v1/tasks/${taskId}/start`, {
      method: 'POST'
    });
    return response.json();
  }

  static async getTask(taskId) {
    const response = await fetch(`${API_BASE}/api/v1/tasks/${taskId}`);
    return response.json();
  }

  static async getResults(taskId, limit = 20) {
    const response = await fetch(`${API_BASE}/api/v1/results/${taskId}?limit=${limit}`);
    return response.json();
  }

  static async getTasks() {
    const response = await fetch(`${API_BASE}/api/v1/tasks`);
    return response.json();
  }
}

// 路由：创建爬取任务
app.post('/crawl', async (req, res) => {
  try {
    const { urls, name, options = {} } = req.body;

    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return res.status(400).json({
        error: '请提供有效的 URL 列表'
      });
    }

    // 构建任务配置
    const taskConfig = {
      name: name || 'Express 集成任务',
      description: '通过 Express 应用创建的爬取任务',
      config: {
        startUrls: urls,
        maxDepth: options.maxDepth || 1,
        concurrency: options.concurrency || 3,
        delay: options.delay || 1000,
        parser: {
          type: options.parserType || 'cheerio',
          selectors: options.selectors || {
            title: 'title',
            content: 'body'
          }
        }
      }
    };

    // 创建任务
    const createResult = await NodexClient.createTask(taskConfig);
    const taskId = createResult.data.id;

    // 启动任务
    await NodexClient.startTask(taskId);

    res.json({
      success: true,
      message: '爬取任务已创建并启动',
      taskId: taskId,
      statusUrl: `/status/${taskId}`,
      resultsUrl: `/results/${taskId}`
    });

  } catch (error) {
    res.status(500).json({
      error: '创建爬取任务失败',
      details: error.message
    });
  }
});

// 路由：获取任务状态
app.get('/status/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    const result = await NodexClient.getTask(taskId);

    res.json({
      taskId: taskId,
      status: result.data.status,
      progress: result.data.progress || {},
      createdAt: result.data.createdAt,
      updatedAt: result.data.updatedAt
    });

  } catch (error) {
    res.status(500).json({
      error: '获取任务状态失败',
      details: error.message
    });
  }
});

// 路由：获取爬取结果
app.get('/results/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    const { limit = 20, format = 'json' } = req.query;

    const results = await NodexClient.getResults(taskId, parseInt(limit));

    if (format === 'csv') {
      // 简单的 CSV 转换
      if (results.data && results.data.length > 0) {
        const headers = ['url', 'title', 'timestamp'];
        const csvRows = [headers.join(',')];
        
        results.data.forEach(item => {
          const row = [
            item.url || '',
            (item.data?.title || '').replace(/,/g, ';'),
            item.timestamp || ''
          ];
          csvRows.push(row.join(','));
        });

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="results_${taskId}.csv"`);
        return res.send(csvRows.join('\n'));
      }
    }

    res.json({
      taskId: taskId,
      total: results.total || 0,
      count: results.data ? results.data.length : 0,
      data: results.data || []
    });

  } catch (error) {
    res.status(500).json({
      error: '获取爬取结果失败',
      details: error.message
    });
  }
});

// 路由：获取所有任务列表
app.get('/tasks', async (req, res) => {
  try {
    const result = await NodexClient.getTasks();

    res.json({
      total: result.data ? result.data.length : 0,
      tasks: result.data || []
    });

  } catch (error) {
    res.status(500).json({
      error: '获取任务列表失败',
      details: error.message
    });
  }
});

// 路由：健康检查
app.get('/health', async (req, res) => {
  try {
    const response = await fetch(`${API_BASE}/health`);
    const health = await response.json();

    res.json({
      service: 'Express Integration',
      nodexApi: health.status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.status(503).json({
      service: 'Express Integration',
      nodexApi: 'unavailable',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 首页 - API 文档
app.get('/', (req, res) => {
  res.json({
    name: 'Nodex Express 集成示例',
    version: '1.0.0',
    endpoints: {
      'POST /crawl': '创建爬取任务',
      'GET /status/:taskId': '获取任务状态',
      'GET /results/:taskId': '获取爬取结果',
      'GET /tasks': '获取所有任务',
      'GET /health': '健康检查'
    },
    example: {
      createTask: {
        method: 'POST',
        url: '/crawl',
        body: {
          urls: ['https://example.com'],
          name: '示例任务',
          options: {
            maxDepth: 2,
            concurrency: 3,
            parserType: 'cheerio',
            selectors: {
              title: 'title',
              content: '.content'
            }
          }
        }
      }
    }
  });
});

// 启动服务器
async function startServer() {
  try {
    // 检查 Nodex API 是否可用
    const healthResponse = await fetch(`${API_BASE}/health`);
    const health = await healthResponse.json();
    
    if (health.status !== 'ok') {
      throw new Error('Nodex API 服务不可用');
    }

    app.listen(PORT, () => {
      console.log('🚀 Express 集成示例服务器已启动');
      console.log('================================');
      console.log(`服务地址: http://localhost:${PORT}`);
      console.log(`Nodex API: ${API_BASE}`);
      console.log('');
      console.log('📋 可用端点:');
      console.log(`  GET  http://localhost:${PORT}/          - API 文档`);
      console.log(`  POST http://localhost:${PORT}/crawl     - 创建爬取任务`);
      console.log(`  GET  http://localhost:${PORT}/tasks     - 获取任务列表`);
      console.log(`  GET  http://localhost:${PORT}/health    - 健康检查`);
      console.log('');
      console.log('💡 示例请求:');
      console.log(`curl -X POST http://localhost:${PORT}/crawl \\`);
      console.log(`  -H "Content-Type: application/json" \\`);
      console.log(`  -d '{"urls":["https://httpbin.org/html"],"name":"测试任务"}'`);
    });

  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    console.log('\n💡 请确保 Nodex API 服务器正在运行:');
    console.log('cd nodex && npm start');
    process.exit(1);
  }
}

// 运行服务器
if (require.main === module) {
  startServer();
}
