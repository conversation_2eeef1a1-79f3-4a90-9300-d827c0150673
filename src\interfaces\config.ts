/**
 * 配置相关接口定义
 */

import { StorageType, ProxyStrategy, ProxyConfig, LogLevel } from '@/types/common';
import { ParserConfig } from '@/interfaces/parser';
import { TraversalStrategy } from '@/shared-types';

/**
 * 任务定义配置
 */
export interface TaskConfig {
  /** 种子URL列表 */
  seedUrls: string[];
  /** 遍历策略 */
  traversalStrategy: TraversalStrategy;
  /** 任务名称 */
  name?: string;
  /** 任务描述 */
  description?: string;
  /** 任务标签 */
  tags?: string[];
  /** 最大爬取深度 */
  maxDepth?: number;
  /** 最大并发数 */
  maxConcurrency?: number;
  /** 请求延迟 (毫秒) */
  delayMs?: number;
  /** 是否启用代理 */
  enableProxy?: boolean;
  /** 是否启用AI */
  enableAI?: boolean;
  /** 是否使用浏览器模拟 */
  useBrowser?: boolean;
  /** 允许的域名 */
  allowedDomains?: string[];
  /** 是否使用Readability解析 */
  useReadability?: boolean;
  /** 是否包含原始HTML */
  includeRawHtml?: boolean;
  /** 匹配模式 */
  match?: string;
  /** 解析器配置 */
  parser?: {
    type: string;
    options: any;
  };
}

/**
 * 抓取范围与策略配置
 */
export interface CrawlScopeConfig {
  /** 域名白名单 */
  allowedDomains: string[];
  /** URL路径黑名单 (正则表达式) */
  disallowedPatterns: RegExp[];
  /** 是否抓取子域名 */
  followSubdomains: boolean;
  /** 是否将 example.com 和 www.example.com 视为同一域名 */
  ignoreWWW: boolean;
  /** 允许的文件扩展名 */
  allowedExtensions?: string[];
  /** 禁止的文件扩展名 */
  disallowedExtensions?: string[];
  /** 最大文件大小 (字节) */
  maxFileSize?: number;
}

/**
 * 深度控制配置
 */
export interface DepthControlConfig {
  /** 全局最大抓取深度 */
  global: number;
  /** 特定域名的深度覆盖全局配置 */
  perDomain?: Record<string, number>;
}

/**
 * 性能与并发配置
 */
export interface ConcurrencyConfig {
  /** 全局最大并发请求数 */
  maxConcurrentRequests: number;
  /** 每个请求之间的最小延迟（毫秒） */
  delayBetweenRequestsMs: number;
  /** 特定域名的延迟 */
  domainSpecificDelay?: Record<string, number>;
  /** 请求超时时间 (毫秒) */
  requestTimeoutMs?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 重试延迟 (毫秒) */
  retryDelayMs?: number;
}

/**
 * 代理健康检查配置
 */
export interface ProxyHealthCheckConfig {
  /** 是否启用健康检查 */
  enabled: boolean;
  /** 检查间隔 (毫秒) */
  intervalMs: number;
  /** 测试URL */
  testUrl: string;
  /** 超时时间 (毫秒) */
  timeoutMs?: number;
}

/**
 * 代理配置
 */
export interface ProxyManagerConfig {
  /** 是否启用代理 */
  enabled: boolean;
  /** 代理服务器列表 */
  proxies: ProxyConfig[];
  /** 额外代理配置文件路径（JSON格式） */
  extraProxyConfig?: string;
  /** 代理切换策略 */
  strategy: ProxyStrategy;
  /** 健康检查配置 */
  healthCheck: ProxyHealthCheckConfig;
  /** 代理失败阈值 */
  failureThreshold?: number;
  /** 代理恢复时间 (毫秒) */
  recoveryTimeMs?: number;
}

/**
 * 存储配置
 */
export interface StorageConfig {
  /** 存储类型 */
  type: StorageType;
  /** 连接字符串 */
  connectionString?: string;
  /** 集合/表名 */
  collection?: string;
  /** 数据库名 */
  database?: string;
  /** 额外的存储选项 */
  options?: Record<string, any>;
  /** 批量写入大小 */
  batchSize?: number;
  /** 写入间隔 (毫秒) */
  writeIntervalMs?: number;
}

/**
 * Redis 配置
 */
export interface RedisConfig {
  /** Redis 主机 */
  host: string;
  /** Redis 端口 */
  port: number;
  /** Redis 密码 */
  password?: string;
  /** Redis 数据库编号 */
  db?: number;
  /** 连接池大小 */
  maxConnections?: number;
  /** 连接超时 (毫秒) */
  connectTimeoutMs?: number;
  /** 命令超时 (毫秒) */
  commandTimeoutMs?: number;
}

/**
 * 队列配置
 */
export interface QueueConfig {
  /** Redis 配置 */
  redis: RedisConfig;
  /** 队列名称前缀 */
  queuePrefix?: string;
  /** 默认任务选项 */
  defaultJobOptions?: {
    /** 任务延迟 (毫秒) */
    delay?: number;
    /** 任务优先级 */
    priority?: number;
    /** 重试次数 */
    attempts?: number;
    /** 退避策略 */
    backoff?: {
      type: 'fixed' | 'exponential';
      delay: number;
    };
  };
  /** 队列处理并发数 */
  concurrency?: number;
}

/**
 * AI 引擎配置
 */
export interface AIConfig {
  /** 是否启用 AI 功能 */
  enabled: boolean;
  /** 默认 AI 提供商 */
  defaultProvider: 'openai' | 'gemini' | 'local';
  /** OpenAI 配置 */
  openai?: {
    apiKey: string;
    baseUrl?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
  };
  /** Gemini 配置 */
  gemini?: {
    apiKey: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
  };
  /** 本地模型配置 */
  local?: {
    endpoint: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
  };
  /** 请求超时 (毫秒) */
  timeoutMs?: number;
  /** 最大重试次数 */
  maxRetries?: number;
}

/**
 * 日志配置
 */
export interface LogConfig {
  /** 日志级别 */
  level: LogLevel;
  /** 日志格式 */
  format?: 'json' | 'simple' | 'combined';
  /** 日志文件路径 */
  filePath?: string;
  /** 是否输出到控制台 */
  console?: boolean;
  /** 日志轮转配置 */
  rotation?: {
    /** 最大文件大小 */
    maxSize?: string;
    /** 最大文件数量 */
    maxFiles?: number;
  };
}

/**
 * 监控配置
 */
export interface MonitoringConfig {
  /** 是否启用监控 */
  enabled: boolean;
  /** 监控端口 */
  port?: number;
  /** 统计信息更新间隔 (毫秒) */
  statsIntervalMs?: number;
  /** 是否启用健康检查端点 */
  healthCheck?: boolean;
  /** 是否启用指标端点 */
  metrics?: boolean;
}

/**
 * 主配置接口
 */
export interface NodexConfig {
  /** 任务定义 */
  task: TaskConfig;
  /** 抓取范围与策略 */
  crawlScope: CrawlScopeConfig;
  /** 深度控制 */
  depthControl: DepthControlConfig;
  /** 性能与并发 */
  concurrency: ConcurrencyConfig;
  /** 代理配置 */
  proxy: ProxyManagerConfig;
  /** 外部代理配置文件路径 */
  proxyJson?: string;
  /** 解析器配置 */
  parsers?: ParserConfig[];
  /** 存储配置 */
  storage: StorageConfig;
  /** 队列配置 */
  queue: QueueConfig;
  /** AI 配置 */
  ai: AIConfig;
  /** 日志配置 */
  logging: LogConfig;
  /** 监控配置 */
  monitoring: MonitoringConfig;
  /** 用户代理字符串 */
  userAgent: string;
  /** 是否严格遵守 robots.txt */
  respectRobotsTxt: boolean;
  /** 请求超时时间 (毫秒) */
  requestTimeoutMs: number;
  /** 自定义HTTP请求头 */
  customHeaders?: Record<string, string>;
  /** Cookie字符串 */
  cookies?: string;
}

/**
 * 配置管理器接口
 */
export interface IConfigManager {
  /**
   * 加载配置
   * @param configPath 配置文件路径
   */
  loadConfig(_configPath?: string): Promise<NodexConfig>;

  /**
   * 获取配置
   */
  getConfig(): NodexConfig;

  /**
   * 更新配置
   * @param updates 配置更新
   */
  updateConfig(_updates: Partial<NodexConfig>): void;

  /**
   * 验证配置
   * @param config 配置对象
   */
  validateConfig(_config: NodexConfig): boolean;

  /**
   * 保存配置
   * @param configPath 配置文件路径
   */
  saveConfig(_configPath?: string): Promise<void>;

  /**
   * 重新加载配置
   */
  reloadConfig(): Promise<void>;
}
