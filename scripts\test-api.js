#!/usr/bin/env node

/**
 * Nodex API 服务器测试脚本
 * 
 * 用于验证新的统一API服务器功能
 */

const http = require('http');
const WebSocket = require('ws');

const API_BASE = 'http://localhost:3000';
const WS_URL = 'ws://localhost:3000';

/**
 * 发送HTTP请求
 */
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, API_BASE);
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const req = http.request(url, options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            headers: res.headers,
            data: body ? JSON.parse(body) : null,
          };
          resolve(result);
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: body,
          });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

/**
 * 测试健康检查
 */
async function testHealthCheck() {
  console.log('🔍 测试健康检查...');
  try {
    const response = await makeRequest('GET', '/health');
    if (response.status === 200) {
      console.log('✅ 健康检查通过');
      console.log('   状态:', response.data.status);
      console.log('   运行时间:', response.data.uptime, '秒');
      return true;
    } else {
      console.log('❌ 健康检查失败:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ 健康检查错误:', error.message);
    return false;
  }
}

/**
 * 测试系统状态API
 */
async function testSystemStatus() {
  console.log('🔍 测试系统状态API...');
  try {
    const response = await makeRequest('GET', '/api/v1/system/status');
    if (response.status === 200) {
      console.log('✅ 系统状态API正常');
      console.log('   初始化状态:', response.data.system?.initialized || false);
      return true;
    } else {
      console.log('❌ 系统状态API失败:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ 系统状态API错误:', error.message);
    return false;
  }
}

/**
 * 测试任务API
 */
async function testTaskAPI() {
  console.log('🔍 测试任务API...');
  try {
    // 获取任务列表
    const listResponse = await makeRequest('GET', '/api/v1/tasks');
    if (listResponse.status === 200) {
      console.log('✅ 任务列表API正常');
      console.log('   任务数量:', listResponse.data.tasks?.length || 0);
    } else {
      console.log('❌ 任务列表API失败:', listResponse.status);
      return false;
    }

    // 创建测试任务
    const taskConfig = {
      name: '测试任务',
      config: {
        startUrls: ['https://example.com'],
        maxDepth: 1,
        concurrency: 1,
      },
    };

    const createResponse = await makeRequest('POST', '/api/v1/tasks', taskConfig);
    if (createResponse.status === 200) {
      console.log('✅ 创建任务API正常');
      console.log('   任务ID:', createResponse.data.taskId);
      
      // 获取任务详情
      const taskId = createResponse.data.taskId;
      const detailResponse = await makeRequest('GET', `/api/v1/tasks/${taskId}`);
      if (detailResponse.status === 200) {
        console.log('✅ 任务详情API正常');
        console.log('   任务状态:', detailResponse.data.status);
      }
      
      return true;
    } else {
      console.log('❌ 创建任务API失败:', createResponse.status);
      return false;
    }
  } catch (error) {
    console.log('❌ 任务API错误:', error.message);
    return false;
  }
}

/**
 * 测试监控API
 */
async function testMonitoringAPI() {
  console.log('🔍 测试监控API...');
  try {
    const response = await makeRequest('GET', '/api/v1/metrics');
    if (response.status === 200) {
      console.log('✅ 监控指标API正常');
      console.log('   系统运行时间:', response.data.metrics?.system?.uptime || 'N/A');
      return true;
    } else {
      console.log('❌ 监控指标API失败:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ 监控指标API错误:', error.message);
    return false;
  }
}

/**
 * 测试WebSocket连接
 */
async function testWebSocket() {
  console.log('🔍 测试WebSocket连接...');
  return new Promise((resolve) => {
    try {
      const ws = new WebSocket(WS_URL);
      let connected = false;

      const timeout = setTimeout(() => {
        if (!connected) {
          console.log('❌ WebSocket连接超时');
          ws.close();
          resolve(false);
        }
      }, 5000);

      ws.on('open', () => {
        connected = true;
        clearTimeout(timeout);
        console.log('✅ WebSocket连接成功');
        
        // 发送测试消息
        ws.send(JSON.stringify({ type: 'request-status' }));
        
        setTimeout(() => {
          ws.close();
          resolve(true);
        }, 1000);
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log('📨 收到WebSocket消息:', message.type || 'unknown');
        } catch (error) {
          console.log('📨 收到WebSocket消息:', data.toString());
        }
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        console.log('❌ WebSocket错误:', error.message);
        resolve(false);
      });

      ws.on('close', () => {
        clearTimeout(timeout);
        if (!connected) {
          console.log('❌ WebSocket连接失败');
          resolve(false);
        }
      });

    } catch (error) {
      console.log('❌ WebSocket测试错误:', error.message);
      resolve(false);
    }
  });
}

/**
 * 测试静态文件服务
 */
async function testStaticFiles() {
  console.log('🔍 测试静态文件服务...');
  try {
    const response = await makeRequest('GET', '/');
    if (response.status === 200 || response.status === 404) {
      console.log('✅ 静态文件服务正常');
      console.log('   响应状态:', response.status);
      return true;
    } else {
      console.log('❌ 静态文件服务异常:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ 静态文件服务错误:', error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试 Nodex API 服务器...\n');

  const tests = [
    { name: '健康检查', fn: testHealthCheck },
    { name: '系统状态API', fn: testSystemStatus },
    { name: '任务管理API', fn: testTaskAPI },
    { name: '监控API', fn: testMonitoringAPI },
    { name: 'WebSocket连接', fn: testWebSocket },
    { name: '静态文件服务', fn: testStaticFiles },
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    const result = await test.fn();
    if (result) {
      passed++;
    } else {
      failed++;
    }
    console.log('');
  }

  console.log('📊 测试结果:');
  console.log(`   ✅ 通过: ${passed}`);
  console.log(`   ❌ 失败: ${failed}`);
  console.log(`   📈 成功率: ${Math.round((passed / tests.length) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 所有测试通过！API服务器运行正常。');
    process.exit(0);
  } else {
    console.log('\n⚠️  部分测试失败，请检查服务器状态。');
    process.exit(1);
  }
}

// 检查是否安装了ws模块
try {
  require('ws');
} catch (error) {
  console.log('⚠️  警告: 未安装ws模块，WebSocket测试将被跳过');
  console.log('   安装命令: npm install ws');
}

// 运行测试
runTests().catch((error) => {
  console.error('❌ 测试运行失败:', error);
  process.exit(1);
});
