# Nodex HTTP API 使用示例

本目录包含了 Nodex 智能爬虫 HTTP API 服务的 JavaScript 调用示例，帮助您快速上手。

## 📁 示例文件

- [`basic-usage.js`](./basic-usage.js) - 基础 API 调用示例
- [`batch-crawling.js`](./batch-crawling.js) - 批量爬取示例
- [`real-time-monitor.js`](./real-time-monitor.js) - 实时监控示例
- [`express-integration.js`](./express-integration.js) - Express 集成示例

## 🚀 快速开始

### 前提条件

1. **启动 Nodex API 服务器**
```bash
# 安装依赖
npm install

# 构建项目
npm run build

# 启动 API 服务器
npm start
# 或开发模式
npm run dev
```

2. **验证服务器运行**
```bash
curl http://localhost:3000/health
```

### 基础 API 调用示例

#### 1. 创建爬取任务
```bash
curl -X POST http://localhost:3000/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "示例爬取任务",
    "config": {
      "startUrls": ["https://example.com"],
      "maxDepth": 2,
      "concurrency": 3
    }
  }'
```

#### 2. 查看任务状态
```bash
curl http://localhost:3000/api/v1/tasks/{taskId}
```

#### 3. 获取爬取结果
```bash
curl http://localhost:3000/api/v1/results/{taskId}
```

## 📋 API 端点概览

| 端点 | 方法 | 描述 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/api/v1/tasks` | POST | 创建爬取任务 |
| `/api/v1/tasks` | GET | 获取任务列表 |
| `/api/v1/tasks/{id}` | GET | 获取任务详情 |
| `/api/v1/tasks/{id}` | PUT | 更新任务配置 |
| `/api/v1/tasks/{id}` | DELETE | 删除任务 |
| `/api/v1/tasks/{id}/start` | POST | 启动任务 |
| `/api/v1/tasks/{id}/stop` | POST | 停止任务 |
| `/api/v1/results/{taskId}` | GET | 获取爬取结果 |
| `/api/v1/stats` | GET | 获取系统统计信息 |

## 🔧 环境配置

### 环境变量
```bash
# API 服务器配置
API_PORT=3000
API_HOST=0.0.0.0
ENABLE_CORS=true
CORS_ORIGIN=*

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/nodex
REDIS_URL=redis://localhost:6379

# AI 服务配置（可选）
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
```

### Docker 快速启动
```bash
# 使用 Docker Compose 启动完整服务
cd examples/docker-deployment
docker-compose up -d

# 验证服务
curl http://localhost:3000/health
```

## 📚 详细文档

- [API 文档](../docs/api.md) - 完整的 API 接口文档
- [配置指南](../docs/configuration.md) - 详细的配置说明
- [部署指南](../docs/deployment.md) - 生产环境部署指南
- [故障排除](../docs/troubleshooting.md) - 常见问题解决方案

## 💡 最佳实践

1. **任务管理**：合理设置并发数和深度限制
2. **错误处理**：始终检查 API 响应状态码
3. **监控**：定期检查任务状态和系统健康
4. **资源管理**：及时清理完成的任务和结果数据

## 🤝 贡献

欢迎提交新的示例和改进建议！请查看 [贡献指南](../CONTRIBUTING.md)。

- Node.js >= 18.0.0
- MongoDB (用于数据存储)
- Redis (用于任务队列)
- 可选：OpenAI API Key (用于 AI 功能)

## 🔧 配置说明

每个示例都包含详细的配置说明和注释。建议按照以下顺序学习：

1. 先运行基础示例了解核心功能
2. 查看配置示例了解各种配置选项
3. 尝试高级功能如 AI 增强和代理
4. 根据实际需求选择相应的应用场景示例

## 📖 更多文档

- [API 文档](../docs/API.md)
- [使用指南](../docs/USAGE.md)
- [配置参考](../docs/CONFIG.md)

## 💡 提示

- 所有示例都可以直接运行，但建议先阅读代码注释
- 某些示例需要配置环境变量（如 API 密钥）
- 生产环境使用前请仔细调整配置参数
