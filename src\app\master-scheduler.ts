/**
 * 主调度器
 *
 * 负责系统级的统一调度和管理，协调各个组件的工作
 */

import { EventEmitter } from 'events';
import { IModuleManager, ModuleManager } from './module-manager';
import { IStrategyManager, StrategyManager } from './strategy-manager';
import { getLogger } from '@/utils';

/**
 * 调度器状态枚举
 */
export enum SchedulerState {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  RUNNING = 'running',
  PAUSED = 'paused',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error',
}

/**
 * 调度配置
 */
export interface SchedulerConfig {
  maxConcurrentTasks: number;
  taskTimeout: number;
  heartbeatInterval: number;
  healthCheckInterval: number;
  metricsInterval: number;
  autoRestart: boolean;
  maxRestartAttempts: number;
}

/**
 * 任务优先级
 */
export enum TaskPriority {
  LOW = 1,
  NORMAL = 5,
  HIGH = 8,
  CRITICAL = 10,
}

/**
 * 调度任务接口
 */
export interface ScheduledTask {
  id: string;
  name: string;
  priority: TaskPriority;
  execute: () => Promise<any>;
  timeout?: number;
  retry?: number;
  createdAt: Date;
  scheduledAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  error?: Error;
}

/**
 * 主调度器接口
 */
export interface IMasterScheduler {
  initialize(): Promise<void>;
  start(): Promise<void>;
  stop(): Promise<void>;
  pause(): Promise<void>;
  resume(): Promise<void>;
  scheduleTask(task: Omit<ScheduledTask, 'id' | 'createdAt'>): string;
  cancelTask(taskId: string): boolean;
  getState(): SchedulerState;
  getStats(): any;
  healthCheck(): Promise<boolean>;
}

/**
 * 主调度器实现
 */
export class MasterScheduler extends EventEmitter implements IMasterScheduler {
  private state: SchedulerState = SchedulerState.IDLE;
  private config: SchedulerConfig;
  private moduleManager: IModuleManager;
  private strategyManager: IStrategyManager;
  private taskQueue: ScheduledTask[] = [];
  private runningTasks: Map<string, ScheduledTask> = new Map();
  private completedTasks: ScheduledTask[] = [];
  private heartbeatTimer?: NodeJS.Timeout;
  private healthCheckTimer?: NodeJS.Timeout;
  private metricsTimer?: NodeJS.Timeout;
  private logger = getLogger('MasterScheduler');

  constructor(config?: Partial<SchedulerConfig>) {
    super();

    this.config = {
      maxConcurrentTasks: 10,
      taskTimeout: 300000, // 5分钟
      heartbeatInterval: 30000, // 30秒
      healthCheckInterval: 60000, // 1分钟
      metricsInterval: 60000, // 1分钟
      autoRestart: true,
      maxRestartAttempts: 3,
      ...config,
    };

    this.moduleManager = new ModuleManager();
    this.strategyManager = new StrategyManager();

    this.setupEventListeners();
    this.logger.info('主调度器初始化', { config: this.config });
  }

  /**
   * 初始化调度器
   */
  async initialize(): Promise<void> {
    if (this.state !== SchedulerState.IDLE) {
      throw new Error(`Cannot initialize scheduler in state: ${this.state}`);
    }

    try {
      this.setState(SchedulerState.INITIALIZING);
      this.logger.info('正在初始化主调度器');

      // 这里可以添加其他初始化逻辑
      await this.moduleManager.startAllModules();

      this.setState(SchedulerState.STOPPED);
      this.logger.info('主调度器初始化完成');
      this.emit('initialized');
    } catch (error) {
      this.setState(SchedulerState.ERROR);
      this.logger.error('主调度器初始化失败', error);
      throw error;
    }
  }

  /**
   * 启动调度器
   */
  async start(): Promise<void> {
    if (this.state === SchedulerState.RUNNING) {
      this.logger.warn('调度器已在运行');
      return;
    }

    try {
      this.setState(SchedulerState.RUNNING);
      this.logger.info('正在启动主调度器');

      // 启动定时器
      this.startTimers();

      // 开始处理任务队列
      this.processTaskQueue();

      this.logger.info('主调度器启动成功');
      this.emit('started');
    } catch (error) {
      this.setState(SchedulerState.ERROR);
      this.logger.error('主调度器启动失败', error);
      throw error;
    }
  }

  /**
   * 停止调度器
   */
  async stop(): Promise<void> {
    if (this.state === SchedulerState.STOPPED) {
      this.logger.warn('调度器已停止');
      return;
    }

    try {
      this.setState(SchedulerState.STOPPING);
      this.logger.info('正在停止主调度器');

      // 停止定时器
      this.stopTimers();

      // 等待正在运行的任务完成或超时
      await this.waitForRunningTasks(10000); // 10秒超时

      // 停止所有模块
      await this.moduleManager.stopAllModules();

      this.setState(SchedulerState.STOPPED);
      this.logger.info('主调度器已停止');
      this.emit('stopped');
    } catch (error) {
      this.setState(SchedulerState.ERROR);
      this.logger.error('主调度器停止失败', error);
      throw error;
    }
  }

  /**
   * 暂停调度器
   */
  async pause(): Promise<void> {
    if (this.state !== SchedulerState.RUNNING) {
      throw new Error(`Cannot pause scheduler in state: ${this.state}`);
    }

    this.setState(SchedulerState.PAUSED);
    this.logger.info('主调度器已暂停');
    this.emit('paused');
  }

  /**
   * 恢复调度器
   */
  async resume(): Promise<void> {
    if (this.state !== SchedulerState.PAUSED) {
      throw new Error(`Cannot resume scheduler in state: ${this.state}`);
    }

    this.setState(SchedulerState.RUNNING);
    this.processTaskQueue(); // 重新开始处理任务
    this.logger.info('主调度器已恢复');
    this.emit('resumed');
  }

  /**
   * 调度任务
   */
  scheduleTask(task: Omit<ScheduledTask, 'id' | 'createdAt'>): string {
    const scheduledTask: ScheduledTask = {
      ...task,
      id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      scheduledAt: new Date(),
    };

    // 按优先级插入队列
    this.insertTaskByPriority(scheduledTask);

    this.logger.debug('任务已调度', { taskId: scheduledTask.id, priority: scheduledTask.priority });
    this.emit('taskScheduled', scheduledTask);

    // 如果调度器正在运行，立即尝试处理任务
    if (this.state === SchedulerState.RUNNING) {
      setImmediate(() => this.processTaskQueue());
    }

    return scheduledTask.id;
  }

  /**
   * 取消任务
   */
  cancelTask(taskId: string): boolean {
    // 从队列中移除
    const queueIndex = this.taskQueue.findIndex(task => task.id === taskId);
    if (queueIndex !== -1) {
      const task = this.taskQueue.splice(queueIndex, 1)[0];
      this.logger.info('任务已从队列中取消', { taskId });
      this.emit('taskCancelled', task);
      return true;
    }

    // 检查是否在运行中（运行中的任务无法取消）
    if (this.runningTasks.has(taskId)) {
      this.logger.warn('无法取消正在运行的任务', { taskId });
      return false;
    }

    return false;
  }

  /**
   * 获取状态
   */
  getState(): SchedulerState {
    return this.state;
  }

  /**
   * 获取统计信息
   */
  getStats(): any {
    return {
      state: this.state,
      config: this.config,
      tasks: {
        queued: this.taskQueue.length,
        running: this.runningTasks.size,
        completed: this.completedTasks.length,
      },
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      moduleStats: this.moduleManager.getAllModules().map(m => ({
        name: m.name,
        state: m.state,
        errorCount: m.errorCount,
      })),
      strategyStats: {
        strategy: this.strategyManager.getTraversalStrategy(),
        // 添加其他相关统计信息
        currentDepth: 0,
        processedTasks: 0,
      },
    };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (this.state === SchedulerState.ERROR) {
        return false;
      }

      // 检查模块健康状态
      const moduleHealth = await this.moduleManager.healthCheck();
      const unhealthyModules = Object.entries(moduleHealth).filter(([, healthy]) => !healthy);

      if (unhealthyModules.length > 0) {
        this.logger.warn('发现不健康的模块', { unhealthyModules });
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('健康检查失败', error);
      return false;
    }
  }

  /**
   * 处理任务队列
   */
  private async processTaskQueue(): Promise<void> {
    if (this.state !== SchedulerState.RUNNING) {
      return;
    }

    while (this.taskQueue.length > 0 && this.runningTasks.size < this.config.maxConcurrentTasks) {
      const task = this.taskQueue.shift()!;
      this.executeTask(task);
    }
  }

  /**
   * 执行任务
   */
  private async executeTask(task: ScheduledTask): Promise<void> {
    this.runningTasks.set(task.id, task);
    task.startedAt = new Date();

    this.logger.info('开始执行任务', { taskId: task.id, name: task.name });
    this.emit('taskStarted', task);

    try {
      // 设置超时
      const timeout = task.timeout || this.config.taskTimeout;
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Task timeout')), timeout);
      });

      // 执行任务
      await Promise.race([task.execute(), timeoutPromise]);

      task.completedAt = new Date();
      this.runningTasks.delete(task.id);
      this.completedTasks.push(task);

      this.logger.info('任务执行成功', {
        taskId: task.id,
        duration: task.completedAt.getTime() - task.startedAt!.getTime(),
      });
      this.emit('taskCompleted', task);
    } catch (error) {
      task.error = error as Error;
      task.completedAt = new Date();
      this.runningTasks.delete(task.id);
      this.completedTasks.push(task);

      this.logger.error('任务执行失败', {
        taskId: task.id,
        error: (error as Error).message,
      });
      this.emit('taskFailed', task);
    }

    // 继续处理队列
    setImmediate(() => this.processTaskQueue());
  }

  /**
   * 按优先级插入任务
   */
  private insertTaskByPriority(task: ScheduledTask): void {
    let insertIndex = 0;
    while (insertIndex < this.taskQueue.length) {
      const currentTask = this.taskQueue[insertIndex];
      if (!currentTask || currentTask.priority < task.priority) {
        break;
      }
      insertIndex++;
    }
    this.taskQueue.splice(insertIndex, 0, task);
  }

  /**
   * 启动定时器
   */
  private startTimers(): void {
    // 心跳定时器
    this.heartbeatTimer = setInterval(() => {
      this.emit('heartbeat', { timestamp: new Date(), state: this.state });
    }, this.config.heartbeatInterval);

    // 健康检查定时器
    this.healthCheckTimer = setInterval(async () => {
      const healthy = await this.healthCheck();
      this.emit('healthCheck', { healthy, timestamp: new Date() });
    }, this.config.healthCheckInterval);

    // 指标收集定时器
    this.metricsTimer = setInterval(() => {
      const stats = this.getStats();
      this.emit('metrics', { stats, timestamp: new Date() });
    }, this.config.metricsInterval);
  }

  /**
   * 停止定时器
   */
  private stopTimers(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = undefined;
    }
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }
    if (this.metricsTimer) {
      clearInterval(this.metricsTimer);
      this.metricsTimer = undefined;
    }
  }

  /**
   * 等待正在运行的任务完成
   */
  private async waitForRunningTasks(timeout: number): Promise<void> {
    if (this.runningTasks.size === 0) {
      return;
    }

    return new Promise(resolve => {
      const startTime = Date.now();
      const checkInterval = setInterval(() => {
        if (this.runningTasks.size === 0 || Date.now() - startTime > timeout) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  /**
   * 设置状态
   */
  private setState(state: SchedulerState): void {
    const oldState = this.state;
    this.state = state;
    this.emit('stateChanged', { oldState, newState: state });
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 模块事件监听 - 检查moduleManager是否支持事件
    if ('on' in this.moduleManager && typeof this.moduleManager.on === 'function') {
      this.moduleManager.on('moduleError', (data: any) => {
        this.logger.warn('模块错误事件', data);
        this.emit('moduleError', data);
      });
    }

    // 进程退出处理
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
  }

  /**
   * 优雅关闭
   */
  private async gracefulShutdown(signal: string): Promise<void> {
    this.logger.info('接收到关闭信号，开始优雅关闭', { signal });
    try {
      await this.stop();
      process.exit(0);
    } catch (error) {
      this.logger.error('优雅关闭失败', error);
      process.exit(1);
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    this.logger.info('正在清理主调度器');

    await this.stop();
    this.stopTimers();

    // 检查是否有cleanup方法
    if ('cleanup' in this.moduleManager && typeof this.moduleManager.cleanup === 'function') {
      await this.moduleManager.cleanup();
    }

    if ('cleanup' in this.strategyManager && typeof this.strategyManager.cleanup === 'function') {
      this.strategyManager.cleanup();
    }

    this.taskQueue = [];
    this.runningTasks.clear();
    this.completedTasks = [];

    this.removeAllListeners();
    this.logger.info('主调度器已清理');
  }
}
