import { Activity, Clock, Database, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { StatusBadge } from '@/components/ui/Badge';
import { formatUptime, formatMemory, formatPercentage, formatNumber } from '@/lib/utils';
import type { SystemStats } from '@/types/monitoring';

interface SystemOverviewProps {
  systemStats: SystemStats | null;
  loading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number | React.ReactNode;
  icon: React.ReactNode;
  color: string;
  subtitle?: string;
}

function MetricCard({ title, value, icon, color, subtitle }: MetricCardProps) {
  return (
    <Card className={`${color} border-0`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium opacity-80">{title}</p>
            <p className="text-2xl font-bold mt-1">{value}</p>
            {subtitle && (
              <p className="text-xs opacity-70 mt-1">{subtitle}</p>
            )}
          </div>
          <div className="opacity-80">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function SystemOverview({ systemStats, loading }: SystemOverviewProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="h-8 w-8 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!systemStats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="col-span-full">
          <CardContent className="p-6 text-center">
            <p className="text-gray-500">暂无系统数据</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const metrics = [
    {
      title: '系统状态',
      value: <StatusBadge status={systemStats?.status} />,
      icon: <Activity className="h-8 w-8" />,
      color: 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-900',
      subtitle: formatUptime(systemStats?.uptime),
    },
    {
      title: '活跃会话',
      value: systemStats?.sessions?.active,
      icon: <Zap className="h-8 w-8" />,
      color: 'bg-gradient-to-br from-green-50 to-green-100 text-green-900',
      subtitle: `总计 ${systemStats?.sessions?.total} 个会话`,
    },
    {
      title: '已处理URL',
      value: formatNumber(systemStats?.urls?.processed),
      icon: <Database className="h-8 w-8" />,
      color: 'bg-gradient-to-br from-purple-50 to-purple-100 text-purple-900',
      subtitle: `总计 ${formatNumber(systemStats?.urls?.total)} 个`,
    },
    {
      title: '成功率',
      value: formatPercentage(systemStats?.urls?.successRate),
      icon: <Clock className="h-8 w-8" />,
      color: 'bg-gradient-to-br from-orange-50 to-orange-100 text-orange-900',
      subtitle: `失败 ${systemStats?.urls?.failed} 个`,
    },
  ];

  return (
    <div className="space-y-6">
      {/* 主要指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={metric.icon}
            color={metric.color}
            subtitle={metric.subtitle}
          />
        ))}
      </div>

      {/* 详细系统信息 */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Activity className="h-5 w-5 mr-2 text-blue-500" />
            系统详细信息
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                运行时间
              </label>
              <div className="text-lg font-mono text-gray-900">
                {formatUptime(systemStats?.uptime)}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                内存使用
              </label>
              <div className="text-lg font-mono text-gray-900">
                {formatMemory(systemStats?.memory.heapUsed)}
              </div>
              <div className="text-xs text-gray-500">
                / {formatMemory(systemStats?.memory.heapTotal)}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                会话统计
              </label>
              <div className="text-lg font-mono text-gray-900">
                {systemStats?.sessions?.active} / {systemStats?.sessions?.total}
              </div>
              <div className="text-xs text-gray-500">
                活跃 / 总计
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                处理统计
              </label>
              <div className="text-lg font-mono text-gray-900">
                {formatNumber(systemStats?.urls?.processed)}
              </div>
              <div className="text-xs text-gray-500">
                成功率 {formatPercentage(systemStats?.urls?.successRate)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
