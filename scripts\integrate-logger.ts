#!/usr/bin/env ts-node

/**
 * 批量集成 Logger 模块到项目中的脚本
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { resolve, join } from 'path';
import { glob } from 'glob';

interface LoggerIntegration {
  filePath: string;
  hasLogger: boolean;
  consoleUsages: number;
  needsIntegration: boolean;
}

const srcDir = resolve(__dirname, '../src');

// 需要集成 logger 的文件模式
const targetPatterns = [
  'src/modules/**/*.ts',
  'src/config/**/*.ts',
  'src/api/**/*.ts',
  'src/app/**/*.ts',
];

// 排除的文件
const excludePatterns = [
  '**/*.test.ts',
  '**/*.spec.ts',
  '**/index.ts',
  '**/types.ts',
  '**/interfaces.ts',
];

async function analyzeFile(filePath: string): Promise<LoggerIntegration> {
  const content = readFileSync(filePath, 'utf-8');
  
  // 检查是否已经导入了 logger
  const hasLoggerImport = /import.*getLogger.*from.*@\/utils/.test(content);
  const hasLoggerUsage = /getLogger\(/.test(content);
  const hasLogger = hasLoggerImport || hasLoggerUsage;
  
  // 统计 console 使用次数
  const consoleMatches = content.match(/console\.(log|error|warn|debug|info)/g);
  const consoleUsages = consoleMatches ? consoleMatches.length : 0;
  
  // 判断是否需要集成
  const needsIntegration = !hasLogger && consoleUsages > 0;
  
  return {
    filePath,
    hasLogger,
    consoleUsages,
    needsIntegration,
  };
}

async function integrateLoggerToFile(filePath: string): Promise<boolean> {
  try {
    let content = readFileSync(filePath, 'utf-8');
    
    // 1. 添加 logger 导入
    if (!content.includes('getLogger')) {
      // 找到最后一个 import 语句
      const importLines = content.split('\n');
      let lastImportIndex = -1;
      
      for (let i = 0; i < importLines.length; i++) {
        if (importLines[i].trim().startsWith('import ') && 
            !importLines[i].includes('//') && 
            importLines[i].includes('from')) {
          lastImportIndex = i;
        }
      }
      
      if (lastImportIndex >= 0) {
        importLines.splice(lastImportIndex + 1, 0, "import { getLogger } from '@/utils';");
        content = importLines.join('\n');
      }
    }
    
    // 2. 在类中添加 logger 实例
    const classMatches = content.match(/export class (\w+)/g);
    if (classMatches) {
      for (const classMatch of classMatches) {
        const className = classMatch.replace('export class ', '');
        
        // 检查是否已经有 logger 实例
        if (!content.includes('private logger = getLogger')) {
          // 找到类的第一个私有属性位置
          const classStartRegex = new RegExp(`export class ${className}[^{]*{`);
          const classStartMatch = content.match(classStartRegex);
          
          if (classStartMatch) {
            const insertPos = content.indexOf(classStartMatch[0]) + classStartMatch[0].length;
            const loggerLine = `\n  private logger = getLogger('${className}');`;
            content = content.slice(0, insertPos) + loggerLine + content.slice(insertPos);
          }
        }
      }
    }
    
    // 3. 替换 console 调用
    content = content
      .replace(/console\.log\(/g, 'this.logger.info(')
      .replace(/console\.info\(/g, 'this.logger.info(')
      .replace(/console\.warn\(/g, 'this.logger.warn(')
      .replace(/console\.error\(/g, 'this.logger.error(')
      .replace(/console\.debug\(/g, 'this.logger.debug(');
    
    writeFileSync(filePath, content, 'utf-8');
    return true;
    
  } catch (error) {
    console.error(`处理文件失败 ${filePath}:`, error);
    return false;
  }
}

async function main() {
  console.log('🔍 分析项目中的日志使用情况...\n');
  
  // 获取所有目标文件
  const allFiles: string[] = [];
  for (const pattern of targetPatterns) {
    const files = await glob(pattern, { 
      cwd: resolve(__dirname, '..'),
      ignore: excludePatterns 
    });
    allFiles.push(...files.map(f => resolve(__dirname, '..', f)));
  }
  
  // 分析每个文件
  const analyses: LoggerIntegration[] = [];
  for (const filePath of allFiles) {
    if (existsSync(filePath)) {
      const analysis = await analyzeFile(filePath);
      analyses.push(analysis);
    }
  }
  
  // 生成报告
  const needsIntegration = analyses.filter(a => a.needsIntegration);
  const hasLogger = analyses.filter(a => a.hasLogger);
  const totalConsoleUsages = analyses.reduce((sum, a) => sum + a.consoleUsages, 0);
  
  console.log('📊 分析结果:');
  console.log(`  总文件数: ${analyses.length}`);
  console.log(`  已集成 Logger: ${hasLogger.length}`);
  console.log(`  需要集成: ${needsIntegration.length}`);
  console.log(`  Console 调用总数: ${totalConsoleUsages}\n`);
  
  if (needsIntegration.length > 0) {
    console.log('📝 需要集成 Logger 的文件:');
    needsIntegration.forEach(analysis => {
      const relativePath = analysis.filePath.replace(resolve(__dirname, '..'), '');
      console.log(`  ${relativePath} (${analysis.consoleUsages} console 调用)`);
    });
    console.log();
  }
  
  // 询问是否执行集成
  console.log('🚀 开始自动集成 Logger...\n');
  
  let successCount = 0;
  let failCount = 0;
  
  for (const analysis of needsIntegration) {
    const relativePath = analysis.filePath.replace(resolve(__dirname, '..'), '');
    console.log(`处理: ${relativePath}`);
    
    const success = await integrateLoggerToFile(analysis.filePath);
    if (success) {
      successCount++;
      console.log(`  ✅ 成功`);
    } else {
      failCount++;
      console.log(`  ❌ 失败`);
    }
  }
  
  console.log(`\n🎉 集成完成!`);
  console.log(`  成功: ${successCount}`);
  console.log(`  失败: ${failCount}`);
  
  if (failCount > 0) {
    console.log('\n⚠️  部分文件集成失败，请手动检查和修复。');
  }
}

if (require.main === module) {
  main().catch(console.error);
}
