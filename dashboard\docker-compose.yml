version: '3.8'

services:
  # Nodex 监控面板前端
  nodex-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nodex-monitoring-frontend
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:3001/api
      - NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
    depends_on:
      - nodex-backend
    networks:
      - nodex-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nodex 后端服务（假设存在）
  nodex-backend:
    image: nodex-backend:latest
    container_name: nodex-monitoring-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    networks:
      - nodex-network
    restart: unless-stopped

networks:
  nodex-network:
    driver: bridge

volumes:
  nodex-data:
    driver: local
