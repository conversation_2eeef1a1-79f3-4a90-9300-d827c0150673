{"task": {"seedUrls": ["https://httpbin.org/get"], "traversalStrategy": "BFS", "name": "nodex-dev", "description": "Nodex 环境配置"}, "crawlScope": {"allowedDomains": ["httpbin.org"], "disallowedPatterns": [], "followSubdomains": false, "ignoreWWW": true, "allowedExtensions": ["html", "htm", "json"], "maxFileSize": 10485760}, "depthControl": {"global": 2, "perDomain": {}}, "concurrency": {"maxConcurrentRequests": 3, "delayBetweenRequestsMs": 1000, "requestTimeoutMs": 30000, "maxRetries": 3, "retryDelayMs": 2000}, "proxy": {"enabled": false, "proxies": [], "strategy": "round-robin", "healthCheck": {"enabled": false, "intervalMs": 60000, "testUrl": "https://httpbin.org/ip", "timeoutMs": 10000}, "failureThreshold": 3, "recoveryTimeMs": 300000}, "proxyJson": "./webshare_proxy.json", "storage": {"type": "memory", "options": {}}, "queue": {"redis": {"host": "localhost", "port": 6379, "db": 0}, "queuePrefix": "nodex", "concurrency": 5}, "ai": {"enabled": false, "provider": "openai", "model": "gpt-3.5-turbo", "apiKey": "", "maxTokens": 4000, "temperature": 0.1}, "logging": {"level": "info", "file": "logs/nodex.log", "console": true, "maxFiles": 5, "maxSize": "10m"}, "monitoring": {"enabled": true, "port": 3001, "host": "localhost"}, "userAgent": "Nodex/2.0.0 (Development)", "respectRobotsTxt": false, "requestTimeoutMs": 30000}