#!/usr/bin/env ts-node

/**
 * Logger 功能演示脚本
 */

import { resolve } from 'path';
import { existsSync, unlinkSync } from 'fs';

// 设置环境变量
process.env.LOG_LEVEL = 'debug';

import { 
  Logger, 
  LogLevel, 
  debug, 
  info, 
  warn, 
  error, 
  time, 
  errorWithStack,
  getLogger,
  loggerManager 
} from '../src/utils';

async function testBasicLogging() {
  console.log('\n=== 基础日志功能测试 ===');
  
  debug('这是一条调试信息', { module: 'test', data: { key: 'value' } });
  info('这是一条信息日志', { operation: 'test', status: 'success' });
  warn('这是一条警告信息', { warning: 'test warning' });
  error('这是一条错误信息', { error: 'test error' });
}

async function testModuleLoggers() {
  console.log('\n=== 模块日志器测试 ===');
  
  const userLogger = getLogger('UserService');
  const authLogger = getLogger('AuthService');
  
  userLogger.info('用户服务启动');
  userLogger.debug('用户查询', { userId: '12345' });
  
  authLogger.info('认证服务启动');
  authLogger.warn('登录失败', { username: 'test', reason: 'invalid password' });
}

async function testPerformanceFeatures() {
  console.log('\n=== 性能优化功能测试 ===');
  
  const logger = getLogger('Performance');
  
  // 测试时间测量
  const timer = time('数据库查询', 'Performance');
  await new Promise(resolve => setTimeout(resolve, 100));
  timer();
  
  // 测试懒加载日志
  logger.debugLazy(() => {
    console.log('  -> 懒加载日志被执行了');
    return '这是一个懒加载的调试消息';
  }, { lazy: true });
  
  // 测试错误堆栈记录
  try {
    throw new Error('测试错误');
  } catch (err) {
    errorWithStack('捕获到测试错误', err as Error, 'Performance');
  }
}

async function testFileLogging() {
  console.log('\n=== 文件日志功能测试 ===');
  
  const logFile = resolve(__dirname, '../test-logs/demo.log');
  
  // 清理旧的日志文件
  if (existsSync(logFile)) {
    unlinkSync(logFile);
  }
  
  const fileLogger = new Logger({
    level: LogLevel.DEBUG,
    console: true,
    file: logFile,
    format: 'json',
  });
  
  await fileLogger.initialize();
  
  fileLogger.info('文件日志测试开始');
  fileLogger.debug('调试信息写入文件', { test: true });
  fileLogger.warn('警告信息写入文件', { warning: 'test' });
  fileLogger.error('错误信息写入文件', { error: 'test error' });
  
  console.log(`  -> 日志已写入文件: ${logFile}`);
  
  await fileLogger.close();
}

async function testLogLevels() {
  console.log('\n=== 日志级别过滤测试 ===');
  
  console.log('  -> 设置日志级别为 WARN');
  const warnLogger = new Logger({
    level: LogLevel.WARN,
    console: true,
  });
  
  console.log('  -> 以下 DEBUG 和 INFO 消息应该被过滤掉:');
  warnLogger.debug('这条调试信息不应该显示');
  warnLogger.info('这条信息日志不应该显示');
  warnLogger.warn('这条警告信息应该显示');
  warnLogger.error('这条错误信息应该显示');
}

async function testLoggerManager() {
  console.log('\n=== 日志管理器测试 ===');
  
  // 创建一些模块日志器
  getLogger('Module1');
  getLogger('Module2');
  getLogger('Module3');
  
  const stats = loggerManager.getStats();
  console.log('  -> 日志器统计信息:', {
    totalLoggers: stats.totalLoggers,
    moduleLoggers: stats.moduleLoggers,
  });
  
  // 测试日志级别设置
  console.log('  -> 设置全局日志级别为 ERROR');
  loggerManager.setLogLevel(LogLevel.ERROR);
  
  const testLogger = getLogger('LevelTest');
  console.log('  -> 以下消息应该被过滤 (只有 ERROR 级别会显示):');
  testLogger.debug('调试消息 - 应该被过滤');
  testLogger.info('信息消息 - 应该被过滤');
  testLogger.warn('警告消息 - 应该被过滤');
  testLogger.error('错误消息 - 应该显示');
  
  // 恢复日志级别
  loggerManager.setLogLevel(LogLevel.DEBUG);
}

async function main() {
  console.log('🚀 开始 Logger 功能演示\n');
  
  try {
    await testBasicLogging();
    await testModuleLoggers();
    await testPerformanceFeatures();
    await testFileLogging();
    await testLogLevels();
    await testLoggerManager();
    
    console.log('\n✅ 所有测试完成！');
    
  } catch (err) {
    console.error('\n❌ 测试过程中发生错误:', err);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}
