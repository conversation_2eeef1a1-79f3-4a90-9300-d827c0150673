
import { Layers, Play, Pause, RotateCcw, Trash2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { StatusBadge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { formatNumber, formatPercentage, getHealthIndicatorColor } from '@/lib/utils';
import type { QueueStats } from '@/types/monitoring';

interface QueueMonitorProps {
  queueStats: QueueStats[];
  loading?: boolean;
  onPauseQueue?: (queueName: string) => void;
  onResumeQueue?: (queueName: string) => void;
  onClearQueue?: (queueName: string) => void;
  onRetryFailed?: (queueName: string) => void;
}

interface QueueCardProps {
  queue: QueueStats;
  onPause?: () => void;
  onResume?: () => void;
  onClear?: () => void;
  onRetryFailed?: () => void;
}

function QueueCard({ queue, onPause, onResume, onClear, onRetryFailed }: QueueCardProps) {
  const successRate = queue.successRate || 0;
  const healthColor = getHealthIndicatorColor(queue.health || 'healthy');

  return (
    <Card className="relative overflow-hidden">
      {/* 健康状态指示器 */}
      <div className={`absolute top-0 left-0 w-1 h-full ${healthColor}`} />
      
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            <Layers className="h-5 w-5 text-indigo-500" />
            {queue.queueName}
          </CardTitle>
          <div className="flex items-center space-x-2">
            <StatusBadge status={queue.health || 'healthy'} size="sm" />
            {queue.paused && <StatusBadge status="paused" size="sm" />}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 队列指标 */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {formatNumber(queue.waiting)}
            </div>
            <div className="text-xs text-gray-500">等待中</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatNumber(queue.active)}
            </div>
            <div className="text-xs text-gray-500">处理中</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {formatNumber(queue.completed)}
            </div>
            <div className="text-xs text-gray-500">已完成</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {formatNumber(queue.failed)}
            </div>
            <div className="text-xs text-gray-500">失败</div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 gap-4 pt-2 border-t border-gray-100">
          <div>
            <div className="text-sm text-gray-500">成功率</div>
            <div className="text-lg font-semibold">
              {formatPercentage(successRate)}
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-500">吞吐量</div>
            <div className="text-lg font-semibold">
              {queue.throughput?.toFixed(2) || '0.00'}/分钟
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-100">
          {queue.paused ? (
            <Button
              size="sm"
              variant="success"
              icon={<Play className="h-4 w-4" />}
              onClick={onResume}
            >
              恢复
            </Button>
          ) : (
            <Button
              size="sm"
              variant="warning"
              icon={<Pause className="h-4 w-4" />}
              onClick={onPause}
            >
              暂停
            </Button>
          )}
          
          {queue.failed > 0 && (
            <Button
              size="sm"
              variant="secondary"
              icon={<RotateCcw className="h-4 w-4" />}
              onClick={onRetryFailed}
            >
              重试失败
            </Button>
          )}
          
          <Button
            size="sm"
            variant="danger"
            icon={<Trash2 className="h-4 w-4" />}
            onClick={onClear}
          >
            清空
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export function QueueMonitor({
  queueStats,
  loading,
  onPauseQueue,
  onResumeQueue,
  onClearQueue,
  onRetryFailed,
}: QueueMonitorProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Layers className="h-5 w-5 mr-2 text-indigo-500" />
            任务队列状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!queueStats || queueStats.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Layers className="h-5 w-5 mr-2 text-indigo-500" />
            任务队列状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Layers className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">暂无队列数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 计算总体统计
  const totalStats = queueStats.reduce(
    (acc, queue) => ({
      waiting: acc.waiting + queue.waiting,
      active: acc.active + queue.active,
      completed: acc.completed + queue.completed,
      failed: acc.failed + queue.failed,
    }),
    { waiting: 0, active: 0, completed: 0, failed: 0 }
  );

  return (
    <div className="space-y-6">
      {/* 总体统计 */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Layers className="h-5 w-5 mr-2 mr-2 text-indigo-500" />
            队列总览
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600">
                {formatNumber(totalStats.waiting)}
              </div>
              <div className="text-sm text-gray-500">等待任务</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {formatNumber(totalStats.active)}
              </div>
              <div className="text-sm text-gray-500">活跃任务</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {formatNumber(totalStats.completed)}
              </div>
              <div className="text-sm text-gray-500">已完成</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">
                {formatNumber(totalStats.failed)}
              </div>
              <div className="text-sm text-gray-500">失败任务</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 队列详情 */}
      {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {queueStats.map((queue) => (
          <QueueCard
            key={queue.queueName}
            queue={queue}
            onPause={() => onPauseQueue?.(queue.queueName)}
            onResume={() => onResumeQueue?.(queue.queueName)}
            onClear={() => onClearQueue?.(queue.queueName)}
            onRetryFailed={() => onRetryFailed?.(queue.queueName)}
          />
        ))}
      </div> */}
    </div>
  );
}
