{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "lib": ["ES2022", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/shared-types": ["shared-types/index"], "@/types/*": ["types/*"], "@/interfaces/*": ["interfaces/*"], "@/modules/*": ["modules/*"], "@/utils/*": ["utils/*"], "@/config/*": ["config/*"], "@/app/*": ["app/*"], "@/api/*": ["api/*"]}}, "include": ["src/**/*", "nodex.config.json"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}