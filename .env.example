# Nodex 环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# ==========================================
# 服务器配置
# ==========================================

# API 服务器配置
API_PORT=3000
API_HOST=0.0.0.0

# 开发模式下是否启用 CORS（生产环境建议设为 false）
ENABLE_CORS=true
CORS_ORIGIN=*

# 静态文件路径（生产环境自动设置，通常不需要修改）
# STATIC_PATH=dist/public

# ==========================================
# 环境设置
# ==========================================

# 运行环境: development, production, test
NODE_ENV=development

# 日志级别: error, warn, info, debug
LOG_LEVEL=info

# 是否启用详细日志
VERBOSE_LOGGING=false

# 禁用彩色输出 (设置为 1 禁用)
NO_COLOR=0

# ==========================================
# 数据库配置
# ==========================================

# MongoDB 连接字符串
MONGODB_URI=mongodb://localhost:27017/nodex

# Redis 连接配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ==========================================
# 爬虫配置
# ==========================================

# 默认并发数
DEFAULT_CONCURRENCY=3
MAX_CONCURRENT_REQUESTS=10

# 默认请求延迟（毫秒）
DEFAULT_DELAY=1000
REQUEST_DELAY_MS=100

# 默认超时时间（毫秒）
DEFAULT_TIMEOUT=30000

# 默认重试次数
DEFAULT_RETRIES=3

# 默认用户代理
DEFAULT_USER_AGENT="Nodex-Crawler/2.0.0"

# ==========================================
# 浏览器配置
# ==========================================

# 是否启用无头模式
HEADLESS_BROWSER=true
BROWSER_TIMEOUT=30000

# Chrome 可执行文件路径（Docker 环境自动设置）
# CHROME_EXECUTABLE_PATH=/usr/bin/chromium-browser

# 浏览器并发数限制
MAX_BROWSER_INSTANCES=3

# ==========================================
# 代理配置
# ==========================================

# 是否启用代理
ENABLE_PROXY=false
PROXY_ENABLED=false

# 代理服务器配置
PROXY_HOST=
PROXY_PORT=
PROXY_USERNAME=
PROXY_PASSWORD=

# 代理类型: http, https, socks4, socks5
PROXY_TYPE=http

# 代理列表URL
PROXY_LIST_URL=

# ==========================================
# AI 引擎配置
# ==========================================

# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Google Gemini API 配置
GOOGLE_API_KEY=your_gemini_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro

# ==========================================
# 安全配置
# ==========================================

# JWT 密钥（如果启用了认证）
JWT_SECRET=your-secret-key-here

# 会话密钥
SESSION_SECRET=your-session-secret-here

# API 速率限制（每分钟请求数）
RATE_LIMIT_PER_MINUTE=100

# ==========================================
# 存储配置
# ==========================================

# 默认存储类型: memory, mongodb, file
DEFAULT_STORAGE_TYPE=memory
STORAGE_TYPE=mongodb
STORAGE_COLLECTION=crawl_data

# 文件存储路径
FILE_STORAGE_PATH=./data

# 是否启用数据压缩
ENABLE_COMPRESSION=true

# ==========================================
# 搜索引擎配置
# ==========================================

# Elasticsearch 配置
ELASTICSEARCH_URL=http://localhost:9200

# MeiliSearch 配置
MEILISEARCH_URL=http://localhost:7700
MEILISEARCH_API_KEY=

# ==========================================
# 监控和告警配置
# ==========================================

# 是否启用性能监控
ENABLE_MONITORING=true

# 是否启用健康检查端点
ENABLE_HEALTH_CHECK=true

# Webhook 通知 URL
WEBHOOK_URL=

# 邮件通知配置
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
NOTIFICATION_EMAIL=

# ==========================================
# 开发配置
# ==========================================

# 是否启用 API 文档
ENABLE_API_DOCS=true

# 是否启用调试模式
DEBUG=false

# 是否启用热重载（开发模式）
HOT_RELOAD=true

# 配置文件路径
CONFIG_PATH=nodex.config.json

# ==========================================
# 性能配置
# ==========================================

# 内存限制（MB）
MEMORY_LIMIT=1024

# CPU 使用率限制（百分比）
CPU_LIMIT=80

# 最大文件大小（MB）
MAX_FILE_SIZE=10

# ==========================================
# 集群配置（高级用户）
# ==========================================

# 是否启用集群模式
ENABLE_CLUSTER=false

# 工作进程数量（0 = CPU 核心数）
WORKER_PROCESSES=0

# 主节点地址（集群模式）
MASTER_HOST=localhost
MASTER_PORT=3001

# ==========================================
# Docker 特定配置
# ==========================================

# 容器时区
TZ=Asia/Shanghai

# Puppeteer 配置（Docker 环境）
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
