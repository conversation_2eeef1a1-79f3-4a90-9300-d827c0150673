import React, { useEffect } from 'react';
import { SystemOverview } from '@/components/monitoring/SystemOverview';
import { QueueMonitor } from '@/components/monitoring/QueueMonitor';
import { PerformanceCharts } from '@/components/monitoring/PerformanceCharts';
import { ModuleStatus } from '@/components/monitoring/ModuleStatus';
import { SystemControl } from '@/components/monitoring/SystemControl';
import { SessionManager } from '@/components/monitoring/SessionManager';
import { LoadingState } from '@/components/ui/LoadingSpinner';
import { useMonitoring, useSystemControl } from '@/hooks/useMonitoring';

interface MonitoringPageProps {
  onSystemUpdate?: () => void;
}

export const MonitoringPage: React.FC<MonitoringPageProps> = ({ onSystemUpdate }) => {
  const {
    systemStats,
    queueStats,
    moduleStatus,
    sessions,
    loading,
    error,
    refreshData,
  } = useMonitoring();

  const {
    pauseQueue,
    resumeQueue,
    clearQueue,
    retryFailedJobs,
  } = useSystemControl();

  // 当系统状态更新时通知父组件
  useEffect(() => {
    if (systemStats && onSystemUpdate) {
      onSystemUpdate();
    }
  }, [systemStats, onSystemUpdate]);

  if (error && !systemStats) {
    return (
      <div className="min-h-screen bg-gray-50">
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <LoadingState loading={false} error={error}>
            <div />
          </LoadingState>
        </main>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 系统状态概览 */}
      <section>
        <SystemOverview
          systemStats={systemStats}
          loading={loading}
        />
      </section>

      {/* 主要监控区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* 左侧主要内容 */}
        <div className="lg:col-span-3 space-y-8">
          {/* 队列监控 */}
          <section>
            <QueueMonitor
              queueStats={queueStats}
              loading={loading}
              onPauseQueue={pauseQueue}
              onResumeQueue={resumeQueue}
              onClearQueue={clearQueue}
              onRetryFailed={retryFailedJobs}
            />
          </section>

          {/* 性能图表 */}
          <section>
            <PerformanceCharts
              systemStats={systemStats}
              loading={loading}
            />
          </section>

          {/* 会话管理 */}
          <section>
            <SessionManager
              sessions={sessions}
              loading={loading}
            />
          </section>
        </div>

        {/* 右侧边栏 */}
        <div className="lg:col-span-1 space-y-6">
          {/* 系统控制 */}
          {/* <SystemControl
            systemStatus={systemStats?.status}
            disabled={loading}
          /> */}

          {/* 模块状态 */}
          <ModuleStatus
            modules={moduleStatus}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
};