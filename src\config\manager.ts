/**
 * 配置管理器实现
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { merge } from 'lodash';
import {
  NodexConfig,
  IConfigManager,
  TaskConfig,
  CrawlScopeConfig,
  DepthControlConfig,
  ConcurrencyConfig,
  ProxyManagerConfig,
  ParserConfig,
  StorageConfig,
  QueueConfig,
  AIConfig,
  LogConfig,
  MonitoringConfig,
  LogLevel,
  StorageType,
} from '@/interfaces';
import { DeepPartial } from '@/types/utils';
import { defaultConfig } from './default';
import { ConfigValidator } from './validator';

/**
 * 配置管理器实现类
 */
export class ConfigManager implements IConfigManager {
  private config: NodexConfig;
  private validator: ConfigValidator;
  private configPath: string | undefined;

  constructor() {
    this.config = { ...defaultConfig };
    this.validator = new ConfigValidator();
  }

  public async loadConfig(configPath?: string): Promise<NodexConfig> {
    this.configPath = configPath;

    let config = { ...defaultConfig };

    config = this.loadEnvironmentConfig(config);

    if (configPath) {
      const fileConfig = await this.loadConfigFile(configPath);
      config = merge(config, fileConfig);
    }

    const validation = this.validator.validateConfig(config);
    if (!validation.success) {
      throw new Error(`Configuration validation failed: ${validation.errors?.join(', ')}`);
    }

    this.config = config;
    return this.config;
  }

  public getConfig(): NodexConfig {
    return { ...this.config };
  }

  public updateConfig(updates: DeepPartial<NodexConfig>): void {
    const newConfig = merge({}, this.config, updates);

    const validation = this.validator.validateConfig(newConfig);
    if (!validation.success) {
      throw new Error(`Configuration update validation failed: ${validation.errors?.join(', ')}`);
    }

    this.config = newConfig;
  }

  public validateConfig(config: NodexConfig): boolean {
    const validation = this.validator.validateConfig(config);
    return validation.success;
  }

  public async saveConfig(configPath?: string): Promise<void> {
    const targetPath = configPath ?? this.configPath;
    if (!targetPath) {
      throw new Error('No config path specified');
    }

    const configJson = JSON.stringify(this.config, null, 2);
    await fs.writeFile(targetPath, configJson, 'utf-8');
  }

  public async reloadConfig(): Promise<void> {
    await this.loadConfig(this.configPath);
  }

  private async loadConfigFile(configPath: string): Promise<DeepPartial<NodexConfig>> {
    try {
      const absolutePath = path.resolve(configPath);
      const configContent = await fs.readFile(absolutePath, 'utf-8');
      const ext = path.extname(configPath).toLowerCase();

      switch (ext) {
        case '.json':
          return JSON.parse(configContent);

        case '.js':
        case '.ts': {
          // 动态导入 JavaScript/TypeScript 配置文件
          const configModule = await import(absolutePath);
          return configModule.default ?? configModule;
        }

        default:
          throw new Error(`Unsupported config file format: ${ext}`);
      }
    } catch (error) {
      if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
        throw new Error(`Config file not found: ${configPath}`);
      }
      throw new Error(`Failed to load config file: ${error}`);
    }
  }

  private loadEnvironmentConfig(config: NodexConfig): NodexConfig {
    const envConfig = { ...config };

    // Redis 配置
    if (process.env.REDIS_HOST) {
      envConfig.queue.redis.host = process.env.REDIS_HOST;
    }
    if (process.env.REDIS_PORT) {
      envConfig.queue.redis.port = parseInt(process.env.REDIS_PORT, 10);
    }
    if (process.env.REDIS_PASSWORD) {
      envConfig.queue.redis.password = process.env.REDIS_PASSWORD;
    }
    if (process.env.REDIS_DB) {
      envConfig.queue.redis.db = parseInt(process.env.REDIS_DB, 10);
    }

    // MongoDB 配置
    if (process.env.MONGODB_URI) {
      envConfig.storage.connectionString = process.env.MONGODB_URI;
    }

    // AI 配置
    if (process.env.OPENAI_API_KEY) {
      if (!envConfig.ai.openai) {
        envConfig.ai.openai = {
          apiKey: process.env.OPENAI_API_KEY,
          baseUrl: 'https://api.openai.com/v1',
          model: 'gpt-3.5-turbo',
          maxTokens: 4000,
          temperature: 0.1,
        };
      } else {
        envConfig.ai.openai.apiKey = process.env.OPENAI_API_KEY;
      }
    }
    if (process.env.OPENAI_BASE_URL && envConfig.ai.openai) {
      envConfig.ai.openai.baseUrl = process.env.OPENAI_BASE_URL;
    }
    if (process.env.GEMINI_API_KEY) {
      if (!envConfig.ai.gemini) {
        envConfig.ai.gemini = {
          apiKey: process.env.GEMINI_API_KEY,
          model: 'gemini-pro',
          maxTokens: 4000,
          temperature: 0.1,
        };
      } else {
        envConfig.ai.gemini.apiKey = process.env.GEMINI_API_KEY;
      }
    }

    // 代理配置
    if (process.env.PROXY_ENABLED) {
      envConfig.proxy.enabled = process.env.PROXY_ENABLED.toLowerCase() === 'true';
    }

    // 应用配置
    if (process.env.NODE_ENV) {
      // 根据环境调整配置
      if (process.env.NODE_ENV === 'development') {
        envConfig.logging.level = LogLevel.DEBUG;
        envConfig.logging.console = true;
      } else if (process.env.NODE_ENV === 'production') {
        envConfig.logging.level = LogLevel.INFO;
        envConfig.logging.console = false;
      }
    }

    if (process.env.LOG_LEVEL) {
      envConfig.logging.level = process.env.LOG_LEVEL as LogLevel;
    }

    if (process.env.PORT) {
      envConfig.monitoring.port = parseInt(process.env.PORT, 10);
    }

    // 性能配置
    if (process.env.MAX_CONCURRENT_REQUESTS) {
      envConfig.concurrency.maxConcurrentRequests = parseInt(
        process.env.MAX_CONCURRENT_REQUESTS,
        10
      );
    }

    if (process.env.REQUEST_DELAY_MS) {
      envConfig.concurrency.delayBetweenRequestsMs = parseInt(process.env.REQUEST_DELAY_MS, 10);
    }

    // 浏览器配置
    if (process.env.HEADLESS_BROWSER) {
      // 这个配置将在下载器模块中使用
    }

    if (process.env.BROWSER_TIMEOUT) {
      envConfig.concurrency.requestTimeoutMs = parseInt(process.env.BROWSER_TIMEOUT, 10);
    }

    // 存储配置
    if (process.env.STORAGE_TYPE) {
      envConfig.storage.type = process.env.STORAGE_TYPE as StorageType;
    }

    if (process.env.STORAGE_COLLECTION) {
      envConfig.storage.collection = process.env.STORAGE_COLLECTION;
    }

    return envConfig;
  }

  /**
   * 获取配置的特定部分
   */
  public getTaskConfig(): TaskConfig {
    return this.config.task;
  }

  public getCrawlScopeConfig(): CrawlScopeConfig {
    return this.config.crawlScope;
  }

  public getDepthControlConfig(): DepthControlConfig {
    return this.config.depthControl;
  }

  public getConcurrencyConfig(): ConcurrencyConfig {
    return this.config.concurrency;
  }

  public getProxyConfig(): ProxyManagerConfig {
    return this.config.proxy;
  }

  public getParsersConfig(): ParserConfig[] {
    return this.config.parsers ?? [];
  }

  public getStorageConfig(): StorageConfig {
    return this.config.storage;
  }

  public getQueueConfig(): QueueConfig {
    return this.config.queue;
  }

  public getAIConfig(): AIConfig {
    return this.config.ai;
  }

  public getLoggingConfig(): LogConfig {
    return this.config.logging;
  }

  public getMonitoringConfig(): MonitoringConfig {
    return this.config.monitoring;
  }

  /**
   * 创建配置的深拷贝
   */
  public cloneConfig(): NodexConfig {
    return JSON.parse(JSON.stringify(this.config));
  }

  /**
   * 合并配置
   */
  public mergeConfig(updates: DeepPartial<NodexConfig>): NodexConfig {
    return merge({}, this.config, updates);
  }

  /**
   * 重置为默认配置
   */
  public resetToDefault(): void {
    this.config = { ...defaultConfig };
  }
}
