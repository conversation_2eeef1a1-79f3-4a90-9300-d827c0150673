version: '3.8'

services:
  # Redis 服务 - 任务队列
  redis:
    image: redis:7-alpine
    container_name: nodex-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3

  # MongoDB 服务 - 数据存储
  mongodb:
    image: mongo:7
    container_name: nodex-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: nodex
    volumes:
      - mongodb_data:/data/db
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 3s
      retries: 3

  # Nodex 统一API服务器
  nodex-api:
    build: .
    container_name: nodex-api
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      API_PORT: 3000
      API_HOST: 0.0.0.0
      REDIS_HOST: redis
      REDIS_PORT: 6379
      MONGODB_URI: *************************************************************
      ENABLE_CORS: true
      CORS_ORIGIN: "*"
      SERVE_FRONTEND: true
    depends_on:
      redis:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./nodex.config.json:/app/nodex.config.json:ro

  # Nodex Worker (可扩展) - 暂时禁用，新架构中由API服务器统一管理
  # nodex-worker:
  #   build: .
  #   restart: unless-stopped
  #   environment:
  #     NODE_ENV: production
  #     REDIS_HOST: redis
  #     REDIS_PORT: 6379
  #     MONGODB_URI: *************************************************************
  #     WORKER_MODE: true
  #   depends_on:
  #     redis:
  #       condition: service_healthy
  #     mongodb:
  #       condition: service_healthy
  #   deploy:
  #     replicas: 2
  #   volumes:
  #     - ./logs:/app/logs
  #     - ./data:/app/data

volumes:
  redis_data:
  mongodb_data:
