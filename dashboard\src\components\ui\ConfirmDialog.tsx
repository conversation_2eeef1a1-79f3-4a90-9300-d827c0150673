import React from 'react';
import { X, AlertTriangle } from 'lucide-react';
import { Button } from './Button';

interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  variant?: 'danger' | 'warning' | 'info';
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  title,
  message,
  confirmText = '确认',
  cancelText = '取消',
  onConfirm,
  onCancel,
  variant = 'danger',
}) => {
  if (!isOpen) return null;

  const variantStyles = {
    danger: {
      icon: 'text-red-500',
      button: 'danger' as const,
      bg: 'bg-red-50',
      border: 'border-red-200',
    },
    warning: {
      icon: 'text-yellow-500',
      button: 'warning' as const,
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
    },
    info: {
      icon: 'text-blue-500',
      button: 'primary' as const,
      bg: 'bg-blue-50',
      border: 'border-blue-200',
    },
  };

  const styles = variantStyles[variant];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onCancel}
      />
      
      {/* 弹框内容 */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all">
        {/* 关闭按钮 */}
        <button
          onClick={onCancel}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="h-5 w-5" />
        </button>

        <div className="p-6">
          {/* 图标和标题 */}
          <div className="flex items-center mb-4">
            <div className={`flex-shrink-0 w-10 h-10 rounded-full ${styles.bg} ${styles.border} flex items-center justify-center`}>
              <AlertTriangle className={`h-5 w-5 ${styles.icon}`} />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-gray-900">
                {title}
              </h3>
            </div>
          </div>

          {/* 消息内容 */}
          <div className="mb-6">
            <p className="text-sm text-gray-600 whitespace-pre-line">
              {message}
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <Button
              onClick={onCancel}
              variant="ghost"
              size="sm"
            >
              {cancelText}
            </Button>
            <Button
              onClick={onConfirm}
              variant={styles.button}
              size="sm"
            >
              {confirmText}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDialog;