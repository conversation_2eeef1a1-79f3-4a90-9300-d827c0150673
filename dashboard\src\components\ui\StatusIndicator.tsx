import React from 'react';
import { LoadingSpinner } from './LoadingSpinner';

interface StatusIndicatorProps {
  loading?: boolean;
  error?: string | null;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  loading = false,
  error = null,
  size = 'sm',
  className = ''
}) => {
  if (!loading && !error) return null;

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {loading && (
        <div className="flex items-center space-x-1">
          <LoadingSpinner size={size} />
          <span className="text-sm text-gray-500">更新中...</span>
        </div>
      )}
      
      {error && !loading && (
        <div className="flex items-center space-x-1 text-red-600">
          <svg 
            className={`${sizeClasses[size]} flex-shrink-0`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" 
            />
          </svg>
          <span className="text-sm text-red-600" title={error}>
            更新失败
          </span>
        </div>
      )}
    </div>
  );
};