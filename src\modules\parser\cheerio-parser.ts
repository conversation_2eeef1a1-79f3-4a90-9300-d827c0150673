/**
 * Cheerio 解析器实现
 */

import * as cheerio from 'cheerio';
import {
  IParser,
  ParserInput,
  ParserOutput,
  CheerioParserOptions,
} from '@/interfaces';

/**
 * Cheerio 解析器实现类
 */
export class CheerioParser implements IParser {
  public readonly name = 'cheerio-parser';
  public readonly version = '1.0.0';
  public readonly description = 'HTML/XML parser using Cheerio library';

  private options: CheerioParserOptions;

  constructor(options: CheerioParserOptions) {
    this.options = {
      extractLinks: true,
      linkSelector: 'a[href]',
      linkAttribute: 'href',
      cleanText: true,
      ...options,
    };
  }

  /**
   * 判断此解析器是否适用于该页面
   */
  public canParse(input: ParserInput): boolean {
    // 检查内容类型
    const contentType = input.headers?.['content-type'] || '';
    const isHtml = contentType.includes('text/html') || 
                   contentType.includes('application/xhtml+xml') ||
                   contentType.includes('text/xml') ||
                   contentType.includes('application/xml');

    // 检查URL扩展名
    const url = new URL(input.url);
    const pathname = url.pathname.toLowerCase();
    const hasHtmlExtension = pathname.endsWith('.html') || 
                            pathname.endsWith('.htm') || 
                            pathname.endsWith('.xml') ||
                            pathname === '/' ||
                            !pathname.includes('.');

    return isHtml || hasHtmlExtension;
  }

  /**
   * 执行解析
   */
  public async parse(input: ParserInput): Promise<ParserOutput> {
    const startTime = Date.now();
    
    try {
      // 加载HTML内容
      const $ = cheerio.load(input.content, {
        xmlMode: this.isXmlContent(input),
        decodeEntities: true,
      });

      // 提取数据
      const extractedData = this.extractData($ as any, input);

      // 提取链接
      const newUrls = this.options.extractLinks ? this.extractLinks($ as any, input) : [];
      
      const parseTime = Date.now() - startTime;
      
      return {
        extractedData,
        newUrls,
        metadata: {
          parserName: this.name,
          parseTime,
          extractedFieldsCount: extractedData ? Object.keys(extractedData).length : 0,
          discoveredUrlsCount: newUrls.length,
        },
      };
      
    } catch (error) {
      return {
        extractedData: null,
        newUrls: [],
        error: {
          message: error instanceof Error ? error.message : 'Unknown parsing error',
          code: 'PARSE_ERROR',
          details: error,
        },
      };
    }
  }

  /**
   * 提取数据
   */
  private extractData($: cheerio.CheerioAPI, input: ParserInput): Record<string, any> | null {
    const data: Record<string, any> = {};
    
    // 使用配置的选择器提取数据
    for (const [field, selector] of Object.entries(this.options.selectors)) {
      try {
        const elements = $(selector);
        
        if (elements.length === 0) {
          data[field] = null;
          continue;
        }

        if (elements.length === 1) {
          // 单个元素
          data[field] = this.extractElementValue($, elements.first(), field);
        } else {
          // 多个元素
          data[field] = elements.map((_, element) => {
            return this.extractElementValue($, $(element), field);
          }).get();
        }
      } catch (error) {
        console.warn(`Failed to extract field '${field}' with selector '${selector}':`, error);
        data[field] = null;
      }
    }

    // 如果没有配置选择器，提取一些默认信息
    if (Object.keys(this.options.selectors).length === 0) {
      data.title = this.extractText($, $('title').first());
      data.description = this.extractText($, $('meta[name="description"]').attr('content') || '');
      data.keywords = this.extractText($, $('meta[name="keywords"]').attr('content') || '');
      data.h1 = this.extractText($, $('h1').first());
      data.h2 = $('h2').map((_, el) => this.extractText($, $(el))).get();
    }

    return Object.keys(data).length > 0 ? data : null;
  }

  /**
   * 提取元素值
   */
  private extractElementValue($: cheerio.CheerioAPI, element: cheerio.Cheerio, field: string): any {
    // 检查是否有自定义处理函数
    if (this.options.customProcessors && this.options.customProcessors[field]) {
      return this.options.customProcessors[field](element);
    }

    // 默认提取逻辑
    const tagName = element.prop('tagName')?.toLowerCase();
    
    switch (tagName) {
      case 'img':
        return {
          src: element.attr('src'),
          alt: element.attr('alt'),
          title: element.attr('title'),
        };
      
      case 'a':
        return {
          href: element.attr('href'),
          text: this.extractText($, element),
          title: element.attr('title'),
        };
      
      case 'input':
        return {
          type: element.attr('type'),
          name: element.attr('name'),
          value: element.attr('value'),
          placeholder: element.attr('placeholder'),
        };
      
      default:
        // 尝试获取常见属性
        const commonAttrs = ['href', 'src', 'alt', 'title', 'data-*'];
        const attrs: Record<string, any> = {};
        
        commonAttrs.forEach(attr => {
          const value = element.attr(attr);
          if (value) {
            attrs[attr] = value;
          }
        });

        const text = this.extractText($, element);
        
        if (Object.keys(attrs).length > 0) {
          return {
            text,
            attributes: attrs,
          };
        }
        
        return text;
    }
  }

  /**
   * 提取文本内容
   */
  private extractText($: cheerio.CheerioAPI, element: cheerio.Cheerio | string): string {
    if (typeof element === 'string') {
      return this.options.cleanText ? this.cleanText(element) : element;
    }
    
    const text = element.text();
    return this.options.cleanText ? this.cleanText(text) : text;
  }

  /**
   * 清理文本
   */
  private cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // 合并多个空白字符
      .replace(/\n\s*\n/g, '\n') // 合并多个换行符
      .trim(); // 去除首尾空白
  }

  /**
   * 提取链接
   */
  private extractLinks($: cheerio.CheerioAPI, input: ParserInput): string[] {
    const links: string[] = [];
    const baseUrl = new URL(input.url);
    
    $(this.options.linkSelector || 'a[href]').each((_, element) => {
      try {
        const href = $(element).attr(this.options.linkAttribute || 'href');
        if (href) {
          // 解析相对URL为绝对URL
          const absoluteUrl = new URL(href, baseUrl).toString();
          
          // 过滤掉一些不需要的链接
          if (this.isValidLink(absoluteUrl)) {
            links.push(absoluteUrl);
          }
        }
      } catch (error) {
        // 忽略无效的URL
      }
    });

    // 去重
    return [...new Set(links)];
  }

  /**
   * 验证链接是否有效
   */
  private isValidLink(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      
      // 只接受 HTTP 和 HTTPS 协议
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return false;
      }
      
      // 过滤掉一些常见的无用链接
      const pathname = parsedUrl.pathname.toLowerCase();
      const excludePatterns = [
        /\.(css|js|ico|png|jpg|jpeg|gif|svg|pdf|zip|rar|exe)$/i,
        /^mailto:/i,
        /^tel:/i,
        /^javascript:/i,
        /^#/,
      ];
      
      for (const pattern of excludePatterns) {
        if (pattern.test(url) || pattern.test(pathname)) {
          return false;
        }
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 判断是否为XML内容
   */
  private isXmlContent(input: ParserInput): boolean {
    const contentType = input.headers?.['content-type'] || '';
    return contentType.includes('xml') || input.content.trim().startsWith('<?xml');
  }

  /**
   * 更新选择器配置
   */
  public updateSelectors(selectors: Record<string, string>): void {
    this.options.selectors = { ...this.options.selectors, ...selectors };
  }

  /**
   * 添加自定义处理函数
   */
  public addCustomProcessor(field: string, processor: (element: any) => any): void {
    if (!this.options.customProcessors) {
      this.options.customProcessors = {};
    }
    this.options.customProcessors[field] = processor;
  }

  /**
   * 获取当前配置
   */
  public getOptions(): CheerioParserOptions {
    return { ...this.options };
  }

  /**
   * 设置配置
   */
  public setOptions(options: Partial<CheerioParserOptions>): void {
    this.options = { ...this.options, ...options };
  }
}
