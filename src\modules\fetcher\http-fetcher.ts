/**
 * HTTP 下载器实现
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  IHttpFetcher,
  HttpRequestOptions,
  FetchResponse,
  ProxyConfig,
  HttpMethod,
} from '@/interfaces';
import { info } from '@/utils';

/**
 * HTTP 下载器实现类
 */
export class HttpFetcher implements IHttpFetcher {
  public readonly name = 'http-fetcher';
  public readonly type = 'http' as const;
  public readonly version = '1.0.0';

  private client: AxiosInstance;
  private stats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    totalResponseTime: 0,
  };

  constructor() {
    this.client = axios.create({
      timeout: 30000,
      maxRedirects: 5,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      },
    });
  }

  /**
   * 执行 HTTP 请求
   */
  public async fetch(url: string, options: HttpRequestOptions = {}): Promise<FetchResponse> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      // 构建请求选项
      const requestOptions = this.buildRequestOptions(url, options);

      // 执行请求
      const response: AxiosResponse<string> = await this.client(requestOptions);

      const responseTime = Date.now() - startTime;
      this.stats.successfulRequests++;
      this.stats.totalResponseTime += responseTime;
      // 构建响应对象
      return this.buildFetchResponse(response, responseTime, url);

    } catch (error) {
      console.error(error)
      const responseTime = Date.now() - startTime;
      this.stats.failedRequests++;
      this.stats.totalResponseTime += responseTime;

      throw this.handleError(error, url, responseTime);
    }
  }

  /**
   * 检查是否支持该URL
   */
  public canHandle(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * 初始化下载器
   */
  public async initialize(): Promise<void> {
    // HTTP 下载器不需要特殊初始化
    info('HTTP Fetcher initialized');
  }

  /**
   * 清理下载器资源
   */
  public async cleanup(): Promise<void> {
    // HTTP 下载器不需要特殊清理
    info('HTTP Fetcher cleaned up');
  }

  /**
   * 获取下载器统计信息
   */
  public getStats() {
    return {
      totalRequests: this.stats.totalRequests,
      successfulRequests: this.stats.successfulRequests,
      failedRequests: this.stats.failedRequests,
      averageResponseTime: this.stats.totalRequests > 0
        ? this.stats.totalResponseTime / this.stats.totalRequests
        : 0,
    };
  }

  /**
   * 构建请求选项
   */
  private buildRequestOptions(url: string, options: HttpRequestOptions): any {
    const requestOptions: any = {
      url,
      method: options.method || HttpMethod.GET,
      headers: { ...options.headers },
      timeout: options.timeout || 30000,
      maxRedirects: options.maxRedirects || 5,
      validateStatus: () => true, // 我们手动处理 HTTP 错误
    };

    // 设置用户代理
    if (options.userAgent) {
      requestOptions.headers['User-Agent'] = options.userAgent;
    }

    // 设置请求体
    if (options.body) {
      if (typeof options.body === 'string') {
        requestOptions.data = options.body;
      } else if (Buffer.isBuffer(options.body)) {
        requestOptions.data = options.body;
      } else {
        // 将对象序列化为JSON字符串
        requestOptions.data = JSON.stringify(options.body);
        // 确保Content-Type正确设置
        if (!requestOptions.headers['Content-Type'] && !requestOptions.headers['content-type']) {
          requestOptions.headers['Content-Type'] = 'application/json';
        }
      }
    }

    // 设置 Cookie
    if (options.cookies) {
      const cookieString = Object.entries(options.cookies)
        .map(([key, value]) => `${key}=${value}`)
        .join('; ');
      requestOptions.headers.Cookie = cookieString;
    }

    // 设置代理
    if (options.proxy) {
      requestOptions.proxy = {
        protocol: options.proxy.protocol || 'http',
        host: options.proxy.host,
        port: options.proxy.port,
        auth: options.proxy.auth ? {
          username: options.proxy.auth.username,
          password: options.proxy.auth.password,
        } : undefined,
      };
    }

    // SSL 验证
    if (options.validateSSL === false) {
      requestOptions.httpsAgent = new (require('https').Agent)({
        rejectUnauthorized: false,
      });
    }

    return requestOptions;
  }



  /**
   * 构建响应对象
   */
  private buildFetchResponse(response: AxiosResponse<string>, responseTime: number, originalUrl: string): FetchResponse {
    const size = typeof response.data === 'string' ? Buffer.byteLength(response.data, 'utf8') : 0;

    return {
      content: response.data,
      statusCode: response.status,
      headers: response.headers as Record<string, string>,
      contentType: response.headers['content-type'] || 'text/html',
      size,
      responseTime,
      finalUrl: response.config.url || originalUrl,
      fetcherType: 'http',
      fromCache: false,
      metadata: {
        httpVersion: '1.1',
        redirectUrls: [],
        retryCount: 0,
      },
    };
  }

  /**
   * 处理错误
   */
  private handleError(error: any, url: string, responseTime: number): Error {
    let message = 'HTTP request failed';
    let code = 'HTTP_ERROR';

    if (error.code) {
      code = error.code;
      message = error.message || message;
    } else if (error.response) {
      code = `HTTP_${error.response.status}`;
      message = `HTTP ${error.response.status}: ${error.response.statusText || 'Unknown error'}`;
    } else if (error.message) {
      message = error.message;
    }

    const enhancedError = new Error(`${message} (URL: ${url})`);
    (enhancedError as any).code = code;
    (enhancedError as any).url = url;
    (enhancedError as any).responseTime = responseTime;
    (enhancedError as any).originalError = error;

    return enhancedError;
  }

  /**
   * 设置全局请求头
   */
  public setDefaultHeaders(headers: Record<string, string>): void {
    this.client.defaults.headers.common = {
      ...this.client.defaults.headers.common,
      ...headers,
    };
  }

  /**
   * 设置全局超时
   */
  public setDefaultTimeout(timeout: number): void {
    this.client.defaults.timeout = timeout;
  }

  /**
   * 设置全局用户代理
   */
  public setDefaultUserAgent(userAgent: string): void {
    this.client.defaults.headers.common['User-Agent'] = userAgent;
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalResponseTime: 0,
    };
  }

  /**
   * 测试连接
   */
  public async testConnection(url = 'https://httpbin.org/get'): Promise<boolean> {
    try {
      await this.fetch(url, { timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }
}
