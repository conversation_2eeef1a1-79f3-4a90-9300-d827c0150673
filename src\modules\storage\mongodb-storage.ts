/**
 * MongoDB 存储实现
 */

import { MongoClient, Db, Collection, CreateIndexesOptions } from 'mongodb';
import {
  IStorage,
  StorageType,
  StorageItem,
  QueryOptions,
  QueryResult,
  BulkOperationResult,
  StorageStats,
  QueryCondition,
} from '@/interfaces';
import { getLogger, info, warn } from '@/utils';

/**
 * MongoDB 存储实现类
 */
export class MongoDBStorage implements IStorage {
  public readonly type: StorageType = StorageType.MONGODB;
  public readonly name = 'mongodb-storage';

  private client: MongoClient | null = null;
  private db: Db | null = null;
  private collection: Collection<StorageItem> | null = null;
  private logger = getLogger('MongoDBStorage');
  private connectionString: string;
  private databaseName: string;
  private collectionName: string;
  private options: any;

  constructor(config: {
    connectionString: string;
    database: string;
    collection: string;
    options?: any;
  }) {
    this.connectionString = config.connectionString;
    this.databaseName = config.database;
    this.collectionName = config.collection;
    this.options = config.options || {};
  }

  /**
   * 初始化存储
   */
  public async initialize(): Promise<void> {
    try {
      this.client = new MongoClient(this.connectionString, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        ...this.options,
      });

      await this.client.connect();
      this.db = this.client.db(this.databaseName);
      this.collection = this.db.collection<StorageItem>(this.collectionName);

      // 创建基础索引
      await this.createBasicIndexes();

      this.logger.info('MongoDB 存储初始化成功', {
        database: this.databaseName,
        collection: this.collectionName,
        connectionString: this.connectionString.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'), // 隐藏密码
      });
    } catch (error) {
      this.logger.error('MongoDB 存储初始化失败', {
        error: error instanceof Error ? error.message : error,
        database: this.databaseName,
        collection: this.collectionName,
      });
      throw error;
    }
  }

  /**
   * 关闭存储连接
   */
  public async close(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.db = null;
      this.collection = null;
      this.logger.info('MongoDB 存储连接已关闭');
    }
  }

  /**
   * 保存单个项目
   */
  public async save(item: StorageItem): Promise<string> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    try {
      const document = {
        ...item,
        _id: item.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = await this.collection.insertOne(document as any);
      return result.insertedId.toString();
    } catch (error) {
      if ((error as any).code === 11000) {
        // 重复键错误，尝试更新
        return await this.upsert(item);
      }
      throw error;
    }
  }

  /**
   * 批量保存项目
   */
  public async saveBatch(items: StorageItem[]): Promise<BulkOperationResult> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const startTime = Date.now();
    let successCount = 0;
    let failureCount = 0;
    const failures: Array<{ item: StorageItem; error: string }> = [];

    try {
      const documents = items.map(item => ({
        ...item,
        _id: item.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      const result = await this.collection.insertMany(documents as any, { ordered: false });
      successCount = result.insertedCount;
      failureCount = items.length - successCount;

    } catch (error: any) {
      if (error.writeErrors) {
        successCount = items.length - error.writeErrors.length;
        failureCount = error.writeErrors.length;

        for (const writeError of error.writeErrors) {
          const item = items[writeError.index];
          if (item) {
            failures.push({
              item,
              error: writeError.errmsg,
            });
          }
        }
      } else {
        failureCount = items.length;
        failures.push(...items.map(item => ({ item, error: error.message })));
      }
    }

    return {
      successCount,
      failureCount,
      failures: failures.length > 0 ? failures : undefined,
      operationTime: Date.now() - startTime,
    };
  }

  /**
   * 根据ID获取项目
   */
  public async getById(id: string): Promise<StorageItem | null> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const document = await this.collection.findOne({ _id: id } as any);
    return document ? this.documentToStorageItem(document) : null;
  }

  /**
   * 根据URL获取项目
   */
  public async getByUrl(url: string): Promise<StorageItem | null> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const document = await this.collection.findOne({ url });
    return document ? this.documentToStorageItem(document) : null;
  }

  /**
   * 查询项目
   */
  public async query(options: QueryOptions): Promise<QueryResult> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const startTime = Date.now();

    try {
      // 构建查询条件
      const filter = this.buildMongoFilter(options.conditions || []);

      // 构建排序
      const sort = this.buildMongoSort(options.sort || []);

      // 构建投影
      const projection = this.buildMongoProjection(options.projection, options.includeRawContent);

      // 执行查询
      const cursor = this.collection.find(filter, { projection });

      if (sort && Object.keys(sort).length > 0) {
        cursor.sort(sort);
      }

      if (options.skip) {
        cursor.skip(options.skip);
      }

      if (options.limit) {
        cursor.limit(options.limit);
      }

      const documents = await cursor.toArray();
      const total = await this.collection.countDocuments(filter);

      const data = documents.map(doc => this.documentToStorageItem(doc));

      return {
        data,
        total,
        page: options.skip && options.limit ? Math.floor(options.skip / options.limit) + 1 : undefined,
        pageSize: options.limit,
        queryTime: Date.now() - startTime,
      };

    } catch (error) {
      throw new Error(`Query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 更新项目
   */
  public async update(id: string, updates: Partial<StorageItem>): Promise<boolean> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const updateDoc = {
      ...updates,
      updatedAt: new Date(),
    };

    const result = await this.collection.updateOne(
      { _id: id } as any,
      { $set: updateDoc }
    );

    return result.modifiedCount > 0;
  }

  /**
   * 删除项目
   */
  public async delete(id: string): Promise<boolean> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const result = await this.collection.deleteOne({ _id: id } as any);
    return result.deletedCount > 0;
  }

  /**
   * 批量删除项目
   */
  public async deleteBatch(ids: string[]): Promise<BulkOperationResult> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const startTime = Date.now();

    try {
      const result = await this.collection.deleteMany({
        _id: { $in: ids }
      } as any);

      return {
        successCount: result.deletedCount || 0,
        failureCount: ids.length - (result.deletedCount || 0),
        operationTime: Date.now() - startTime,
      };

    } catch (error) {
      return {
        successCount: 0,
        failureCount: ids.length,
        failures: ids.map(id => ({
          item: { id, url: '', data: {}, depth: 0, crawledAt: new Date() },
          error: error instanceof Error ? error.message : 'Unknown error',
        })),
        operationTime: Date.now() - startTime,
      };
    }
  }

  /**
   * 检查项目是否存在
   */
  public async exists(url: string): Promise<boolean> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const count = await this.collection.countDocuments({ url }, { limit: 1 });
    return count > 0;
  }

  /**
   * 获取存储统计信息
   */
  public async getStats(): Promise<StorageStats> {
    if (!this.collection || !this.db) {
      throw new Error('MongoDB storage is not initialized');
    }

    try {
      const totalRecords = await this.collection.countDocuments();
      const stats = await this.db.stats();
      
      // 获取集合统计信息
      const collStats = await this.db.command({ collStats: this.collectionName });

      // 计算平均文档大小
      const averageDocumentSize = totalRecords > 0 ? collStats.avgObjSize || 0 : 0;

      return {
        totalRecords,
        storageSize: collStats.storageSize || 0,
        indexSize: collStats.totalIndexSize || 0,
        averageDocumentSize,
        lastUpdated: new Date(),
      };

    } catch (error) {
      throw new Error(`Failed to get storage stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 创建索引
   */
  public async createIndex(field: string, options?: { unique?: boolean; sparse?: boolean }): Promise<void> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const indexSpec: any = { [field]: 1 };
    const indexOptions: CreateIndexesOptions = {};

    if (options?.unique) {
      indexOptions.unique = true;
    }

    if (options?.sparse) {
      indexOptions.sparse = true;
    }

    await this.collection.createIndex(indexSpec, indexOptions);
    info(`Created index on field: ${field}`);
  }

  /**
   * 清空存储
   */
  public async clear(): Promise<void> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    await this.collection.deleteMany({});
    info('Storage cleared');
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.client || !this.db) {
        return false;
      }

      await this.db.admin().ping();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 创建基础索引
   */
  private async createBasicIndexes(): Promise<void> {
    if (!this.collection) {
      return;
    }

    try {
      // URL 索引（唯一）
      await this.collection.createIndex({ url: 1 }, { unique: true });
      
      // 抓取时间索引
      await this.collection.createIndex({ crawledAt: -1 });
      
      // 深度索引
      await this.collection.createIndex({ depth: 1 });
      
      // 标签索引
      await this.collection.createIndex({ tags: 1 });
      
      // 复合索引：域名 + 抓取时间
      await this.collection.createIndex({ 'metadata.domain': 1, crawledAt: -1 });

      info('Basic indexes created');
    } catch (error) {
      warn('Failed to create some indexes:', error);
    }
  }

  /**
   * Upsert 操作
   */
  private async upsert(item: StorageItem): Promise<string> {
    if (!this.collection) {
      throw new Error('MongoDB storage is not initialized');
    }

    const document = {
      ...item,
      updatedAt: new Date(),
    };

    const result = await this.collection.replaceOne(
      { url: item.url },
      document as any,
      { upsert: true }
    );

    return result.upsertedId?.toString() || item.id || '';
  }

  /**
   * 构建 MongoDB 查询过滤器
   */
  private buildMongoFilter(conditions: QueryCondition[]): any {
    const filter: any = {};

    for (const condition of conditions) {
      const { field, operator, value } = condition;

      switch (operator) {
        case 'eq':
          filter[field] = value;
          break;
        case 'ne':
          filter[field] = { $ne: value };
          break;
        case 'gt':
          filter[field] = { $gt: value };
          break;
        case 'gte':
          filter[field] = { $gte: value };
          break;
        case 'lt':
          filter[field] = { $lt: value };
          break;
        case 'lte':
          filter[field] = { $lte: value };
          break;
        case 'in':
          filter[field] = { $in: Array.isArray(value) ? value : [value] };
          break;
        case 'nin':
          filter[field] = { $nin: Array.isArray(value) ? value : [value] };
          break;
        case 'regex':
          filter[field] = { $regex: value, $options: 'i' };
          break;
        case 'exists':
          filter[field] = { $exists: Boolean(value) };
          break;
      }
    }

    return filter;
  }

  /**
   * 构建 MongoDB 排序
   */
  private buildMongoSort(sort: Array<{ field: string; direction: 'asc' | 'desc' }>): any {
    const mongoSort: any = {};

    for (const { field, direction } of sort) {
      mongoSort[field] = direction === 'asc' ? 1 : -1;
    }

    return mongoSort;
  }

  /**
   * 构建 MongoDB 投影
   */
  private buildMongoProjection(projection?: string[], includeRawContent?: boolean): any {
    if (!projection || projection.length === 0) {
      return includeRawContent ? {} : { rawContent: 0 };
    }

    const mongoProjection: any = {};
    for (const field of projection) {
      mongoProjection[field] = 1;
    }

    if (!includeRawContent && !projection.includes('rawContent')) {
      mongoProjection.rawContent = 0;
    }

    return mongoProjection;
  }

  /**
   * 将 MongoDB 文档转换为 StorageItem
   */
  private documentToStorageItem(document: any): StorageItem {
    const { _id, createdAt, updatedAt, ...rest } = document;
    return {
      id: _id?.toString(),
      ...rest,
    };
  }
}
