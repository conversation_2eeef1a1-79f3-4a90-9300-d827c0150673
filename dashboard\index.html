<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="高性能智能爬虫系统监控面板 - 基于 Vite + React 的现代化监控界面" />
    <meta name="keywords" content="爬虫,监控,Node.js,TypeScript,React,Vite" />
    <meta name="author" content="Nodex Team" />
    <meta name="theme-color" content="#3b82f6" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Nodex 监控面板" />
    <meta property="og:description" content="高性能智能爬虫系统监控面板" />
    <meta property="og:locale" content="zh_CN" />
    
    <!-- Preconnect to Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    
    <title>Nodex 监控面板</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
