/**
 * 类型定义测试
 */

import {
  TraversalStrategy,
  FetcherType,
  StorageType,
  ProxyStrategy,
  TaskStatus,
  LogLevel,
  HttpMethod,
} from './common';

import type {
  NodexConfig,
  TaskConfig,
  CrawlScopeConfig,
  DepthControlConfig,
  ConcurrencyConfig,
} from '@/interfaces/config';

import type {
  IParser,
  ParserInput,
  ParserOutput,
  IParserManager,
} from '@/interfaces/parser';

import type {
  CrawlTask,
  FetchTask,
  ParseTask,
  TaskResult,
  ITaskScheduler,
} from '@/interfaces/task';

import type {
  <PERSON>etcher,
  IHttpFetcher,
  IBrowserFetcher,
  FetchResponse,
  IProxyManager,
} from '@/interfaces/fetcher';

import type {
  IStorage,
  StorageItem,
  QueryOptions,
  QueryResult,
  ISearchEngine,
} from '@/interfaces/storage';

import type {
  IAIEngine,
  IAIProvider,
  ContentExtractionRequest,
  ContentExtractionResponse,
  CrawlPlanRequest,
  CrawlPlanResponse,
} from '@/interfaces/ai';

import type {
  IMasterScheduler,
  IModuleManager,
  IModule,
  SystemStatus,
  SystemStats,
  CrawlSession,
} from '@/interfaces/master';

describe('Type Definitions', () => {
  describe('Enums', () => {
    it('should have correct enum values', () => {
      expect(TraversalStrategy.BFS).toBe('BFS');
      expect(TraversalStrategy.DFS).toBe('DFS');
      
      expect(FetcherType.HTTP).toBe('http');
      expect(FetcherType.BROWSER).toBe('browser');
      
      expect(StorageType.MONGODB).toBe('mongodb');
      expect(StorageType.POSTGRESQL).toBe('postgresql');
      
      expect(ProxyStrategy.ROUND_ROBIN).toBe('round-robin');
      expect(ProxyStrategy.RANDOM).toBe('random');
      
      expect(TaskStatus.PENDING).toBe('pending');
      expect(TaskStatus.PROCESSING).toBe('processing');
      
      expect(LogLevel.ERROR).toBe('error');
      expect(LogLevel.INFO).toBe('info');
      
      expect(HttpMethod.GET).toBe('GET');
      expect(HttpMethod.POST).toBe('POST');
    });
  });

  describe('Interface Compatibility', () => {
    it('should allow creating valid config objects', () => {
      const taskConfig: TaskConfig = {
        seedUrls: ['https://example.com'],
        traversalStrategy: TraversalStrategy.BFS,
        name: 'test-task',
      };

      const crawlScopeConfig: CrawlScopeConfig = {
        allowedDomains: ['example.com'],
        disallowedPatterns: [/logout/],
        followSubdomains: true,
        ignoreWWW: true,
      };

      const depthControlConfig: DepthControlConfig = {
        global: 5,
        perDomain: {
          'example.com': 3,
        },
      };

      const concurrencyConfig: ConcurrencyConfig = {
        maxConcurrentRequests: 10,
        delayBetweenRequestsMs: 100,
      };

      expect(taskConfig.traversalStrategy).toBe(TraversalStrategy.BFS);
      expect(crawlScopeConfig.allowedDomains).toContain('example.com');
      expect(depthControlConfig.global).toBe(5);
      expect(concurrencyConfig.maxConcurrentRequests).toBe(10);
    });

    it('should allow creating valid task objects', () => {
      const crawlTask: CrawlTask = {
        id: 'task-1',
        url: {
          url: 'https://example.com',
          depth: 0,
          domain: 'example.com',
          timestamp: new Date(),
          retryCount: 0,
          priority: 1,
        },
        status: TaskStatus.PENDING,
        createdAt: new Date(),
        priority: 1,
        retryCount: 0,
        maxRetries: 3,
      };

      expect(crawlTask.status).toBe(TaskStatus.PENDING);
      expect(crawlTask.url.domain).toBe('example.com');
    });

    it('should allow creating valid parser input/output', () => {
      const parserInput: ParserInput = {
        content: '<html><body>Test</body></html>',
        url: 'https://example.com',
        fetcherType: FetcherType.HTTP,
        depth: 0,
      };

      const parserOutput: ParserOutput = {
        extractedData: { title: 'Test Page' },
        newUrls: ['https://example.com/page2'],
      };

      expect(parserInput.fetcherType).toBe(FetcherType.HTTP);
      expect(parserOutput.extractedData).toHaveProperty('title');
    });

    it('should allow creating valid fetch response', () => {
      const fetchResponse: FetchResponse = {
        content: '<html><body>Test</body></html>',
        statusCode: 200,
        headers: { 'content-type': 'text/html' },
        contentType: 'text/html',
        size: 1024,
        responseTime: 500,
        finalUrl: 'https://example.com',
      };

      expect(fetchResponse.statusCode).toBe(200);
      expect(fetchResponse.contentType).toBe('text/html');
    });

    it('should allow creating valid storage items', () => {
      const storageItem: StorageItem = {
        url: 'https://example.com',
        data: { title: 'Test Page', content: 'Test content' },
        depth: 0,
        crawledAt: new Date(),
      };

      expect(storageItem.data).toHaveProperty('title');
      expect(storageItem.depth).toBe(0);
    });

    it('should allow creating valid AI requests', () => {
      const extractionRequest: ContentExtractionRequest = {
        content: '<html><body><h1>Title</h1></body></html>',
        url: 'https://example.com',
        schema: {
          title: '页面主标题',
          content: '页面内容',
        },
      };

      const planRequest: CrawlPlanRequest = {
        topic: '新能源汽车技术趋势',
        dataType: 'articles',
        language: 'zh-CN',
      };

      expect(extractionRequest.schema).toHaveProperty('title');
      expect(planRequest.topic).toContain('新能源汽车');
    });
  });

  describe('Type Safety', () => {
    it('should enforce correct enum usage', () => {
      // 这些应该通过类型检查
      const strategy: TraversalStrategy = TraversalStrategy.BFS;
      const fetcherType: FetcherType = FetcherType.BROWSER;
      const storageType: StorageType = StorageType.MONGODB;

      expect(strategy).toBe('BFS');
      expect(fetcherType).toBe('browser');
      expect(storageType).toBe('mongodb');
    });

    it('should enforce interface contracts', () => {
      // 模拟一个解析器实现
      const mockParser: IParser = {
        name: 'test-parser',
        version: '1.0.0',
        canParse: jest.fn().mockReturnValue(true),
        parse: jest.fn().mockResolvedValue({
          extractedData: { title: 'Test' },
          newUrls: [],
        }),
      };

      expect(mockParser.name).toBe('test-parser');
      expect(typeof mockParser.canParse).toBe('function');
      expect(typeof mockParser.parse).toBe('function');
    });
  });
});
