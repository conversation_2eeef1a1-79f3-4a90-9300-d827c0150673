import React from 'react';
import { cn } from '@/lib/utils';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

const badgeVariants = {
  default: 'bg-gray-100 text-gray-800',
  success: 'bg-success-100 text-success-800',
  warning: 'bg-warning-100 text-warning-800',
  danger: 'bg-danger-100 text-danger-800',
  info: 'bg-primary-100 text-primary-800',
};

const badgeSizes = {
  sm: 'px-2 py-0.5 text-xs',
  md: 'px-2.5 py-1 text-sm',
  lg: 'px-3 py-1.5 text-base',
};

export function Badge({
  className,
  variant = 'default',
  size = 'md',
  children,
  ...props
}: BadgeProps) {
  return (
    <span
      className={cn(
        'inline-flex items-center rounded-full font-medium',
        badgeVariants[variant],
        badgeSizes[size],
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}

interface StatusBadgeProps {
  status: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  children?: React.ReactNode;
}

export function StatusBadge({ status, className, size = 'md', children }: StatusBadgeProps) {
  const getVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'completed':
      case 'success':
        return 'success';
      case 'warning':
      case 'paused':
      case 'degraded':
        return 'warning';
      case 'critical':
      case 'error':
      case 'failed':
      case 'unhealthy':
        return 'danger';
      case 'stopped':
      case 'inactive':
        return 'default';
      default:
        return 'info';
    }
  };

  const getDisplayText = (status: string) => {
    const statusMap: Record<string, string> = {
      healthy: '健康',
      running: '运行中',
      completed: '已完成',
      success: '成功',
      warning: '警告',
      paused: '已暂停',
      degraded: '降级',
      critical: '严重',
      error: '错误',
      failed: '失败',
      unhealthy: '不健康',
      stopped: '已停止',
      inactive: '未激活',
    };

    return statusMap[status.toLowerCase()] || status;
  };

  return (
    <Badge variant={getVariant(status)} size={size} className={className}>
      {children || getDisplayText(status)}
    </Badge>
  );
}
