import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { X, Plus, Trash2, Upload, HelpCircle } from 'lucide-react';
import { CreateTaskRequest, TaskConfig } from '@/types/task';
import { useNotification } from '@/contexts/NotificationContext';

interface CrawlerFormProps {
  onSubmit: (data: CreateTaskRequest) => void;
  onClose: () => void;
}

interface FormData {
  name: string;
  urls: string[];
  config: Partial<TaskConfig>;
}

export const CrawlerForm: React.FC<CrawlerFormProps> = ({ onSubmit, onClose }) => {
  const { showError } = useNotification();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    urls: [''],
    config: {
      maxDepth: 3,
      maxConcurrency: 5,
      delayMs: 1000,
      enableProxy: false,
      enableAI: true,
      allowedDomains: [],
      useReadability: true,
      includeRawHtml: true,
      useBrowser: true
    }
  });

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const handleUrlChange = (index: number, value: string) => {
    const newUrls = [...formData.urls];
    newUrls[index] = value;
    setFormData({ ...formData, urls: newUrls });
  };

  const addUrl = () => {
    setFormData({ ...formData, urls: [...formData.urls, ''] });
  };

  const removeUrl = (index: number) => {
    if (formData.urls.length > 1) {
      const newUrls = formData.urls.filter((_, i) => i !== index);
      setFormData({ ...formData, urls: newUrls });
    }
  };

  const handleDomainChange = (index: number, value: string) => {
    const newDomains = [...(formData.config.allowedDomains || [])];
    newDomains[index] = value;
    setFormData({
      ...formData,
      config: { ...formData.config, allowedDomains: newDomains },
    });
  };

  const addDomain = () => {
    setFormData({
      ...formData,
      config: {
        ...formData.config,
        allowedDomains: [...(formData.config.allowedDomains || []), ''],
      },
    });
  };

  const removeDomain = (index: number) => {
    const newDomains = (formData.config.allowedDomains || []).filter((_, i) => i !== index);
    setFormData({
      ...formData,
      config: { ...formData.config, allowedDomains: newDomains },
    });
  };

  const handleReadabilityChange = (value: boolean) => {
    setFormData({
      ...formData,
      config: {
        ...formData.config,
        useReadability: value,
      },
    });
  };

  const handleRawHtmlChange = (value: boolean) => {
    setFormData({
      ...formData,
      config: {
        ...formData.config,
        includeRawHtml: value,
      },
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      // 验证表单数据
      if (!formData.name.trim()) {
        showError('输入验证错误', '请输入任务名称');
        return;
      }

      const validUrls = formData.urls.filter(url => url.trim() !== '');
      if (validUrls.length === 0) {
        showError('输入验证错误', '请至少输入一个有效的URL');
        return;
      }

      // 验证URL格式
      const invalidUrls = validUrls.filter(url => {
        try {
          new URL(url);
          return false;
        } catch {
          return true;
        }
      });

      if (invalidUrls.length > 0) {
        showError('URL格式错误', `以下URL格式不正确：\n${invalidUrls.join('\n')}`);
        return;
      }

      const taskData: CreateTaskRequest = {
        name: formData.name,
        type: 'html_crawler' as any,
        urls: validUrls,
        config: {
          ...formData.config,
          allowedDomains: (formData.config.allowedDomains || []).filter(domain => domain.trim() !== ''),
        },
      };

      await onSubmit(taskData);
    } catch (error) {
      console.error('Submit error:', error);
      showError('创建任务失败', '请检查输入信息');
    } finally {
      setSubmitting(false);
    }
  };

  const importFromFile = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt,.csv';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          const urls = content.split('\n').filter(url => url.trim() !== '');
          setFormData({ ...formData, urls });
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>创建爬虫任务</CardTitle>
              <Button
                onClick={onClose}
                variant="ghost"
                size="sm"
                icon={<X className="h-4 w-4" />}
              >
                关闭
              </Button>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* 基本信息 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  任务名称
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入任务名称"
                  required
                />
              </div>

              {/* URL 列表 */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    起始 URL
                  </label>
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      onClick={importFromFile}
                      variant="ghost"
                      size="sm"
                      icon={<Upload className="h-4 w-4" />}
                    >
                      从文件导入
                    </Button>
                    <Button
                      type="button"
                      onClick={addUrl}
                      variant="ghost"
                      size="sm"
                      icon={<Plus className="h-4 w-4" />}
                    >
                      添加 URL
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  {formData.urls.map((url, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="url"
                        value={url}
                        onChange={(e) => handleUrlChange(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="https://example.com"
                      />
                      {formData.urls.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeUrl(index)}
                          variant="danger"
                          size="sm"
                          icon={<Trash2 className="h-4 w-4" />}
                        >
                          删除
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* 基本配置 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大深度
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={formData.config.maxDepth}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        config: { ...formData.config, maxDepth: parseInt(e.target.value) },
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    并发数
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="20"
                    value={formData.config.maxConcurrency}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        config: { ...formData.config, maxConcurrency: parseInt(e.target.value) },
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    延迟(毫秒)
                  </label>
                  <input
                    type="number"
                    min="100"
                    max="10000"
                    value={formData.config.delayMs}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        config: { ...formData.config, delayMs: parseInt(e.target.value) },
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* 功能开关 */}
              <div className="flex flex-wrap gap-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.config.useBrowser}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        config: { ...formData.config, useBrowser: e.target.checked },
                      })
                    }
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    模拟浏览器 (Puppeteer/Playwright)
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.config.enableProxy}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        config: { ...formData.config, enableProxy: e.target.checked },
                      })
                    }
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">启用代理</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.config.enableAI}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        config: { ...formData.config, enableAI: e.target.checked },
                      })
                    }
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">启用 AI 解析</span>
                </label>
              </div>

              {/* 高级配置 */}
              <div>
                <Button
                  type="button"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  variant="ghost"
                  size="sm"
                  icon={<HelpCircle className="h-4 w-4" />}
                >
                  {showAdvanced ? '隐藏' : '显示'}高级配置
                </Button>

                {showAdvanced && (
                  <div className="mt-4 space-y-4 p-4 bg-gray-50 rounded-lg">
                    {/* 允许的域名 */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-sm font-medium text-gray-700">
                          允许的域名（可选）
                        </label>
                        <Button
                          type="button"
                          onClick={addDomain}
                          variant="ghost"
                          size="sm"
                          icon={<Plus className="h-4 w-4" />}
                        >
                          添加域名
                        </Button>
                      </div>
                      <div className="space-y-2">
                        {(formData.config.allowedDomains || []).map((domain, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={domain}
                              onChange={(e) => handleDomainChange(index, e.target.value)}
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="example.com"
                            />
                            <Button
                              type="button"
                              onClick={() => removeDomain(index)}
                              variant="danger"
                              size="sm"
                              icon={<Trash2 className="h-4 w-4" />}
                            >
                              删除
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 内容解析配置 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        内容解析配置
                      </label>
                      <div className="space-y-3">
                        <div className="bg-blue-50 p-3 rounded-md">
                          <p className="text-sm text-blue-800 mb-2">
                            <strong>智能内容解析</strong> - 使用 Mozilla Readability 自动提取文章主要内容
                          </p>
                          <p className="text-xs text-blue-600">
                            自动识别标题、正文、摘要等，无需配置CSS选择器
                          </p>
                        </div>
                        
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.config.useReadability ?? true}
                            onChange={(e) => handleReadabilityChange(e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            使用智能内容解析 (推荐)
                          </span>
                          <span className="ml-2 text-xs text-gray-500">
                            基于Mozilla Readability算法
                          </span>
                        </label>

                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.config.includeRawHtml ?? true}
                            onChange={(e) => handleRawHtmlChange(e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            包含原始HTML
                          </span>
                          <span className="ml-2 text-xs text-gray-500">
                            便于定制化处理
                          </span>
                        </label>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* 提交按钮 */}
              <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                <Button
                  type="button"
                  onClick={onClose}
                  variant="ghost"
                  disabled={submitting}
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  loading={submitting}
                >
                  创建任务
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};