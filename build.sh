#!/bin/bash
set -e

# Nodex 项目构建和部署脚本
# 针对开发时前后端分离，生产时后端serve前端静态文件的架构

echo "🚀 开始构建 Nodex 项目..."

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$PROJECT_ROOT"
FRONTEND_DIR="$PROJECT_ROOT/dashboard"
DIST_DIR="$PROJECT_ROOT/dist"
FRONTEND_DIST_DIR="$FRONTEND_DIR/dist"

# 创建必要的目录
mkdir -p "$DIST_DIR/public"

echo "📂 项目目录结构:"
echo "  - 后端源码: $BACKEND_DIR/src"
echo "  - 前端源码: $FRONTEND_DIR/src"
echo "  - 后端构建: $DIST_DIR"
echo "  - 前端构建: $FRONTEND_DIST_DIR"

# 检查 Node.js 版本
node_version=$(node -v)
echo "📋 Node.js 版本: $node_version"

if [[ ! "$node_version" =~ ^v1[89]\. ]] && [[ ! "$node_version" =~ ^v2[0-9]\. ]]; then
    echo "❌ 错误: 需要 Node.js 18+ 版本"
    exit 1
fi

# 1. 清理旧的构建文件
echo "🧹 清理旧的构建文件..."
rm -rf "$DIST_DIR"
rm -rf "$FRONTEND_DIST_DIR"

# 2. 安装后端依赖
echo "📦 安装后端依赖..."
cd "$BACKEND_DIR"
npm ci --production=false

# 3. 类型检查
echo "🔍 执行类型检查..."
echo "  - 检查后端类型..."
npm run lint 2>/dev/null || echo "  ⚠️  Lint 检查有警告，继续构建..."

# 检查共享类型文件是否存在
if [ ! -f "$BACKEND_DIR/src/shared-types/index.ts" ]; then
    echo "❌ 错误: 共享类型文件不存在: $BACKEND_DIR/src/shared-types/index.ts"
    echo "请确保已创建共享类型文件"
    exit 1
fi

# 4. 构建后端
echo "🔨 构建后端..."
npm run build

if [ ! -d "$DIST_DIR" ]; then
    echo "❌ 错误: 后端构建失败"
    exit 1
fi

echo "✅ 后端构建完成"

# 5. 安装前端依赖
echo "📦 安装前端依赖..."
cd "$FRONTEND_DIR"

if [ ! -f "package.json" ]; then
    echo "❌ 错误: 前端 package.json 不存在"
    exit 1
fi

npm ci

# 6. 前端类型检查
echo "🔍 检查前端类型..."
npm run type-check 2>/dev/null || echo "  ⚠️  前端类型检查有警告，继续构建..."

# 7. 构建前端
echo "🔨 构建前端..."
npm run build

if [ ! -d "$FRONTEND_DIST_DIR" ]; then
    echo "❌ 错误: 前端构建失败"
    exit 1
fi

echo "✅ 前端构建完成"

# 8. 复制前端静态文件到后端dist目录
echo "📁 复制前端静态文件..."
cp -r "$FRONTEND_DIST_DIR"/* "$DIST_DIR/public/"

# 9. 复制共享类型定义到构建目录
echo "📋 复制共享类型定义..."
cp -r "$BACKEND_DIR/src/shared-types" "$DIST_DIR/"

# 10. 生成构建信息
echo "📝 生成构建信息..."
BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
BUILD_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
NODE_VERSION=$(node -v)

cat > "$DIST_DIR/build-info.json" << EOF
{
  "buildTime": "$BUILD_TIME",
  "gitHash": "$BUILD_HASH",
  "nodeVersion": "$NODE_VERSION",
  "environment": "${NODE_ENV:-development}",
  "backend": {
    "sourceDir": "src/",
    "distDir": "dist/",
    "entryPoint": "index.js"
  },
  "frontend": {
    "sourceDir": "dashboard/src/",
    "distDir": "dist/public/",
    "framework": "React + Vite"
  },
  "sharedTypes": {
    "location": "src/shared-types/",
    "exported": "dist/shared-types/"
  }
}
EOF

# 11. 验证构建结果
echo "🔍 验证构建结果..."
REQUIRED_FILES=(
    "$DIST_DIR/index.js"
    "$DIST_DIR/public/index.html"
    "$DIST_DIR/shared-types/index.js"
    "$DIST_DIR/build-info.json"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 错误: 必需文件不存在: $file"
        exit 1
    fi
done

# 12. 显示构建统计
echo ""
echo "📊 构建统计:"
echo "  - 后端文件数: $(find "$DIST_DIR" -name "*.js" -not -path "*/public/*" | wc -l)"
echo "  - 前端文件数: $(find "$DIST_DIR/public" -type f | wc -l)"
echo "  - 总构建大小: $(du -sh "$DIST_DIR" | cut -f1)"
echo "  - 前端资源大小: $(du -sh "$DIST_DIR/public" | cut -f1)"

# 13. 生成启动说明
echo ""
echo "✅ 构建完成!"
echo ""
echo "📚 使用说明:"
echo ""
echo "开发模式 (前后端分离):"
echo "  cd $PROJECT_ROOT && npm run dev"
echo "  - 后端: http://localhost:3000"
echo "  - 前端: http://localhost:3002"
echo ""
echo "生产模式 (后端serve前端):"
echo "  cd $PROJECT_ROOT && npm start"
echo "  - 统一访问: http://localhost:3000"
echo "  - 前端界面: http://localhost:3000/"
echo "  - API接口: http://localhost:3000/api/v1/"
echo ""
echo "生产环境启动:"
echo "  NODE_ENV=production npm run start:production"
echo ""
echo "Docker 部署:"
echo "  docker build -t nodex:latest ."
echo "  docker run -p 3000:3000 -e NODE_ENV=production nodex:latest"
echo ""

# 14. 可选：创建启动脚本
cat > "$DIST_DIR/start.sh" << 'EOF'
#!/bin/bash
# Nodex 生产环境启动脚本

export NODE_ENV=${NODE_ENV:-production}
export PORT=${PORT:-3000}
export HOST=${HOST:-0.0.0.0}

echo "🚀 启动 Nodex 服务器..."
echo "环境: $NODE_ENV"
echo "端口: $PORT"
echo "主机: $HOST"

node index.js
EOF

chmod +x "$DIST_DIR/start.sh"

echo "🎉 构建脚本执行完成!"
echo "构建文件位置: $DIST_DIR"