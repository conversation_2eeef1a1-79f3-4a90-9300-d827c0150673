/**
 * 代理管理器实现
 */

import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';
import {
  IProxyManager,
  ProxyConfig,
  ErrorInfo,
  ProxyStrategy,
  ProxyHealthCheckConfig,
} from '@/interfaces';
import { TEST_URLS } from '@/config';
import { getLogger } from '@/utils';

/**
 * Webshare代理数据接口
 */
interface WebshareProxy {
  id: string;
  username: string;
  password: string;
  proxy_address: string;
  port: number;
  valid: boolean;
  last_verification: string;
  country_code: string;
  city_name: string;
  asn_name: string;
  asn_number: number;
  high_country_confidence: boolean;
  created_at: string;
}

/**
 * 代理状态接口
 */
interface ProxyStatus {
  proxy: ProxyConfig;
  status: 'active' | 'failed' | 'recovering' | 'testing';
  successCount: number;
  failureCount: number;
  lastUsed?: Date;
  lastChecked?: Date;
  consecutiveFailures: number;
  isHealthy: boolean;
  responseTime: number;
  weight: number;
  priority: number;
  sessionId?: string; // 用于粘性会话
  lastError?: ErrorInfo;
  healthScore: number; // 健康评分 0-100
}

/**
 * 代理管理器实现类
 */
export class ProxyManager extends EventEmitter implements IProxyManager {
  private proxies: Map<string, ProxyStatus> = new Map();
  private strategy: ProxyStrategy;
  private healthCheckConfig: ProxyHealthCheckConfig;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private currentIndex = 0;
  private failureThreshold: number;
  private recoveryTimeMs: number;
  private sessionProxyMap: Map<string, string> = new Map(); // sessionId -> proxyKey
  private weightedProxies: ProxyStatus[] = []; // 用于加权轮询
  private testUrls: string[] = []; // 测试URL列表
  private currentTestUrlIndex = 0; // 当前测试URL索引
  private urlUsageStats: Map<string, { count: number; lastUsed: number }> = new Map(); // URL使用统计
  private urlCooldownMs = 30000; // URL冷却时间（30秒）
  private logger = getLogger('ProxyManager');
  private stats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    totalResponseTime: 0,
    proxyRotations: 0,
  };

  constructor(
    strategy: ProxyStrategy = ProxyStrategy.ROUND_ROBIN,
    healthCheckConfig: ProxyHealthCheckConfig,
    failureThreshold = 3,
    recoveryTimeMs = 300000 // 5分钟
  ) {
    super();
    this.strategy = strategy;
    this.healthCheckConfig = healthCheckConfig;
    this.failureThreshold = failureThreshold;
    this.recoveryTimeMs = recoveryTimeMs;

    // 初始化时加载测试URL
    this.loadTestUrls();
  }

  /**
   * 获取可用的代理
   */
  public getProxy(sessionId?: string): ProxyConfig | null {
    const availableProxies = this.getAvailableProxies();

    if (availableProxies.length === 0) {
      return null;
    }

    let selectedProxy: ProxyStatus;

    switch (this.strategy) {
      case ProxyStrategy.ROUND_ROBIN:
        selectedProxy = this.getRoundRobinProxy(availableProxies);
        break;

      case ProxyStrategy.RANDOM:
        selectedProxy = this.getRandomProxy(availableProxies);
        break;

      case ProxyStrategy.STICKY:
        selectedProxy = this.getStickyProxy(availableProxies, sessionId);
        break;

      case ProxyStrategy.WEIGHTED_ROUND_ROBIN:
        selectedProxy = this.getWeightedRoundRobinProxy(availableProxies);
        break;

      case ProxyStrategy.LEAST_CONNECTIONS:
        selectedProxy = this.getLeastConnectionsProxy(availableProxies);
        break;

      case ProxyStrategy.FASTEST_RESPONSE:
        selectedProxy = this.getFastestResponseProxy(availableProxies);
        break;

      default:
        selectedProxy = availableProxies[0]!;
    }

    // 更新使用时间和统计
    selectedProxy.lastUsed = new Date();
    this.stats.totalRequests++;
    this.stats.proxyRotations++;

    // 如果是粘性会话，记录会话映射
    if (this.strategy === ProxyStrategy.STICKY && sessionId) {
      this.sessionProxyMap.set(sessionId, this.getProxyKey(selectedProxy.proxy));
    }

    return selectedProxy.proxy;
  }

  /**
   * 标记代理为失败
   */
  public markProxyFailed(proxy: ProxyConfig, error: ErrorInfo): void {
    const key = this.getProxyKey(proxy);
    const proxyStatus = this.proxies.get(key);

    if (proxyStatus) {
      proxyStatus.failureCount++;
      proxyStatus.consecutiveFailures++;
      proxyStatus.lastError = error;
      this.stats.failedRequests++;

      // 更新健康评分
      proxyStatus.healthScore = Math.max(0, proxyStatus.healthScore - 10);

      if (proxyStatus.consecutiveFailures >= this.failureThreshold) {
        proxyStatus.status = 'failed';
        proxyStatus.isHealthy = false;
        proxyStatus.healthScore = 0;

        // 从加权代理列表中移除
        this.updateWeightedProxies();

        this.emit('proxy-failed', proxy, error);
        this.logger.warn('代理标记为失败', {
          host: proxy.host,
          port: proxy.port,
          consecutiveFailures: proxyStatus.consecutiveFailures,
          error: error.message,
        });
      }
    }
  }

  /**
   * 标记代理为成功
   */
  public markProxySuccess(proxy: ProxyConfig, responseTime?: number): void {
    const key = this.getProxyKey(proxy);
    const proxyStatus = this.proxies.get(key);

    if (proxyStatus) {
      proxyStatus.successCount++;
      proxyStatus.consecutiveFailures = 0;
      this.stats.successfulRequests++;

      // 更新响应时间
      if (responseTime !== undefined) {
        proxyStatus.responseTime = responseTime;
        this.stats.totalResponseTime += responseTime;
      }

      // 更新健康评分
      proxyStatus.healthScore = Math.min(100, proxyStatus.healthScore + 5);

      if (proxyStatus.status === 'failed' || proxyStatus.status === 'recovering') {
        proxyStatus.status = 'active';
        proxyStatus.isHealthy = true;

        // 重新加入加权代理列表
        this.updateWeightedProxies();

        this.emit('proxy-recovered', proxy);
        this.logger.info('代理恢复并标记为活跃', {
          host: proxy.host,
          port: proxy.port,
          healthScore: proxyStatus.healthScore,
        });
      }
    }
  }

  /**
   * 添加代理
   */
  public addProxy(proxy: ProxyConfig, weight = 1, priority = 0): void {
    const key = this.getProxyKey(proxy);

    if (!this.proxies.has(key)) {
      const proxyStatus: ProxyStatus = {
        proxy,
        status: 'active',
        successCount: 0,
        failureCount: 0,
        consecutiveFailures: 0,
        isHealthy: true,
        responseTime: 0,
        weight,
        priority,
        healthScore: 100,
      };

      this.proxies.set(key, proxyStatus);
      this.updateWeightedProxies();
      this.emit('proxy-added', proxy);
      this.logger.info(`添加代理: ${proxy.host}: ${proxy.port}`);
    }
  }

  /**
   * 移除代理
   */
  public removeProxy(proxy: ProxyConfig): void {
    const key = this.getProxyKey(proxy);

    if (this.proxies.delete(key)) {
      this.emit('proxy-removed', proxy);
      this.logger.info('移除代理', {
        host: proxy.host,
        port: proxy.port,
      });
    }
  }

  /**
   * 获取所有代理
   */
  public getAllProxies(): ProxyConfig[] {
    return Array.from(this.proxies.values()).map(status => status.proxy);
  }

  /**
   * 获取可用代理数量
   */
  public getAvailableProxyCount(): number {
    return this.getAvailableProxies().length;
  }

  /**
   * 启动健康检查
   */
  public startHealthCheck(): void {
    if (!this.healthCheckConfig.enabled || this.healthCheckInterval) {
      return;
    }

    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck().catch(error => {
        this.logger.error('健康检查失败', {
          error: error instanceof Error ? error.message : error,
        });
      });
    }, this.healthCheckConfig.intervalMs);

    this.logger.info('代理健康检查已启动', {
      intervalMs: this.healthCheckConfig.intervalMs,
      timeoutMs: this.healthCheckConfig.timeoutMs,
    });
  }

  /**
   * 停止健康检查
   */
  public stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      this.logger.info('代理健康检查已停止');
    }
  }

  /**
   * 获取代理统计信息
   */
  public getProxyStats(): Array<{
    proxy: ProxyConfig;
    status: 'active' | 'failed' | 'recovering';
    successCount: number;
    failureCount: number;
    lastUsed?: Date;
    lastChecked?: Date;
    responseTime?: number;
  }> {
    return Array.from(this.proxies.values()).map(status => ({
      proxy: status.proxy,
      status: status.status === 'testing' ? 'recovering' : status.status,
      successCount: status.successCount,
      failureCount: status.failureCount,
      lastUsed: status.lastUsed,
      lastChecked: status.lastChecked,
      responseTime: status.proxy.responseTime,
    }));
  }

  /**
   * 获取代理键
   */
  private getProxyKey(proxy: ProxyConfig): string {
    return `${proxy.host}:${proxy.port}`;
  }

  /**
   * 获取可用代理
   */
  private getAvailableProxies(): ProxyStatus[] {
    const now = new Date();

    return Array.from(this.proxies.values()).filter(status => {
      // 如果代理是活跃的，直接返回
      if (status.status === 'active' && status.isHealthy) {
        return true;
      }

      // 如果代理失败了，检查是否到了恢复时间
      if (status.status === 'failed' && status.lastUsed) {
        const timeSinceLastUse = now.getTime() - status.lastUsed.getTime();
        if (timeSinceLastUse >= this.recoveryTimeMs) {
          status.status = 'recovering';
          return true;
        }
      }

      return false;
    });
  }

  /**
   * 轮询策略获取代理
   */
  private getRoundRobinProxy(availableProxies: ProxyStatus[]): ProxyStatus {
    const proxy = availableProxies[this.currentIndex % availableProxies.length];
    this.currentIndex = (this.currentIndex + 1) % availableProxies.length;
    return proxy!;
  }

  /**
   * 随机策略获取代理
   */
  private getRandomProxy(availableProxies: ProxyStatus[]): ProxyStatus {
    const randomIndex = Math.floor(Math.random() * availableProxies.length);
    return availableProxies[randomIndex]!;
  }

  /**
   * 粘性策略获取代理
   */
  private getStickyProxy(availableProxies: ProxyStatus[], sessionId?: string): ProxyStatus {
    if (sessionId && this.sessionProxyMap.has(sessionId)) {
      const proxyKey = this.sessionProxyMap.get(sessionId)!;
      const stickyProxy = this.proxies.get(proxyKey);

      if (stickyProxy && stickyProxy.isHealthy && stickyProxy.status === 'active') {
        return stickyProxy;
      } else {
        // 如果粘性代理不可用，移除映射并选择新的
        this.sessionProxyMap.delete(sessionId);
      }
    }

    // 选择第一个可用的代理
    return availableProxies[0]!;
  }

  /**
   * 加权轮询策略获取代理
   */
  private getWeightedRoundRobinProxy(availableProxies: ProxyStatus[]): ProxyStatus {
    if (this.weightedProxies.length === 0) {
      this.updateWeightedProxies();
    }

    const availableWeightedProxies = this.weightedProxies.filter(proxy =>
      availableProxies.some(ap => ap.proxy.host === proxy.proxy.host && ap.proxy.port === proxy.proxy.port)
    );

    if (availableWeightedProxies.length === 0) {
      return availableProxies[0]!;
    }

    const proxy = availableWeightedProxies[this.currentIndex % availableWeightedProxies.length];
    this.currentIndex = (this.currentIndex + 1) % availableWeightedProxies.length;
    return proxy!;
  }

  /**
   * 最少连接策略获取代理
   */
  private getLeastConnectionsProxy(availableProxies: ProxyStatus[]): ProxyStatus {
    return availableProxies.reduce((least, current) => {
      const leastConnections = least.successCount + least.failureCount;
      const currentConnections = current.successCount + current.failureCount;
      return currentConnections < leastConnections ? current : least;
    });
  }

  /**
   * 最快响应策略获取代理
   */
  private getFastestResponseProxy(availableProxies: ProxyStatus[]): ProxyStatus {
    return availableProxies.reduce((fastest, current) => {
      // 如果当前代理没有响应时间记录，使用默认值
      const currentTime = current.responseTime || 999999;
      const fastestTime = fastest.responseTime || 999999;
      return currentTime < fastestTime ? current : fastest;
    });
  }

  /**
   * 更新加权代理列表
   */
  private updateWeightedProxies(): void {
    this.weightedProxies = [];

    for (const proxyStatus of this.proxies.values()) {
      if (proxyStatus.isHealthy && proxyStatus.status === 'active') {
        // 根据权重添加多个副本
        for (let i = 0; i < proxyStatus.weight; i++) {
          this.weightedProxies.push(proxyStatus);
        }
      }
    }

    // 按优先级排序
    this.weightedProxies.sort((a, b) => b.priority - a.priority);
  }
  private updateReponseTimeProxies(timeMap: Map<string, number>): void {
    this.weightedProxies = [];

    for (const [proxy, time] of timeMap.entries()) {
      const old = this.proxies.get(proxy)
      console.log('old:', old?.proxy)
      const newProxy = {
        ...old,
        proxy: {
          ...old?.proxy,
          responseTime: time
        }
      }
      this.proxies.set(proxy, newProxy as ProxyStatus)
    }
    console.log('this.proxies:', this.proxies)
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    const checkPromises = Array.from(this.proxies.values()).map(async (proxyStatus) => {
      try {
        const isHealthy = await this.checkProxyHealth(proxyStatus.proxy);
        proxyStatus.lastChecked = new Date();

        if (isHealthy) {
          if (!proxyStatus.isHealthy) {
            proxyStatus.isHealthy = true;
            proxyStatus.status = 'active';
            proxyStatus.consecutiveFailures = 0;
            this.emit('proxy-health-recovered', proxyStatus.proxy);
          }
        } else {
          if (proxyStatus.isHealthy) {
            proxyStatus.isHealthy = false;
            proxyStatus.consecutiveFailures++;
            if (proxyStatus.consecutiveFailures >= this.failureThreshold) {
              proxyStatus.status = 'failed';
            }
            this.emit('proxy-health-failed', proxyStatus.proxy);
          }
        }
      } catch (error) {
        this.logger.error(`Health check failed for proxy ${proxyStatus.proxy.host}:${proxyStatus.proxy.port}:`, error);
      }
    });

    await Promise.allSettled(checkPromises);
  }

  /**
   * 检查单个代理的健康状态（优化版本）
   */
  private async checkProxyHealth(proxy: ProxyConfig): Promise<boolean> {
    try {
      const axios = (await import('axios')).default;

      let proxyUrl = `${proxy.protocol || 'http'}://${proxy.host}:${proxy.port}`;

      if (proxy.auth) {
        const auth = `${proxy.auth.username}:${proxy.auth.password}`;
        proxyUrl = proxyUrl.replace('://', `://${auth}@`);
      }

      let proxyAgent;

      if (proxy.protocol === 'https') {
        const { HttpsProxyAgent } = await import('https-proxy-agent');
        proxyAgent = new HttpsProxyAgent(proxyUrl);
      } else {
        const { HttpProxyAgent } = await import('http-proxy-agent');
        proxyAgent = new HttpProxyAgent(proxyUrl);
      }

      // 使用轮换的测试URL
      const testUrl = this.getNextTestUrl();

      // 优化的测速策略：先尝试HEAD请求，失败则尝试GET请求
      try {
        // 首先尝试HEAD请求（最小流量消耗）
        await axios({
          method: 'HEAD',
          url: testUrl,
          httpsAgent: proxyAgent,
          httpAgent: proxyAgent,
          timeout: this.healthCheckConfig.timeoutMs || 5000,
          maxRedirects: 0,
          validateStatus: () => true, // 不抛出状态码错误
        });

        return true;
      } catch (headError) {
        // HEAD请求失败，尝试GET请求小文件
        try {
          await axios({
            method: 'GET',
            url: testUrl,
            httpsAgent: proxyAgent,
            httpAgent: proxyAgent,
            timeout: this.healthCheckConfig.timeoutMs || 5000,
            maxRedirects: 0,
            validateStatus: () => true, // 不抛出状态码错误
          });

          return true;
        } catch (getError) {
          this.logger.debug(`Proxy health check failed for ${proxy.host}:${proxy.port}`, {
            testUrl,
            headError: headError instanceof Error ? headError.message : String(headError),
            getError: getError instanceof Error ? getError.message : String(getError),
          });
          return false;
        }
      }
    } catch (error) {
      this.logger.debug(`Proxy health check error for ${proxy.host}:${proxy.port}`, {
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * 设置策略
   */
  public setStrategy(strategy: ProxyStrategy): void {
    this.strategy = strategy;
    this.currentIndex = 0; // 重置轮询索引
  }

  /**
   * 获取当前策略
   */
  public getStrategy(): ProxyStrategy {
    return this.strategy;
  }

  /**
   * 清空所有代理
   */
  public clearAllProxies(): void {
    this.proxies.clear();
    this.currentIndex = 0;
    this.emit('proxies-cleared');
    this.logger.info('All proxies cleared');
  }

  /**
   * 批量添加代理
   */
  public addProxies(proxies: ProxyConfig[]): void {
    proxies.forEach(proxy => this.addProxy(proxy));
  }

  /**
   * 加载测试URL列表
   */
  private async loadTestUrls(): Promise<void> {
    try {
      const testUrlsPath = path.resolve(process.cwd(), 'testUrls.json');
      const content = await fs.readFile(testUrlsPath, 'utf-8');
      this.testUrls = JSON.parse(content);
      this.logger.info(`Loaded ${this.testUrls.length} test URLs for proxy health checks`);
    } catch (error) {
      this.logger.warn('Failed to load test URLs, using default:', error);
      // 使用默认的测试URL
      this.testUrls = TEST_URLS || [
        'https://httpbin.org/ip',
        'https://httpbin.org/get',
        'https://httpbin.org/headers',
        'https://www.google.com',
        'https://www.cloudflare.com',
        'https://www.github.com'
      ];
      this.logger.info(`Using ${this.testUrls.length} default test URLs for proxy health checks`);
    }
  }

  /**
   * 获取下一个测试URL（智能轮换机制）
   */
  private getNextTestUrl(): string {

    // 智能选择URL：优先选择使用频率低且冷却时间已过的URL
    const now = Date.now();
    const availableUrls = this.testUrls.filter(url => {
      const stats = this.urlUsageStats.get(url);
      if (!stats) return true; // 未使用过的URL优先

      // 检查冷却时间
      return (now - stats.lastUsed) >= this.urlCooldownMs;
    });

    let selectedUrl: string;

    if (availableUrls.length > 0) {
      // 从可用URL中选择使用频率最低的
      selectedUrl = availableUrls.reduce((best, current) => {
        const bestStats = this.urlUsageStats.get(best);
        const currentStats = this.urlUsageStats.get(current);

        const bestCount = bestStats?.count || 0;
        const currentCount = currentStats?.count || 0;

        return currentCount < bestCount ? current : best;
      });
    } else {
      // 如果所有URL都在冷却期，使用轮换机制
      selectedUrl = this.testUrls[this.currentTestUrlIndex]!;
      this.currentTestUrlIndex = (this.currentTestUrlIndex + 1) % this.testUrls.length;
    }

    // 更新使用统计
    this.updateUrlUsageStats(selectedUrl);

    return selectedUrl;
  }

  /**
   * 更新URL使用统计
   */
  private updateUrlUsageStats(url: string): void {
    const now = Date.now();
    const stats = this.urlUsageStats.get(url);

    if (stats) {
      stats.count++;
      stats.lastUsed = now;
    } else {
      this.urlUsageStats.set(url, { count: 1, lastUsed: now });
    }
  }

  /**
   * 重置URL使用统计
   */
  public resetUrlUsageStats(): void {
    this.urlUsageStats.clear();
    this.logger.info('URL usage statistics reset');
  }

  /**
   * 获取URL使用统计
   */
  public getUrlUsageStats(): Array<{ url: string; count: number; lastUsed: Date }> {
    return Array.from(this.urlUsageStats.entries()).map(([url, stats]) => ({
      url,
      count: stats.count,
      lastUsed: new Date(stats.lastUsed),
    }));
  }

  /**
   * 转换Webshare代理格式为标准ProxyConfig格式
   */
  private convertWebshareProxy(webshareProxy: WebshareProxy): ProxyConfig {
    return {
      host: webshareProxy.proxy_address,
      port: webshareProxy.port,
      auth: {
        username: webshareProxy.username,
        password: webshareProxy.password,
      },
      protocol: 'http' as const,
    } as ProxyConfig;
  }

  /**
   * 从webshare格式的JSON文件加载代理
   */
  public async loadWebshareProxies(configPath: string): Promise<void> {
    try {
      this.logger.info(`Loading webshare proxies from: ${configPath}`);

      // 读取配置文件
      const configContent = await fs.readFile(configPath, 'utf-8');
      const webshareProxies: WebshareProxy[] = JSON.parse(configContent);

      if (!Array.isArray(webshareProxies)) {
        throw new Error('Webshare proxy file must contain an array of proxies');
      }

      // 转换并添加代理
      const validProxies: ProxyConfig[] = [];
      for (const webshareProxy of webshareProxies) {
        // 只添加有效的代理
        if (webshareProxy.valid && webshareProxy.proxy_address && webshareProxy.port) {
          const proxyConfig = this.convertWebshareProxy(webshareProxy);
          validProxies.push(proxyConfig);
        }
      }

      this.addProxies(validProxies);
      this.logger.info(`Loaded ${validProxies.length} valid proxies from ${webshareProxies.length} webshare proxies`);

      this.emit('proxies-loaded', {
        count: validProxies.length,
        total: webshareProxies.length,
        source: configPath,
        type: 'webshare'
      });
    } catch (error) {
      this.logger.error(`Failed to load webshare proxies from ${configPath}:`, error);
      throw error;
    }
  }

  /**
   * 从外部配置文件加载代理
   */
  public async loadExtraProxyConfig(configPath: string): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      const absolutePath = path.resolve(configPath);
      const configContent = await fs.readFile(absolutePath, 'utf-8');

      let proxyConfig: any;
      const ext = path.extname(configPath).toLowerCase();

      switch (ext) {
        case '.json':
          proxyConfig = JSON.parse(configContent);

          // 检测是否为webshare格式
          if (Array.isArray(proxyConfig) && proxyConfig.length > 0 && 'proxy_address' in proxyConfig[0]) {
            // 这是webshare格式，直接调用专门的加载方法
            await this.loadWebshareProxies(configPath);
            return;
          }
          break;

        case '.js':
        case '.ts': {
          // 动态导入 JavaScript/TypeScript 配置文件
          const configModule = await import(absolutePath);
          proxyConfig = configModule.default ?? configModule;
          break;
        }

        default:
          throw new Error(`Unsupported proxy config file format: ${ext}`);
      }

      // 验证配置格式
      if (!this.validateProxyConfig(proxyConfig)) {
        throw new Error('Invalid proxy configuration format');
      }

      // 添加代理
      if (Array.isArray(proxyConfig)) {
        // 直接是代理数组
        this.addProxies(proxyConfig);
        this.logger.info(`Loaded ${proxyConfig.length} proxies from ${configPath}`);
      } else if (proxyConfig.proxies && Array.isArray(proxyConfig.proxies)) {
        // 包含 proxies 字段的对象
        this.addProxies(proxyConfig.proxies);

        // 如果有权重和优先级配置，应用它们
        if (proxyConfig.weights || proxyConfig.priorities) {
          this.applyProxyMetadata(proxyConfig.proxies, proxyConfig.weights, proxyConfig.priorities);
        }

        this.logger.info(`Loaded ${proxyConfig.proxies.length} proxies from ${configPath}`);
      } else {
        throw new Error('Proxy configuration must contain a proxies array');
      }

      this.emit('proxies-loaded', { count: this.proxies.size, source: configPath });

    } catch (error) {
      if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
        this.logger.warn(`Extra proxy config file not found: ${configPath}`);
        return; // 文件不存在时不抛出错误，只是警告
      }

      this.logger.error(`Failed to load extra proxy config from ${configPath}:`, error);
      throw new Error(`Failed to load extra proxy config: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 验证代理配置格式
   */
  private validateProxyConfig(config: any): boolean {
    if (Array.isArray(config)) {
      // 直接是代理数组
      return config.every(proxy => this.validateSingleProxy(proxy));
    }

    if (config && typeof config === 'object' && Array.isArray(config.proxies)) {
      // 包含 proxies 字段的对象
      return config.proxies.every((proxy: any) => this.validateSingleProxy(proxy));
    }

    return false;
  }

  /**
   * 验证单个代理配置
   */
  private validateSingleProxy(proxy: any): boolean {
    return (
      proxy &&
      typeof proxy === 'object' &&
      typeof proxy.host === 'string' &&
      typeof proxy.port === 'number' &&
      proxy.port > 0 &&
      proxy.port <= 65535
    );
  }

  /**
   * 应用代理元数据（权重和优先级）
   */
  private applyProxyMetadata(
    proxies: ProxyConfig[],
    weights?: Record<string, number>,
    priorities?: Record<string, number>
  ): void {
    proxies.forEach((proxy, index) => {
      const key = this.getProxyKey(proxy);
      const proxyStatus = this.proxies.get(key);

      if (proxyStatus) {
        // 应用权重
        if (weights) {
          const weight = weights[key] || weights[`${proxy.host}:${proxy.port}`] || weights[index.toString()];
          if (typeof weight === 'number' && weight > 0) {
            proxyStatus.weight = weight;
          }
        }

        // 应用优先级
        if (priorities) {
          const priority = priorities[key] || priorities[`${proxy.host}:${proxy.port}`] || priorities[index.toString()];
          if (typeof priority === 'number') {
            proxyStatus.priority = priority;
          }
        }
      }
    });

    // 更新加权代理列表
    this.updateWeightedProxies();
  }

  /**
   * 获取健康的代理数量
   */
  public getHealthyProxyCount(): number {
    return Array.from(this.proxies.values()).filter(status => status.isHealthy).length;
  }

  /**
   * 获取失败的代理数量
   */
  public getFailedProxyCount(): number {
    return Array.from(this.proxies.values()).filter(status => status.status === 'failed').length;
  }

  /**
   * 获取代理管理器统计信息
   */
  public getManagerStats(): {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    proxyRotations: number;
    totalProxies: number;
    healthyProxies: number;
    failedProxies: number;
    strategy: ProxyStrategy;
  } {
    return {
      totalRequests: this.stats.totalRequests,
      successfulRequests: this.stats.successfulRequests,
      failedRequests: this.stats.failedRequests,
      averageResponseTime: this.stats.successfulRequests > 0
        ? this.stats.totalResponseTime / this.stats.successfulRequests
        : 0,
      proxyRotations: this.stats.proxyRotations,
      totalProxies: this.proxies.size,
      healthyProxies: this.getHealthyProxyCount(),
      failedProxies: this.getFailedProxyCount(),
      strategy: this.strategy,
    };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalResponseTime: 0,
      proxyRotations: 0,
    };
    this.logger.info('Proxy manager stats reset');
  }

  /**
   * 设置代理权重
   */
  public setProxyWeight(proxy: ProxyConfig, weight: number): void {
    const key = this.getProxyKey(proxy);
    const proxyStatus = this.proxies.get(key);

    if (proxyStatus) {
      proxyStatus.weight = weight;
      this.updateWeightedProxies();
      this.logger.info(`Updated proxy ${proxy.host}:${proxy.port} weight to ${weight}`);
    }
  }

  /**
   * 设置代理优先级
   */
  public setProxyPriority(proxy: ProxyConfig, priority: number): void {
    const key = this.getProxyKey(proxy);
    const proxyStatus = this.proxies.get(key);

    if (proxyStatus) {
      proxyStatus.priority = priority;
      this.updateWeightedProxies();
      this.logger.info(`Updated proxy ${proxy.host}:${proxy.port} priority to ${priority}`);
    }
  }

  /**
   * 获取代理详细信息
   */
  public getProxyDetails(proxy: ProxyConfig): ProxyStatus | null {
    const key = this.getProxyKey(proxy);
    return this.proxies.get(key) || null;
  }

  /**
   * 强制恢复代理
   */
  public forceRecoverProxy(proxy: ProxyConfig): void {
    const key = this.getProxyKey(proxy);
    const proxyStatus = this.proxies.get(key);

    if (proxyStatus) {
      proxyStatus.status = 'active';
      proxyStatus.isHealthy = true;
      proxyStatus.consecutiveFailures = 0;
      proxyStatus.healthScore = 50; // 给一个中等的健康评分

      this.updateWeightedProxies();
      this.emit('proxy-force-recovered', proxy);
      this.logger.info(`Force recovered proxy: ${proxy.host}:${proxy.port}`);
    }
  }

  /**
   * 清理会话映射
   */
  public clearSessionMappings(): void {
    this.sessionProxyMap.clear();
    this.logger.info('Session proxy mappings cleared');
  }

  /**
   * 获取会话对应的代理
   */
  public getSessionProxy(sessionId: string): ProxyConfig | null {
    const proxyKey = this.sessionProxyMap.get(sessionId);
    if (proxyKey) {
      const proxyStatus = this.proxies.get(proxyKey);
      return proxyStatus ? proxyStatus.proxy : null;
    }
    return null;
  }

  /**
   * 获取最佳代理（基于健康评分）
   */
  public getBestProxy(): ProxyConfig | null {
    const availableProxies = this.getAvailableProxies();
    if (availableProxies.length === 0) {
      return null;
    }

    const bestProxy = availableProxies.reduce((best, current) => {
      return current.healthScore > best.healthScore ? current : best;
    });

    return bestProxy.proxy;
  }

  /**
   * 测试所有代理
   */
  public async testAllProxies(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();
    const responseTimeMap = new Map<string, number>();
    const testPromises = Array.from(this.proxies.entries()).map(async ([key, proxyStatus]) => {
      proxyStatus.status = 'testing';
      const start = Date.now()
      const isHealthy = await this.checkProxyHealth(proxyStatus.proxy);
      const end = Date.now() - start
      results.set(key, isHealthy);
      responseTimeMap.set(key, end);

      if (isHealthy) {
        proxyStatus.status = 'active';
        proxyStatus.isHealthy = true;
        proxyStatus.healthScore = Math.min(100, proxyStatus.healthScore + 10);
      } else {
        proxyStatus.status = 'failed';
        proxyStatus.isHealthy = false;
        proxyStatus.healthScore = Math.max(0, proxyStatus.healthScore - 20);
      }
    });

    await Promise.all(testPromises);
    this.updateWeightedProxies();
    this.updateReponseTimeProxies(responseTimeMap);

    return results;
  }

  /**
   * 自动优化代理配置
   */
  public autoOptimizeProxies(): void {
    for (const proxyStatus of this.proxies.values()) {
      const totalRequests = proxyStatus.successCount + proxyStatus.failureCount;

      if (totalRequests > 10) {
        const successRate = proxyStatus.successCount / totalRequests;

        // 根据成功率调整权重
        if (successRate > 0.9) {
          proxyStatus.weight = Math.min(5, proxyStatus.weight + 1);
        } else if (successRate < 0.5) {
          proxyStatus.weight = Math.max(1, proxyStatus.weight - 1);
        }

        // 根据响应时间调整优先级
        if (proxyStatus.responseTime > 0) {
          if (proxyStatus.responseTime < 1000) {
            proxyStatus.priority = Math.min(10, proxyStatus.priority + 1);
          } else if (proxyStatus.responseTime > 5000) {
            proxyStatus.priority = Math.max(0, proxyStatus.priority - 1);
          }
        }
      }
    }

    this.updateWeightedProxies();
    this.logger.info('Proxy configuration auto-optimized');
  }
}
