/**
 * Nodex API 服务器 - 重构版
 *
 * 专注于 HTTP 服务器、路由和中间件管理
 * 提供 RESTful API 和 WebSocket 服务
 * 使用新的TaskManager架构
 */

import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import { join } from 'path';
import { TaskManager, TaskManagerDependencies } from '@/app/task-manager';
import { FetcherManager } from '@/modules/fetcher';
import { ParserManager } from '@/modules/parser';
import { StorageManager } from '@/modules/storage';
import { AIEngine } from '@/modules/ai';
import { createApiRoutes } from './routes';
import { getLogger, time } from '@/utils';

export interface ApiServerConfig {
  port: number;
  host: string;
  enableCors?: boolean;
  corsOrigin?: string | string[];
  staticPath?: string;
}

export interface ApiServerDependencies {
  fetcherManager: FetcherManager;
  parserManager: ParserManager;
  storageManager: StorageManager;
  aiEngine?: AIEngine;
}

export class ApiServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private config: ApiServerConfig;
  private dependencies: ApiServerDependencies;
  private taskManager: TaskManager;
  private logger = getLogger('ApiServer');

  constructor(config: ApiServerConfig, dependencies: ApiServerDependencies) {
    this.config = config;
    this.dependencies = dependencies;

    // 创建TaskManager实例
    this.taskManager = new TaskManager({
      fetcherManager: dependencies.fetcherManager,
      parserManager: dependencies.parserManager,
      storageManager: dependencies.storageManager,
      aiEngine: dependencies.aiEngine,
    });

    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: this.config.corsOrigin,
        methods: ['GET', 'POST', 'PUT', 'DELETE'],
      },
    });

    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketIO();
    this.setupTaskManagerEventListeners();
  }

  /**
   * 设置中间件
   */
  private setupMiddleware(): void {
    // CORS
    if (this.config.enableCors) {
      this.app.use(
        cors({
          origin: this.config.corsOrigin,
          credentials: true,
        })
      );
    }

    // JSON 解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 静态文件服务
    if (this.config.staticPath) {
      this.app.use(express.static(this.config.staticPath));
    }

    // 请求日志中间件
    this.app.use((req, res, next) => {
      const startTime = Date.now();

      // 记录请求开始
      this.logger.debug(`${req.method} ${req.path}`, {
        method: req.method,
        path: req.path,
        query: req.query,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
      });

      // 记录响应完成
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        const logLevel = res.statusCode >= 400 ? 'warn' : 'info';

        this.logger[logLevel](`${req.method} ${req.path} ${res.statusCode}`, {
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
          duration,
          contentLength: res.get('Content-Length'),
        });
      });

      next();
    });

    // 错误处理中间件
    this.app.use(
      (apiError: any, req: express.Request, res: express.Response, _next: express.NextFunction) => {
        this.logger.error('API 错误', {
          error: apiError.message,
          stack: apiError.stack,
          method: req.method,
          path: req.path,
          query: req.query,
          body: req.body,
        });

        res.status(500).json({
          success: false,
          error: apiError.message || 'Internal server error',
        });
      }
    );
  }

  private setupRoutes(): void {
    this.app.get('/health', (_req, res) => {
      res.json({
        success: true,
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        taskManager: this.taskManager
          ? {
              totalTasks: this.taskManager.getAllTasks().length,
              stats: this.taskManager.getCrawlerStats(),
            }
          : null,
      });
    });

    // 创建路由配置
    const apiV1Routes = createApiRoutes({
      taskManager: this.taskManager,
      io: this.io,
      activeTasks: new Map(), // 空Map，保持兼容
    });
    this.app.use('/api/v1', apiV1Routes);

    // 默认路由 - 返回前端应用
    this.app.get('*', (_req, res) => {
      if (!this.config.staticPath) {
        return res.status(404).json({ error: 'Static path not configured' });
      }

      const indexPath = join(this.config.staticPath, 'index.html');
      res.sendFile(indexPath, err => {
        if (err) {
          this.logger.error('Failed to serve index.html', {
            error: err.message,
            path: indexPath,
            staticPath: this.config.staticPath,
          });
          res.status(404).json({ error: 'Frontend not found', path: indexPath });
        }
      });
    });
  }

  private setupSocketIO(): void {
    this.io.on('connection', socket => {
      this.logger.info('客户端连接', { socketId: socket.id });

      socket.on('disconnect', () => {
        this.logger.info('客户端断开连接', { socketId: socket.id });
      });

      // 发送初始数据
      socket.emit('connected', {
        message: 'Connected to Nodex API Server',
        timestamp: new Date().toISOString(),
      });
    });
  }

  private setupTaskManagerEventListeners(): void {
    const taskManager = this.taskManager;
    if (!taskManager) return;

    // 任务状态变化事件
    taskManager.on('taskStatusChanged', (task: any) => {
      this.logger.info(`任务状态变化: ${task.id} -> ${task.status}`, {
        taskId: task.id,
        status: task.status,
      });
      this.io.emit('task_status_changed', { taskId: task.id, status: task.status });
    });

    // 任务统计更新事件
    taskManager.on('taskStatsUpdated', (task: any) => {
      this.logger.debug('任务统计更新', { taskId: task.id, stats: task.stats });
      this.io.emit('task_stats_updated', { taskId: task.id, stats: task.stats });
    });

    // URL处理事件
    taskManager.on('urlProcessed', (data: any) => {
      this.logger.debug('URL 处理完成', data);
      this.io.emit('url_processed', data);
    });

    // 数据提取事件
    taskManager.on('dataExtracted', (data: any) => {
      // this.logger.debug('数据提取完成', data);
      this.io.emit('data_extracted', data);
    });

    // 任务错误事件
    taskManager.on('taskError', (data: any) => {
      this.logger.error('任务错误', data);
      this.io.emit('task_error', data);
    });

    // 任务完成事件
    taskManager.on('taskCompleted', (task: any) => {
      this.logger.info('任务完成', { taskId: task.id });
      this.io.emit('task_completed', { taskId: task.id });
    });
  }

  /**
   * 启动 API 服务器
   */
  public async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      const startTimer = time('API服务器启动');
      const host = this.config.host;
      const port = this.config.port;
      this.server.listen(port, host, (listenError: any) => {
        if (listenError) {
          startTimer();
          this.logger.error('API 服务器启动失败', {
            error: listenError.message,
            port: port,
            host: host,
          });
          reject(listenError);
          return;
        }

        startTimer();

        const serverInfo = {
          host: host,
          port: port,
          pid: process.pid,
          nodeVersion: process.version,
        };

        this.logger.info('🚀 Nodex API 服务器启动成功', serverInfo);
        this.logger.info(`📍 地址: http://${host}:${port}`);
        this.logger.info(`🌐 前端界面: http://${host}:${port}`);
        this.logger.info(`📡 WebSocket: ws://${host}:${port}`);
        this.logger.info(`📖 API 文档: http://${host}:${port}/api/v1`);

        if (host === '0.0.0.0') {
          this.logger.info(`🚀 本地开发请访问: http://localhost:${port}`);
        }

        resolve();
      });
    });
  }

  /**
   * 停止 API 服务器
   */
  public async stop(): Promise<void> {
    return new Promise(async resolve => {
      const stopTimer = time('API服务器停止');

      this.logger.info('🛑 正在停止 API 服务器...');

      try {
        // 停止 TaskManager
        const taskManager = this.taskManager;
        if (taskManager) {
          this.logger.debug('停止 TaskManager...');
          await taskManager.cleanup();
        }

        // 关闭 Socket.IO
        this.logger.debug('关闭 Socket.IO...');
        this.io.close();

        // 关闭 HTTP 服务器
        this.logger.debug('关闭 HTTP 服务器...');
        this.server.close(() => {
          stopTimer();
          this.logger.info('✅ API 服务器已停止');
          resolve();
        });
      } catch (stopError) {
        stopTimer();
        this.logger.error('停止 API 服务器时发生错误', {
          error: stopError instanceof Error ? stopError.message : stopError,
        });
        resolve(); // 即使出错也要 resolve，确保优雅关闭
      }
    });
  }

  /**
   * 获取服务器状态
   */
  public getServerStatus(): {
    running: boolean;
    taskManager: boolean;
    totalTasks: number;
    connectedClients: number;
  } {
    return {
      running: this.server.listening,
      taskManager: this.taskManager !== null,
      totalTasks: this.taskManager?.getAllTasks().length || 0,
      connectedClients: this.io.sockets.sockets.size,
    };
  }
}
