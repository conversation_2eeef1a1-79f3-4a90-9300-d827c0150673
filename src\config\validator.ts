/**
 * 配置验证器
 */

import { NodexConfig, TraversalStrategy, StorageType, ProxyStrategy, LogLevel } from '@/interfaces';
import { ValidationResult } from '@/types/utils';

/**
 * 配置验证器类
 */
export class ConfigValidator {
  /**
   * 验证完整配置
   */
  public validateConfig(config: NodexConfig): ValidationResult<NodexConfig> {
    const errors: string[] = [];

    // 验证任务配置
    const taskErrors = this.validateTaskConfig(config.task);
    errors.push(...taskErrors);

    // 验证抓取范围配置
    const scopeErrors = this.validateCrawlScopeConfig(config.crawlScope);
    errors.push(...scopeErrors);

    // 验证深度控制配置
    const depthErrors = this.validateDepthControlConfig(config.depthControl);
    errors.push(...depthErrors);

    // 验证并发配置
    const concurrencyErrors = this.validateConcurrencyConfig(config.concurrency);
    errors.push(...concurrencyErrors);

    // 验证代理配置
    const proxyErrors = this.validateProxyConfig(config.proxy);
    errors.push(...proxyErrors);

    // 验证外部代理配置文件路径
    if (config.proxyJson !== undefined) {
      const proxyJsonErrors = this.validateProxyJsonConfig(config.proxyJson);
      errors.push(...proxyJsonErrors);
    }

    // 验证存储配置
    const storageErrors = this.validateStorageConfig(config.storage);
    errors.push(...storageErrors);

    // 验证队列配置
    const queueErrors = this.validateQueueConfig(config.queue);
    errors.push(...queueErrors);

    // 验证AI配置
    const aiErrors = this.validateAIConfig(config.ai);
    errors.push(...aiErrors);

    // 验证日志配置
    const loggingErrors = this.validateLoggingConfig(config.logging);
    errors.push(...loggingErrors);

    // 验证监控配置
    const monitoringErrors = this.validateMonitoringConfig(config.monitoring);
    errors.push(...monitoringErrors);

    if (errors.length > 0) {
      return { success: false, errors };
    }

    return { success: true, data: config };
  }

  /**
   * 验证任务配置
   */
  private validateTaskConfig(task: NodexConfig['task']): string[] {
    const errors: string[] = [];

    if (!task.seedUrls || !Array.isArray(task.seedUrls)) {
      errors.push('task.seedUrls must be an array');
    } else if (task.seedUrls.length === 0) {
      errors.push('task.seedUrls cannot be empty');
    } else {
      task.seedUrls.forEach((url, index) => {
        if (!this.isValidUrl(url)) {
          errors.push(`task.seedUrls[${index}] is not a valid URL: ${url}`);
        }
      });
    }

    if (!Object.values(TraversalStrategy).includes(task.traversalStrategy)) {
      errors.push(
        `task.traversalStrategy must be one of: ${Object.values(TraversalStrategy).join(', ')}`
      );
    }

    return errors;
  }

  /**
   * 验证抓取范围配置
   */
  private validateCrawlScopeConfig(scope: NodexConfig['crawlScope']): string[] {
    const errors: string[] = [];

    if (!Array.isArray(scope.allowedDomains)) {
      errors.push('crawlScope.allowedDomains must be an array');
    }

    if (!Array.isArray(scope.disallowedPatterns)) {
      errors.push('crawlScope.disallowedPatterns must be an array');
    }

    if (typeof scope.followSubdomains !== 'boolean') {
      errors.push('crawlScope.followSubdomains must be a boolean');
    }

    if (typeof scope.ignoreWWW !== 'boolean') {
      errors.push('crawlScope.ignoreWWW must be a boolean');
    }

    if (scope.maxFileSize && (typeof scope.maxFileSize !== 'number' || scope.maxFileSize <= 0)) {
      errors.push('crawlScope.maxFileSize must be a positive number');
    }

    return errors;
  }

  /**
   * 验证深度控制配置
   */
  private validateDepthControlConfig(depth: NodexConfig['depthControl']): string[] {
    const errors: string[] = [];

    if (typeof depth.global !== 'number' || depth.global < 0) {
      errors.push('depthControl.global must be a non-negative number');
    }

    if (depth.perDomain) {
      Object.entries(depth.perDomain).forEach(([domain, depthValue]) => {
        if (typeof depthValue !== 'number' || depthValue < 0) {
          errors.push(`depthControl.perDomain.${domain} must be a non-negative number`);
        }
      });
    }

    return errors;
  }

  /**
   * 验证并发配置
   */
  private validateConcurrencyConfig(concurrency: NodexConfig['concurrency']): string[] {
    const errors: string[] = [];

    if (
      typeof concurrency.maxConcurrentRequests !== 'number' ||
      concurrency.maxConcurrentRequests <= 0
    ) {
      errors.push('concurrency.maxConcurrentRequests must be a positive number');
    }

    if (
      typeof concurrency.delayBetweenRequestsMs !== 'number' ||
      concurrency.delayBetweenRequestsMs < 0
    ) {
      errors.push('concurrency.delayBetweenRequestsMs must be a non-negative number');
    }

    if (
      concurrency.requestTimeoutMs &&
      (typeof concurrency.requestTimeoutMs !== 'number' || concurrency.requestTimeoutMs <= 0)
    ) {
      errors.push('concurrency.requestTimeoutMs must be a positive number');
    }

    if (
      concurrency.maxRetries &&
      (typeof concurrency.maxRetries !== 'number' || concurrency.maxRetries < 0)
    ) {
      errors.push('concurrency.maxRetries must be a non-negative number');
    }

    return errors;
  }

  /**
   * 验证代理配置
   */
  private validateProxyConfig(proxy: NodexConfig['proxy']): string[] {
    const errors: string[] = [];

    if (typeof proxy.enabled !== 'boolean') {
      errors.push('proxy.enabled must be a boolean');
    }

    if (!Array.isArray(proxy.proxies)) {
      errors.push('proxy.proxies must be an array');
    } else {
      // 验证每个代理配置
      proxy.proxies.forEach((p, index) => {
        if (!p.host || typeof p.host !== 'string') {
          errors.push(`proxy.proxies[${index}].host must be a non-empty string`);
        }
        if (!p.port || typeof p.port !== 'number' || p.port <= 0 || p.port > 65535) {
          errors.push(`proxy.proxies[${index}].port must be a valid port number`);
        }
      });
    }

    // 验证额外代理配置文件路径
    if (proxy.extraProxyConfig !== undefined) {
      if (typeof proxy.extraProxyConfig !== 'string') {
        errors.push('proxy.extraProxyConfig must be a string');
      } else if (proxy.extraProxyConfig.trim() === '') {
        errors.push('proxy.extraProxyConfig cannot be empty');
      } else {
        // 验证文件扩展名
        const validExtensions = ['.json', '.js', '.ts'];
        const hasValidExtension = validExtensions.some(ext =>
          proxy.extraProxyConfig!.toLowerCase().endsWith(ext)
        );
        if (!hasValidExtension) {
          errors.push(
            `proxy.extraProxyConfig must have one of these extensions: ${validExtensions.join(', ')}`
          );
        }
      }
    }

    if (!Object.values(ProxyStrategy).includes(proxy.strategy)) {
      errors.push(`proxy.strategy must be one of: ${Object.values(ProxyStrategy).join(', ')}`);
    }

    // 验证健康检查配置
    if (proxy.healthCheck) {
      if (typeof proxy.healthCheck.enabled !== 'boolean') {
        errors.push('proxy.healthCheck.enabled must be a boolean');
      }
      if (proxy.healthCheck.enabled) {
        if (
          !proxy.healthCheck.intervalMs ||
          typeof proxy.healthCheck.intervalMs !== 'number' ||
          proxy.healthCheck.intervalMs <= 0
        ) {
          errors.push('proxy.healthCheck.intervalMs must be a positive number');
        }
        if (!proxy.healthCheck.testUrl || typeof proxy.healthCheck.testUrl !== 'string') {
          errors.push('proxy.healthCheck.testUrl must be a non-empty string');
        }
      }
    }

    return errors;
  }

  /**
   * 验证存储配置
   */
  private validateStorageConfig(storage: NodexConfig['storage']): string[] {
    const errors: string[] = [];

    if (!Object.values(StorageType).includes(storage.type)) {
      errors.push(`storage.type must be one of: ${Object.values(StorageType).join(', ')}`);
    }

    if (storage.type === StorageType.MONGODB && !storage.connectionString) {
      errors.push('storage.connectionString is required for MongoDB');
    }

    if (storage.batchSize && (typeof storage.batchSize !== 'number' || storage.batchSize <= 0)) {
      errors.push('storage.batchSize must be a positive number');
    }

    return errors;
  }

  /**
   * 验证队列配置
   */
  private validateQueueConfig(queue: NodexConfig['queue']): string[] {
    const errors: string[] = [];

    if (!queue.redis.host) {
      errors.push('queue.redis.host is required');
    }

    if (typeof queue.redis.port !== 'number' || queue.redis.port <= 0 || queue.redis.port > 65535) {
      errors.push('queue.redis.port must be a valid port number');
    }

    return errors;
  }

  /**
   * 验证AI配置
   */
  private validateAIConfig(ai: NodexConfig['ai']): string[] {
    const errors: string[] = [];

    if (typeof ai.enabled !== 'boolean') {
      errors.push('ai.enabled must be a boolean');
    }

    if (ai.enabled) {
      if (ai.defaultProvider === 'openai' && !ai.openai?.apiKey) {
        errors.push('ai.openai.apiKey is required when using OpenAI');
      }

      if (ai.defaultProvider === 'gemini' && !ai.gemini?.apiKey) {
        errors.push('ai.gemini.apiKey is required when using Gemini');
      }
    }

    return errors;
  }

  /**
   * 验证日志配置
   */
  private validateLoggingConfig(logging: NodexConfig['logging']): string[] {
    const errors: string[] = [];

    if (!Object.values(LogLevel).includes(logging.level)) {
      errors.push(`logging.level must be one of: ${Object.values(LogLevel).join(', ')}`);
    }

    return errors;
  }

  /**
   * 验证监控配置
   */
  private validateMonitoringConfig(monitoring: NodexConfig['monitoring']): string[] {
    const errors: string[] = [];

    if (typeof monitoring.enabled !== 'boolean') {
      errors.push('monitoring.enabled must be a boolean');
    }

    if (
      monitoring.port &&
      (typeof monitoring.port !== 'number' || monitoring.port <= 0 || monitoring.port > 65535)
    ) {
      errors.push('monitoring.port must be a valid port number');
    }

    return errors;
  }

  /**
   * 验证外部代理配置文件路径
   */
  private validateProxyJsonConfig(proxyJson: string): string[] {
    const errors: string[] = [];

    if (typeof proxyJson !== 'string') {
      errors.push('proxyJson must be a string');
    } else if (proxyJson.trim() === '') {
      errors.push('proxyJson cannot be empty');
    } else if (!proxyJson.endsWith('.json')) {
      errors.push('proxyJson must be a JSON file path');
    }

    return errors;
  }

  /**
   * 验证URL格式
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}
