/**
 * AI 解析器实现
 */

import {
  IParser,
  ParserInput,
  ParserOutput,
  AIParserOptions,
} from '@/interfaces';
import { AIEngine } from '@/modules/ai/ai-engine';

/**
 * AI 解析器实现类
 */
export class AIParser implements IParser {
  public readonly name = 'ai-parser';
  public readonly version = '1.0.0';
  public readonly description = 'AI-powered content parser using LLM';

  private aiEngine: AIEngine;
  private options: AIParserOptions;

  constructor(aiEngine: AIEngine, options: AIParserOptions) {
    this.aiEngine = aiEngine;
    this.options = {
      model: 'gpt-3.5-turbo',
      maxTokens: 4000,
      temperature: 0.1,
      extractLinks: true,
      linkExtractionPrompt: '提取页面中所有有价值的链接',
      ...options,
    };
  }

  /**
   * 判断此解析器是否适用于该页面
   */
  public canParse(input: ParserInput): boolean {
    // AI 解析器可以处理任何内容，但通常作为备选方案
    return Object.keys(this.options.schema).length > 0;
  }

  /**
   * 执行解析
   */
  public async parse(input: ParserInput): Promise<ParserOutput> {
    const startTime = Date.now();
    
    try {
      // 构建 AI 提示
      const prompt = this.buildPrompt(input);
      
      // 调用 AI 引擎
      const response = await this.aiEngine.generateResponse({
        prompt,
        model: this.options.model,
        maxTokens: this.options.maxTokens,
        temperature: this.options.temperature,
      });

      // 解析 AI 响应
      const parsedData = this.parseAIResponse(response.content);
      
      // 提取链接（如果启用）
      let extractedLinks: string[] = [];
      if (this.options.extractLinks) {
        extractedLinks = await this.extractLinks(input);
      }

      const parseTime = Date.now() - startTime;

      return {
        extractedData: parsedData,
        newUrls: extractedLinks,
        metadata: {
          parserName: this.name,
          parseTime,
          extractedFieldsCount: Object.keys(parsedData).length,
          discoveredUrlsCount: extractedLinks.length,
        },
      };

    } catch (error) {
      const parseTime = Date.now() - startTime;
      
      return {
        extractedData: {},
        newUrls: [],
        error: {
          message: error instanceof Error ? error.message : 'AI parsing failed',
          code: 'AI_PARSING_ERROR',
        },
        metadata: {
          parserName: this.name,
          parseTime,
          extractedFieldsCount: 0,
          discoveredUrlsCount: 0,
        },
      };
    }
  }

  /**
   * 构建 AI 提示
   */
  private buildPrompt(input: ParserInput): string {
    const { content, url } = input;
    const schema = this.options.schema;
    
    let prompt = `请从以下网页内容中提取结构化数据。\n\n`;
    prompt += `网页URL: ${url}\n\n`;
    prompt += `需要提取的字段:\n`;
    
    for (const [field, description] of Object.entries(schema)) {
      prompt += `- ${field}: ${description}\n`;
    }
    
    prompt += `\n请以JSON格式返回提取的数据，确保字段名与上述要求完全匹配。\n\n`;
    prompt += `网页内容:\n${content.substring(0, 8000)}\n\n`;
    prompt += `JSON结果:`;
    
    return prompt;
  }

  /**
   * 解析 AI 响应
   */
  private parseAIResponse(response: string): Record<string, any> {
    try {
      // 尝试提取 JSON 部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // 如果没有找到 JSON，尝试解析整个响应
      return JSON.parse(response);
      
    } catch (error) {
      console.warn('Failed to parse AI response as JSON:', error);
      
      // 回退到简单的文本解析
      const data: Record<string, any> = {};
      const schema = this.options.schema;
      
      for (const field of Object.keys(schema)) {
        // 简单的关键词匹配
        const regex = new RegExp(`${field}[:\\s]+([^\\n]+)`, 'i');
        const match = response.match(regex);
        if (match && match[1]) {
          data[field] = match[1].trim();
        }
      }
      
      return data;
    }
  }

  /**
   * 提取链接
   */
  private async extractLinks(input: ParserInput): Promise<string[]> {
    try {
      const linkPrompt = `${this.options.linkExtractionPrompt}\n\n网页内容:\n${input.content.substring(0, 4000)}\n\n请返回链接列表，每行一个链接:`;
      
      const response = await this.aiEngine.generateResponse({
        prompt: linkPrompt,
        model: this.options.model,
        maxTokens: 1000,
        temperature: 0.1,
      });

      // 解析链接
      const links = response.content
        .split('\n')
        .map((line: string) => line.trim())
        .filter((line: string) => line.startsWith('http'))
        .slice(0, 20); // 限制链接数量

      return links;

    } catch (error) {
      console.warn('Failed to extract links with AI:', error);
      return [];
    }
  }

  /**
   * 获取解析器配置
   */
  public getConfig(): AIParserOptions {
    return { ...this.options };
  }

  /**
   * 更新解析器配置
   */
  public updateConfig(newOptions: Partial<AIParserOptions>): void {
    this.options = { ...this.options, ...newOptions };
  }

  /**
   * 获取支持的内容类型
   */
  public getSupportedContentTypes(): string[] {
    return ['text/html', 'text/plain', 'application/json', 'text/xml'];
  }

  /**
   * 验证解析结果
   */
  public validateResult(data: Record<string, any>): boolean {
    const schema = this.options.schema;
    const requiredFields = Object.keys(schema);
    
    // 检查是否至少有一半的必需字段被提取
    const extractedFields = Object.keys(data).filter(key => 
      data[key] !== null && data[key] !== undefined && data[key] !== ''
    );
    
    return extractedFields.length >= Math.ceil(requiredFields.length * 0.5);
  }

  /**
   * 获取解析器统计信息
   */
  public getStats(): {
    totalParses: number;
    successfulParses: number;
    averageParseTime: number;
    averageConfidence: number;
  } {
    // 这里应该实现实际的统计逻辑
    return {
      totalParses: 0,
      successfulParses: 0,
      averageParseTime: 0,
      averageConfidence: 0,
    };
  }
}
