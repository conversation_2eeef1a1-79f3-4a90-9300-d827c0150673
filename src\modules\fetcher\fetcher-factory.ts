/**
 * 下载器工厂类
 */

import {
  <PERSON>etcher,
  IHttpFetcher,
  IBrowserFetcher,
  HttpRequestOptions,
  BrowserRequestOptions,
} from '@/interfaces';
import { HttpFetcher } from './http-fetcher';
// import { EnhancedHttpFetcher } from './enhanced-http-fetcher'; // 暂时禁用
import { BrowserFetcher } from './browser-fetcher';
import { StealthBrowserFetcher } from './stealth-browser-fetcher';
import { info } from '@/utils';

/**
 * 下载器类型枚举
 */
export enum FetcherType {
  /** 基础HTTP下载器 */
  HTTP_BASIC = 'http-basic',
  /** 增强HTTP下载器 */
  HTTP_ENHANCED = 'http-enhanced',
  /** 基础浏览器下载器 */
  BROWSER_BASIC = 'browser-basic',
  /** 反屏蔽浏览器下载器 */
  BROWSER_STEALTH = 'browser-stealth',
}

/**
 * 下载器配置接口
 */
export interface FetcherConfig {
  /** 下载器类型 */
  type: FetcherType;
  /** 自定义配置 */
  options?: any;
  /** 是否启用 */
  enabled?: boolean;
  /** 优先级 */
  priority?: number;
}

/**
 * 下载器工厂类
 */
export class FetcherFactory {
  private static instance: FetcherFactory;
  private fetcherCache: Map<string, IFetcher> = new Map();

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): FetcherFactory {
    if (!FetcherFactory.instance) {
      FetcherFactory.instance = new FetcherFactory();
    }
    return FetcherFactory.instance;
  }

  /**
   * 创建下载器
   */
  public createFetcher(config: FetcherConfig): IFetcher {
    const cacheKey = this.generateCacheKey(config);
    
    // 检查缓存
    if (this.fetcherCache.has(cacheKey)) {
      const cachedFetcher = this.fetcherCache.get(cacheKey)!;
      info(`Using cached fetcher: ${cachedFetcher.name}`);
      return cachedFetcher;
    }

    let fetcher: IFetcher;

    switch (config.type) {
      case FetcherType.HTTP_BASIC:
        fetcher = new HttpFetcher();
        break;

      case FetcherType.HTTP_ENHANCED:
        // fetcher = new EnhancedHttpFetcher(); // 暂时禁用，使用基础HTTP下载器
        fetcher = new HttpFetcher();
        break;

      case FetcherType.BROWSER_BASIC:
        fetcher = new BrowserFetcher(config.options);
        break;

      case FetcherType.BROWSER_STEALTH:
        fetcher = new StealthBrowserFetcher(config.options);
        break;

      default:
        throw new Error(`Unsupported fetcher type: ${config.type}`);
    }

    // 缓存下载器
    this.fetcherCache.set(cacheKey, fetcher);
    info(`Created new fetcher: ${fetcher.name} (${fetcher.type})`);

    return fetcher;
  }

  /**
   * 批量创建下载器
   */
  public createFetchers(configs: FetcherConfig[]): IFetcher[] {
    return configs
      .filter(config => config.enabled !== false)
      .sort((a, b) => (b.priority || 0) - (a.priority || 0))
      .map(config => this.createFetcher(config));
  }

  /**
   * 创建默认下载器集合
   */
  public createDefaultFetchers(): IFetcher[] {
    const defaultConfigs: FetcherConfig[] = [
      {
        type: FetcherType.HTTP_BASIC,
        enabled: true,
        priority: 1,
      },
      {
        type: FetcherType.HTTP_ENHANCED,
        enabled: true,
        priority: 2,
      },
      {
        type: FetcherType.BROWSER_BASIC,
        enabled: true,
        priority: 3,
        options: {
          headless: true,
          args: ['--no-sandbox', '--disable-setuid-sandbox'],
        },
      },
      {
        type: FetcherType.BROWSER_STEALTH,
        enabled: true,
        priority: 4,
        options: {
          headless: true,
          args: ['--no-sandbox', '--disable-setuid-sandbox'],
        },
      },
    ];

    return this.createFetchers(defaultConfigs);
  }

  /**
   * 根据URL特征推荐下载器类型
   */
  public recommendFetcherType(url: string, options?: HttpRequestOptions | BrowserRequestOptions): FetcherType {
    try {
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname.toLowerCase();
      const pathname = parsedUrl.pathname.toLowerCase();

      // 检查是否是已知的反爬虫网站
      const protectedDomains = [
        'cloudflare.com',
        'amazonaws.com',
        'google.com',
        'facebook.com',
        'twitter.com',
        'linkedin.com',
        'instagram.com',
        'youtube.com',
      ];

      if (protectedDomains.some(protectedDomain => 
        domain.includes(protectedDomain) || domain.endsWith(protectedDomain)
      )) {
        return FetcherType.BROWSER_STEALTH;
      }

      // 检查是否是动态内容
      const dynamicIndicators = [
        '/api/',
        '/ajax/',
        'json',
        'xml',
        'graphql',
      ];

      if (dynamicIndicators.some(indicator => pathname.includes(indicator)) ||
          parsedUrl.search.length > 0) {
        return FetcherType.BROWSER_BASIC;
      }

      // 检查文件类型
      const staticFileExtensions = [
        '.html', '.htm', '.txt', '.xml', '.rss', '.atom',
        '.css', '.js', '.json',
        '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
        '.pdf', '.doc', '.docx', '.xls', '.xlsx',
      ];

      const hasStaticExtension = staticFileExtensions.some(ext => 
        pathname.endsWith(ext)
      );

      if (hasStaticExtension) {
        return FetcherType.HTTP_ENHANCED;
      }

      // 默认推荐增强HTTP下载器
      return FetcherType.HTTP_ENHANCED;

    } catch {
      return FetcherType.HTTP_BASIC;
    }
  }

  /**
   * 创建推荐的下载器
   */
  public createRecommendedFetcher(
    url: string, 
    options?: HttpRequestOptions | BrowserRequestOptions
  ): IFetcher {
    const recommendedType = this.recommendFetcherType(url, options);
    return this.createFetcher({ type: recommendedType });
  }

  /**
   * 获取所有支持的下载器类型
   */
  public getSupportedTypes(): FetcherType[] {
    return Object.values(FetcherType);
  }

  /**
   * 检查下载器类型是否支持
   */
  public isTypeSupported(type: FetcherType): boolean {
    return Object.values(FetcherType).includes(type);
  }

  /**
   * 获取下载器类型描述
   */
  public getTypeDescription(type: FetcherType): string {
    const descriptions = {
      [FetcherType.HTTP_BASIC]: '基础HTTP下载器，适用于简单的HTTP请求',
      [FetcherType.HTTP_ENHANCED]: '增强HTTP下载器，支持反屏蔽和高级功能',
      [FetcherType.BROWSER_BASIC]: '基础浏览器下载器，适用于动态内容',
      [FetcherType.BROWSER_STEALTH]: '反屏蔽浏览器下载器，适用于受保护的网站',
    };

    return descriptions[type] || '未知下载器类型';
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.fetcherCache.clear();
    info('Fetcher cache cleared');
  }

  /**
   * 获取缓存统计
   */
  public getCacheStats(): {
    totalCached: number;
    cacheKeys: string[];
  } {
    return {
      totalCached: this.fetcherCache.size,
      cacheKeys: Array.from(this.fetcherCache.keys()),
    };
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(config: FetcherConfig): string {
    return `${config.type}-${JSON.stringify(config.options || {})}`;
  }

  /**
   * 验证下载器配置
   */
  public validateConfig(config: FetcherConfig): boolean {
    if (!config.type || !this.isTypeSupported(config.type)) {
      return false;
    }

    // 可以添加更多的配置验证逻辑
    return true;
  }

  /**
   * 创建自定义下载器配置
   */
  public createCustomConfig(
    type: FetcherType,
    options?: any,
    enabled = true,
    priority = 0
  ): FetcherConfig {
    return {
      type,
      options,
      enabled,
      priority,
    };
  }

  /**
   * 获取下载器性能建议
   */
  public getPerformanceRecommendations(url: string): {
    recommendedType: FetcherType;
    reasons: string[];
    alternatives: FetcherType[];
  } {
    const recommendedType = this.recommendFetcherType(url);
    const reasons: string[] = [];
    const alternatives: FetcherType[] = [];

    try {
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname.toLowerCase();

      if (recommendedType === FetcherType.BROWSER_STEALTH) {
        reasons.push('检测到可能的反爬虫保护');
        reasons.push('建议使用反屏蔽浏览器下载器');
        alternatives.push(FetcherType.BROWSER_BASIC, FetcherType.HTTP_ENHANCED);
      } else if (recommendedType === FetcherType.BROWSER_BASIC) {
        reasons.push('检测到动态内容或JavaScript渲染');
        reasons.push('建议使用浏览器下载器');
        alternatives.push(FetcherType.BROWSER_STEALTH, FetcherType.HTTP_ENHANCED);
      } else if (recommendedType === FetcherType.HTTP_ENHANCED) {
        reasons.push('适合使用增强HTTP下载器');
        reasons.push('支持反屏蔽和高级功能');
        alternatives.push(FetcherType.HTTP_BASIC, FetcherType.BROWSER_BASIC);
      } else {
        reasons.push('适合使用基础HTTP下载器');
        alternatives.push(FetcherType.HTTP_ENHANCED, FetcherType.BROWSER_BASIC);
      }

    } catch {
      reasons.push('URL解析失败，使用默认推荐');
    }

    return {
      recommendedType,
      reasons,
      alternatives,
    };
  }
}
