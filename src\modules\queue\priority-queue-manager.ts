/**
 * 优先级队列管理器
 */

import { Queue, QueueOptions } from 'bullmq';
import {
  FetchTask,
  ParseTask,
  TraversalStrategy,
  IStrategyManager,
  QueueConfig,
  CrawlTask,
} from '@/interfaces';
import { getLogger, info } from '@/utils';

/**
 * 优先级队列配置
 */
export interface PriorityQueueConfig {
  /** 队列名称 */
  name: string;
  /** 遍历策略 */
  strategy: TraversalStrategy;
  /** 最大优先级值 */
  maxPriority: number;
  /** 最小优先级值 */
  minPriority: number;
  /** 是否启用延迟队列 */
  enableDelayedQueue: boolean;
}

/**
 * 优先级队列管理器类
 */
export class PriorityQueueManager {
  private queues: Map<string, Queue> = new Map();
  private strategyManager: IStrategyManager;
  private config: QueueConfig;
  private queueConfigs: Map<string, PriorityQueueConfig> = new Map();
  private logger = getLogger('PriorityQueueManager');

  constructor(strategyManager: IStrategyManager, config: QueueConfig) {
    this.strategyManager = strategyManager;
    this.config = config;
  }

  /**
   * 创建优先级队列
   */
  public async createPriorityQueue(
    queueConfig: PriorityQueueConfig,
    options?: QueueOptions
  ): Promise<Queue> {
    const redisConnection = {
      host: this.config.redis.host,
      port: this.config.redis.port,
      password: this.config.redis.password,
      db: this.config.redis.db,
    };

    const queueOptions: QueueOptions = {
      connection: redisConnection,
      defaultJobOptions: {
        ...this.config.defaultJobOptions,
        // 确保优先级队列正确配置
        priority: 0,
      },
      ...options,
    };

    const queue = new Queue(queueConfig.name, queueOptions);
    
    this.queues.set(queueConfig.name, queue);
    this.queueConfigs.set(queueConfig.name, queueConfig);

    this.logger.info('创建优先级队列', {
      name: queueConfig.name,
      strategy: queueConfig.strategy,
      maxPriority: queueConfig.maxPriority,
      minPriority: queueConfig.minPriority,
    });
    return queue;
  }

  /**
   * 添加任务到优先级队列
   */
  public async addTaskWithPriority<T extends FetchTask | ParseTask>(
    queueName: string,
    task: T,
    customPriority?: number
  ): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue not found: ${queueName}`);
    }

    const queueConfig = this.queueConfigs.get(queueName);
    if (!queueConfig) {
      throw new Error(`Queue configuration not found: ${queueName}`);
    }

    // 计算优先级
    let priority = customPriority;
    if (priority === undefined) {
      priority = this.calculatePriority(task, queueConfig);
    }

    // 确保优先级在有效范围内
    priority = Math.max(queueConfig.minPriority, Math.min(queueConfig.maxPriority, priority));

    // 计算延迟（如果启用）
    let delay = 0;
    if (queueConfig.enableDelayedQueue && 'url' in task) {
      delay = this.calculateDelay(typeof task.url === 'string' ? task.url : task.url.url);
    }

    await queue.add(
      queueConfig.strategy === TraversalStrategy.BFS ? 'bfs-task' : 'dfs-task',
      task,
      {
        priority,
        delay,
        attempts: 'maxRetries' in task ? task.maxRetries + 1 : 3,
        backoff: this.config.defaultJobOptions?.backoff,
      }
    );
  }

  /**
   * 批量添加任务到优先级队列
   */
  public async addBatchTasksWithPriority<T extends FetchTask | ParseTask>(
    queueName: string,
    tasks: T[]
  ): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue not found: ${queueName}`);
    }

    const queueConfig = this.queueConfigs.get(queueName);
    if (!queueConfig) {
      throw new Error(`Queue configuration not found: ${queueName}`);
    }

    const jobs = tasks.map(task => {
      const priority = Math.max(
        queueConfig.minPriority,
        Math.min(queueConfig.maxPriority, this.calculatePriority(task, queueConfig))
      );

      const delay = queueConfig.enableDelayedQueue && 'url' in task
        ? this.calculateDelay(typeof task.url === 'string' ? task.url : task.url.url)
        : 0;

      return {
        name: queueConfig.strategy === TraversalStrategy.BFS ? 'bfs-task' : 'dfs-task',
        data: task,
        opts: {
          priority,
          delay,
          attempts: 'maxRetries' in task ? task.maxRetries + 1 : 3,
          backoff: this.config.defaultJobOptions?.backoff,
        },
      };
    });

    await queue.addBulk(jobs);
  }

  /**
   * 计算任务优先级
   */
  private calculatePriority<T extends FetchTask | ParseTask>(
    task: T,
    queueConfig: PriorityQueueConfig
  ): number {
    // 如果任务是FetchTask，使用策略管理器计算优先级
    if ('url' in task && typeof task.url === 'object' && this.strategyManager) {
      const fetchTask = task as FetchTask;
      const crawlTask: CrawlTask = {
        id: fetchTask.id,
        url: fetchTask.url,
        status: fetchTask.status,
        createdAt: fetchTask.createdAt,
        priority: fetchTask.priority,
        retryCount: fetchTask.retryCount,
        maxRetries: fetchTask.maxRetries,
        tags: fetchTask.tags,
        context: fetchTask.context,
      };
      return this.strategyManager.calculateTaskPriority(crawlTask);
    }

    // 默认优先级计算
    const baseTime = Date.now();
    const depth = 'url' in task && typeof task.url === 'object' ? task.url.depth : 0;

    switch (queueConfig.strategy) {
      case TraversalStrategy.BFS:
        // 广度优先：深度越浅，优先级越高（数值越小）
        return baseTime + (depth * 1000);
      
      case TraversalStrategy.DFS:
        // 深度优先：深度越深，优先级越高（数值越小）
        return baseTime - (depth * 1000);
      
      default:
        return baseTime;
    }
  }

  /**
   * 计算任务延迟
   */
  private calculateDelay(url: string): number {
    try {
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname;
      
      // 使用策略管理器获取速率限制延迟
      return this.strategyManager.getRateLimitDelay(domain);
    } catch {
      return 0;
    }
  }

  /**
   * 获取队列统计信息
   */
  public async getQueueStats(queueName: string): Promise<{
    name: string;
    strategy: TraversalStrategy;
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: boolean;
  } | null> {
    const queue = this.queues.get(queueName);
    const queueConfig = this.queueConfigs.get(queueName);
    
    if (!queue || !queueConfig) {
      return null;
    }

    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    const isPaused = await queue.isPaused();

    return {
      name: queueName,
      strategy: queueConfig.strategy,
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      paused: isPaused,
    };
  }

  /**
   * 获取所有队列的统计信息
   */
  public async getAllQueueStats(): Promise<Array<{
    name: string;
    strategy: TraversalStrategy;
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: boolean;
  }>> {
    const stats = [];
    
    for (const queueName of this.queues.keys()) {
      const queueStats = await this.getQueueStats(queueName);
      if (queueStats) {
        stats.push(queueStats);
      }
    }

    return stats;
  }

  /**
   * 暂停队列
   */
  public async pauseQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName);
    if (queue) {
      await queue.pause();
      info(`Paused queue: ${queueName}`);
    }
  }

  /**
   * 恢复队列
   */
  public async resumeQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName);
    if (queue) {
      await queue.resume();
      info(`Resumed queue: ${queueName}`);
    }
  }

  /**
   * 清空队列
   */
  public async clearQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName);
    if (queue) {
      await queue.obliterate({ force: true });
      info(`Cleared queue: ${queueName}`);
    }
  }

  /**
   * 关闭所有队列
   */
  public async closeAllQueues(): Promise<void> {
    const closePromises = Array.from(this.queues.values()).map(queue => queue.close());
    await Promise.all(closePromises);
    
    this.queues.clear();
    this.queueConfigs.clear();
    
    info('All priority queues closed');
  }

  /**
   * 获取队列实例
   */
  public getQueue(queueName: string): Queue | undefined {
    return this.queues.get(queueName);
  }

  /**
   * 获取所有队列名称
   */
  public getQueueNames(): string[] {
    return Array.from(this.queues.keys());
  }

  /**
   * 更新队列策略
   */
  public updateQueueStrategy(queueName: string, strategy: TraversalStrategy): void {
    const queueConfig = this.queueConfigs.get(queueName);
    if (queueConfig) {
      queueConfig.strategy = strategy;
      info(`Updated queue ${queueName} strategy to ${strategy}`);
    }
  }
}
