/**
 * 存储工厂类
 */

import {
  IStorage,
  StorageType,
} from '@/interfaces';
import { MongoDBStorage } from './mongodb-storage';
import { MemoryStorage } from './memory-storage';
import { info } from '@/utils';

/**
 * 存储配置接口
 */
export interface StorageConfig {
  type: StorageType;
  name?: string;
  config: any;
  enabled?: boolean;
  priority?: number;
}



/**
 * 存储模板接口
 */
export interface StorageTemplate {
  name: string;
  description: string;
  type: StorageType;
  defaultConfig: StorageConfig;
  suggestedUseCase: string;
  requirements: string[];
}



/**
 * 存储工厂类
 */
export class StorageFactory {
  private static instance: StorageFactory;
  private storageCache: Map<string, IStorage> = new Map();
  private storageTemplates: Map<string, StorageTemplate> = new Map();

  private constructor() {
    this.initializeTemplates();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): StorageFactory {
    if (!StorageFactory.instance) {
      StorageFactory.instance = new StorageFactory();
    }
    return StorageFactory.instance;
  }

  /**
   * 创建存储实例
   */
  public createStorage(config: StorageConfig): IStorage {
    const cacheKey = this.generateStorageCacheKey(config);
    
    // 检查缓存
    if (this.storageCache.has(cacheKey)) {
      const cachedStorage = this.storageCache.get(cacheKey)!;
      info(`Using cached storage: ${cachedStorage.name}`);
      return cachedStorage;
    }

    let storage: IStorage;

    switch (config.type) {
      case StorageType.MONGODB:
        storage = new MongoDBStorage(config.config);
        break;

      case StorageType.MEMORY:
        storage = new MemoryStorage();
        break;

      case StorageType.FILE:
        throw new Error('File storage not implemented yet');

      default:
        throw new Error(`Unsupported storage type: ${config.type}`);
    }

    // 缓存存储实例
    this.storageCache.set(cacheKey, storage);
    info(`Created new storage: ${storage.name} (${storage.type})`);

    return storage;
  }



  /**
   * 从模板创建存储
   */
  public createStorageFromTemplate(templateName: string, customConfig?: any): IStorage {
    const template = this.storageTemplates.get(templateName);
    if (!template) {
      throw new Error(`Storage template '${templateName}' not found`);
    }

    const config = {
      ...template.defaultConfig,
      config: { ...template.defaultConfig.config, ...customConfig },
    };

    return this.createStorage(config);
  }



  /**
   * 获取存储模板
   */
  public getStorageTemplates(): StorageTemplate[] {
    return Array.from(this.storageTemplates.values());
  }

  /**
   * 获取特定存储模板
   */
  public getStorageTemplate(name: string): StorageTemplate | null {
    return this.storageTemplates.get(name) || null;
  }

  /**
   * 推荐存储配置
   */
  public recommendStorage(requirements: {
    dataVolume: 'small' | 'medium' | 'large';
    queryComplexity: 'simple' | 'complex';
    consistency: 'eventual' | 'strong';
    scalability: 'single' | 'distributed';
  }): StorageTemplate[] {
    const recommendations: StorageTemplate[] = [];

    for (const template of this.storageTemplates.values()) {
      let score = 0;

      // 根据数据量评分
      if (requirements.dataVolume === 'large' && template.type === StorageType.MONGODB) {
        score += 3;
      } else if (requirements.dataVolume === 'medium' && template.type === StorageType.MONGODB) {
        score += 2;
      } else if (requirements.dataVolume === 'small') {
        score += 1;
      }

      // 根据查询复杂度评分
      if (requirements.queryComplexity === 'complex' && template.type === StorageType.MONGODB) {
        score += 2;
      }

      // 根据一致性要求评分
      if (requirements.consistency === 'strong' && template.type === StorageType.MONGODB) {
        score += 2;
      }

      if (score > 0) {
        recommendations.push(template);
      }
    }

    return recommendations.sort((a, b) => b.defaultConfig.priority! - a.defaultConfig.priority!);
  }



  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.storageCache.clear();
    info('Storage factory cache cleared');
  }

  /**
   * 获取缓存统计
   */
  public getCacheStats(): {
    storageInstances: number;
    storageKeys: string[];
  } {
    return {
      storageInstances: this.storageCache.size,
      storageKeys: Array.from(this.storageCache.keys()),
    };
  }

  /**
   * 初始化模板
   */
  private initializeTemplates(): void {
    // MongoDB 存储模板
    this.storageTemplates.set('mongodb-default', {
      name: 'mongodb-default',
      description: 'MongoDB 默认配置，适用于大多数场景',
      type: StorageType.MONGODB,
      suggestedUseCase: '中大型数据存储，复杂查询需求',
      requirements: ['MongoDB 服务器', '网络连接'],
      defaultConfig: {
        type: StorageType.MONGODB,
        enabled: true,
        priority: 80,
        config: {
          connectionString: 'mongodb://localhost:27017',
          database: 'crawler_db',
          collection: 'crawled_data',
          options: {
            maxPoolSize: 10,
            serverSelectionTimeoutMS: 5000,
          },
        },
      },
    });


  }

  /**
   * 生成存储缓存键
   */
  private generateStorageCacheKey(config: StorageConfig): string {
    return `${config.type}-${JSON.stringify(config.config)}`;
  }

  /**
   * 验证存储配置
   */
  public validateStorageConfig(config: StorageConfig): boolean {
    switch (config.type) {
      case StorageType.MONGODB:
        return !!(config.config.connectionString && config.config.database && config.config.collection);

      default:
        return true;
    }
  }
}
