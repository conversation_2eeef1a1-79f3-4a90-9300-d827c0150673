/**
 * 存储相关接口定义
 */

import { StorageType } from '@/types/common';

/**
 * 存储项接口
 */
export interface StorageItem {
  /** 唯一标识符 */
  id?: string;
  /** 任务ID */
  taskId?: string;
  /** URL */
  url: string;
  /** 页面标题 */
  title?: string;
  /** 页面内容 */
  content?: string;
  /** 页面描述 */
  description?: string;
  /** 标签 */
  tags?: string[];
  /** 提取的数据 */
  data: Record<string, any>;
  /** 原始内容 */
  rawContent?: string;
  /** 内容类型 */
  contentType?: string;
  /** 页面深度 */
  depth: number;
  /** 父页面URL */
  parentUrl?: string;
  /** 抓取时间 */
  crawledAt: Date;
  /** 数据来源 */
  source?: {
    /** 下载器类型 */
    fetcherType: 'http' | 'browser';
    /** 解析器名称 */
    parserName: string;
    /** 用户代理 */
    userAgent?: string;
  };
  /** 元数据 */
  metadata?: {
    /** 响应时间 */
    responseTime?: number;
    /** 响应大小 */
    responseSize?: number;
    /** HTTP 状态码 */
    statusCode?: number;
    /** 响应头 */
    headers?: Record<string, string>;
  };

  /** 自定义字段 */
  customFields?: Record<string, any>;

  status?: string;
}

/**
 * 查询条件接口
 */
export interface QueryCondition {
  /** 字段名 */
  field: string;
  /** 操作符 */
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'exists';
  /** 值 */
  value: any;
}

/**
 * 查询选项接口
 */
export interface QueryOptions {
  /** 查询条件 */
  conditions?: QueryCondition[];
  /** 排序 */
  sort?: Array<{
    field: string;
    direction: 'asc' | 'desc';
  }>;
  /** 限制数量 */
  limit?: number;
  /** 跳过数量 */
  skip?: number;
  /** 投影字段 */
  projection?: string[];
  /** 是否包含原始内容 */
  includeRawContent?: boolean;
}

/**
 * 查询结果接口
 */
export interface QueryResult<T = StorageItem> {
  /** 结果数据 */
  data: T[];
  /** 总数量 */
  total: number;
  /** 当前页 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 查询时间 (毫秒) */
  queryTime: number;
  /** 聚合结果 - 兼容性字段 */
  aggregations?: Record<string, any>;
  /** 分面分布 - 兼容性字段 */
  facetDistribution?: Record<string, any>;
}

/**
 * 批量操作结果接口
 */
export interface BulkOperationResult {
  /** 成功数量 */
  successCount: number;
  /** 失败数量 */
  failureCount: number;
  /** 失败的项目 */
  failures?: Array<{
    item: StorageItem;
    error: string;
  }>;
  /** 操作时间 (毫秒) */
  operationTime: number;
  /** 持续时间 (毫秒) - 兼容性字段 */
  duration?: number;
}

/**
 * 存储统计信息接口
 */
export interface StorageStats {
  /** 总记录数 */
  totalRecords: number;
  /** 存储大小 (字节) */
  storageSize: number;
  /** 索引大小 (字节) */
  indexSize?: number;
  /** 平均文档大小 (字节) */
  averageDocumentSize: number;
  /** 最后更新时间 */
  lastUpdated: Date;
  /** 按日期分组的统计 */
  dailyStats?: Array<{
    date: string;
    count: number;
    size: number;
  }>;
  /** 按域名分组的统计 */
  domainStats?: Array<{
    domain: string;
    count: number;
    size: number;
  }>;
}

/**
 * 存储接口
 */
export interface IStorage {
  /** 存储类型 */
  readonly type: StorageType;
  /** 存储名称 */
  readonly name: string;

  /**
   * 初始化存储
   */
  initialize(): Promise<void>;

  /**
   * 关闭存储连接
   */
  close(): Promise<void>;

  /**
   * 保存单个项目
   * @param item 存储项
   */
  save(item: StorageItem): Promise<string>;

  /**
   * 批量保存项目
   * @param items 存储项列表
   */
  saveBatch(items: StorageItem[]): Promise<BulkOperationResult>;

  /**
   * 根据ID获取项目
   * @param id 项目ID
   */
  getById(id: string): Promise<StorageItem | null>;

  /**
   * 根据URL获取项目
   * @param url URL
   */
  getByUrl(url: string): Promise<StorageItem | null>;

  /**
   * 查询项目
   * @param options 查询选项
   */
  query(options: QueryOptions): Promise<QueryResult>;

  /**
   * 更新项目
   * @param id 项目ID
   * @param updates 更新数据
   */
  update(id: string, updates: Partial<StorageItem>): Promise<boolean>;

  /**
   * 删除项目
   * @param id 项目ID
   */
  delete(id: string): Promise<boolean>;

  /**
   * 批量删除项目
   * @param ids 项目ID列表
   */
  deleteBatch(ids: string[]): Promise<BulkOperationResult>;

  /**
   * 检查项目是否存在
   * @param url URL
   */
  exists(url: string): Promise<boolean>;

  /**
   * 获取存储统计信息
   */
  getStats(): Promise<StorageStats>;

  /**
   * 创建索引
   * @param field 字段名
   * @param options 索引选项
   */
  createIndex(field: string, options?: { unique?: boolean; sparse?: boolean }): Promise<void>;

  /**
   * 清空存储
   */
  clear(): Promise<void>;

  /**
   * 健康检查
   */
  healthCheck(): Promise<boolean>;
}

/**
 * 存储管理器接口
 */
export interface IStorageManager {
  /**
   * 注册存储
   * @param storage 存储实例
   */
  registerStorage(storage: IStorage): void;

  /**
   * 获取主存储
   */
  getPrimaryStorage(): IStorage;

  /**
   * 保存数据
   * @param item 存储项
   */
  save(item: StorageItem): Promise<string>;

  /**
   * 批量保存数据
   * @param items 存储项列表
   */
  saveBatch(items: StorageItem[]): Promise<BulkOperationResult>;

  /**
   * 查询数据
   * @param options 查询选项
   */
  query(options: QueryOptions): Promise<QueryResult>;

  /**
   * 初始化所有存储
   */
  initialize(): Promise<void>;

  /**
   * 关闭所有存储连接
   */
  close(): Promise<void>;

  /**
   * 获取存储统计信息
   */
  getStats(): Promise<Record<string, any>>;
}
