export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SOCKET_ERROR = 'SOCKET_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// 统一错误接口
export interface AppError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  code?: string;
  details?: any;
  timestamp: Date;
  source?: string;
}

// API 错误响应接口
export interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

/**
 * 创建应用错误
 */
export function createAppError(
  type: ErrorType,
  message: string,
  options: {
    severity?: ErrorSeverity;
    code?: string;
    details?: any;
    source?: string;
  } = {}
): AppError {
  return {
    type,
    message,
    severity: options.severity || ErrorSeverity.MEDIUM,
    code: options.code,
    details: options.details,
    source: options.source,
    timestamp: new Date(),
  };
}

/**
 * 解析 Socket.IO 错误
 */
export function parseSocketError(error: any): AppError {
  // Socket连接错误
  if (error && error.type === 'TransportError') {
    return createAppError(
      ErrorType.SOCKET_ERROR,
      'Socket.IO传输错误，正在尝试重新连接',
      { severity: ErrorSeverity.MEDIUM, source: 'socket', details: error }
    );
  }

  // Socket超时错误
  if (error && error.description && error.description.includes('timeout')) {
    return createAppError(
      ErrorType.SOCKET_ERROR,
      'Socket.IO连接超时',
      { severity: ErrorSeverity.MEDIUM, source: 'socket', details: error }
    );
  }

  // 数据验证错误
  if (error && error.message && error.message.includes('validation')) {
    return createAppError(
      ErrorType.VALIDATION_ERROR,
      '接收到的数据格式不正确',
      { severity: ErrorSeverity.LOW, source: 'socket', details: error }
    );
  }

  // 通用Socket错误
  if (error && typeof error === 'object' && error.message) {
    return createAppError(
      ErrorType.SOCKET_ERROR,
      error.message,
      { severity: ErrorSeverity.MEDIUM, source: 'socket', details: error }
    );
  }

  // 字符串错误
  if (typeof error === 'string') {
    return createAppError(
      ErrorType.SOCKET_ERROR,
      error,
      { severity: ErrorSeverity.LOW, source: 'socket' }
    );
  }

  // 默认Socket错误
  return createAppError(
    ErrorType.SOCKET_ERROR,
    '未知的Socket.IO错误',
    { severity: ErrorSeverity.LOW, source: 'socket', details: error }
  );
}

/**
 * 解析 API 错误
 */
export function parseApiError(error: any): AppError {
  // 网络错误
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return createAppError(
      ErrorType.NETWORK_ERROR,
      '网络连接失败，请检查网络连接',
      { severity: ErrorSeverity.HIGH, source: 'api' }
    );
  }

  // HTTP 错误
  if (error.message?.includes('HTTP')) {
    const statusMatch = error.message.match(/HTTP (\d+)/);
    const status = statusMatch ? parseInt(statusMatch[1]) : 0;
    
    let severity = ErrorSeverity.MEDIUM;
    let message = '服务器错误';
    
    if (status >= 400 && status < 500) {
      severity = ErrorSeverity.MEDIUM;
      message = status === 404 ? '请求的资源不存在' : '请求参数错误';
    } else if (status >= 500) {
      severity = ErrorSeverity.HIGH;
      message = '服务器内部错误';
    }
    
    return createAppError(
      ErrorType.API_ERROR,
      message,
      { 
        severity, 
        code: status.toString(),
        details: error.message,
        source: 'api'
      }
    );
  }

  // Socket 错误
  if (error.type === 'TransportError' || error.description?.includes('socket')) {
    return createAppError(
      ErrorType.SOCKET_ERROR,
      '实时连接失败',
      { severity: ErrorSeverity.MEDIUM, source: 'socket' }
    );
  }

  // 默认错误
  return createAppError(
    ErrorType.UNKNOWN_ERROR,
    error.message || '未知错误',
    { severity: ErrorSeverity.LOW, source: 'unknown' }
  );
}

/**
 * 错误日志记录
 */
export function logError(error: AppError): void {
  const logLevel = {
    [ErrorSeverity.LOW]: 'info',
    [ErrorSeverity.MEDIUM]: 'warn',
    [ErrorSeverity.HIGH]: 'error',
    [ErrorSeverity.CRITICAL]: 'error',
  }[error.severity];

  const logMessage = `[${error.type}] ${error.message}`;
  
  switch (logLevel) {
    case 'info':
      console.info(logMessage, error);
      break;
    case 'warn':
      console.warn(logMessage, error);
      break;
    case 'error':
      console.error(logMessage, error);
      break;
  }
}

/**
 * 获取用户友好的错误消息
 */
export function getUserFriendlyMessage(error: AppError): string {
  switch (error.type) {
    case ErrorType.NETWORK_ERROR:
      return '网络连接失败，请检查网络设置后重试';
    case ErrorType.API_ERROR:
      return error.message || '服务器响应异常，请稍后重试';
    case ErrorType.SOCKET_ERROR:
      return '实时连接中断，正在尝试重新连接...';
    case ErrorType.VALIDATION_ERROR:
      return error.message || '输入数据格式不正确';
    default:
      return '操作失败，请稍后重试';
  }
}

/**
 * 错误重试策略
 */
export function shouldRetry(error: AppError): boolean {
  switch (error.type) {
    case ErrorType.NETWORK_ERROR:
    case ErrorType.SOCKET_ERROR:
      return true;
    case ErrorType.API_ERROR:
      // 5xx 错误可以重试，4xx 错误不重试
      return error.code ? parseInt(error.code) >= 500 : false;
    default:
      return false;
  }
}

/**
 * Socket.IO 错误恢复策略
 */
export interface SocketErrorRecoveryStrategy {
  shouldReconnect: boolean;
  shouldUseFallbackData: boolean;
  shouldNotifyUser: boolean;
  retryDelay: number;
  maxRetries: number;
}

/**
 * 获取Socket.IO错误恢复策略
 */
export function getSocketErrorRecoveryStrategy(error: AppError): SocketErrorRecoveryStrategy {
  const baseStrategy: SocketErrorRecoveryStrategy = {
    shouldReconnect: false,
    shouldUseFallbackData: true,
    shouldNotifyUser: false,
    retryDelay: 1000,
    maxRetries: 3,
  };

  switch (error.severity) {
    case ErrorSeverity.HIGH:
      return {
        ...baseStrategy,
        shouldReconnect: true,
        shouldNotifyUser: true,
        retryDelay: 2000,
        maxRetries: 5,
      };

    case ErrorSeverity.MEDIUM:
      return {
        ...baseStrategy,
        shouldReconnect: true,
        retryDelay: 1500,
        maxRetries: 3,
      };

    case ErrorSeverity.LOW:
      return {
        ...baseStrategy,
        retryDelay: 1000,
        maxRetries: 2,
      };

    default:
      return baseStrategy;
  }
}

/**
 * Socket.IO 错误处理器类
 */
export class SocketErrorHandler {
  private retryCount = new Map<string, number>();
  private lastErrorTime = new Map<string, number>();

  /**
   * 处理Socket.IO错误
   */
  handleError(error: any, context: string = 'unknown'): {
    parsedError: AppError;
    strategy: SocketErrorRecoveryStrategy;
    shouldHandle: boolean;
  } {
    const parsedError = parseSocketError(error);
    const strategy = getSocketErrorRecoveryStrategy(parsedError);

    // 检查是否应该处理这个错误（避免重复处理）
    const errorKey = `${context}-${parsedError.type}`;
    const now = Date.now();
    const lastTime = this.lastErrorTime.get(errorKey) || 0;
    const currentRetries = this.retryCount.get(errorKey) || 0;

    // 如果错误发生太频繁，跳过处理
    if (now - lastTime < 1000) {
      return {
        parsedError,
        strategy,
        shouldHandle: false,
      };
    }

    // 如果重试次数超过限制，跳过处理
    if (currentRetries >= strategy.maxRetries) {
      return {
        parsedError,
        strategy: { ...strategy, shouldReconnect: false },
        shouldHandle: false,
      };
    }

    // 更新重试计数和时间
    this.retryCount.set(errorKey, currentRetries + 1);
    this.lastErrorTime.set(errorKey, now);

    return {
      parsedError,
      strategy,
      shouldHandle: true,
    };
  }

  /**
   * 重置错误计数
   */
  resetErrorCount(context: string): void {
    const keysToDelete = Array.from(this.retryCount.keys()).filter(key =>
      key.startsWith(context)
    );

    keysToDelete.forEach(key => {
      this.retryCount.delete(key);
      this.lastErrorTime.delete(key);
    });
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): { [context: string]: number } {
    const stats: { [context: string]: number } = {};

    this.retryCount.forEach((count, key) => {
      const context = key.split('-')[0];
      stats[context] = (stats[context] || 0) + count;
    });

    return stats;
  }
}

/**
 * 获取重试延迟时间（毫秒）
 */
export function getRetryDelay(attemptNumber: number): number {
  // 指数退避策略：1s, 2s, 4s, 8s, 16s
  return Math.min(1000 * Math.pow(2, attemptNumber - 1), 16000);
}
