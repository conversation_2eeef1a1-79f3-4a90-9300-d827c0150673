/**
 * 下载器相关接口定义
 */

import { HttpMethod, ProxyConfig, ErrorInfo } from '@/types/common';

/**
 * HTTP 请求选项
 */
export interface HttpRequestOptions {
  /** HTTP 方法 */
  method?: HttpMethod;
  /** 请求头 */
  headers?: Record<string, string>;
  /** 请求体 */
  body?: string | Buffer | Record<string, any>;
  /** 超时时间 (毫秒) */
  timeout?: number;
  /** 是否跟随重定向 */
  followRedirects?: boolean;
  /** 最大重定向次数 */
  maxRedirects?: number;
  /** 代理配置 */
  proxy?: ProxyConfig;
  /** 是否使用代理 */
  useProxy?: boolean;
  /** 用户代理 */
  userAgent?: string;
  /** Cookie */
  cookies?: Record<string, string>;
  /** 是否验证 SSL 证书 */
  validateSSL?: boolean;
  /** 自定义选项 */
  customOptions?: Record<string, any>;
  /** 是否使用随机用户代理 */
  randomUserAgent?: boolean;
  /** 引用页面 */
  referer?: string;
  /** 是否模拟真实浏览器 */
  simulateRealBrowser?: boolean;
}

/**
 * 浏览器请求选项
 */
export interface BrowserRequestOptions {
  /** 是否等待页面加载完成 */
  waitForLoad?: boolean;
  /** 等待特定选择器 */
  waitForSelector?: string;
  /** 等待时间 (毫秒) */
  waitTime?: number;
  /** 是否执行 JavaScript */
  executeJavaScript?: boolean;
  /** 自定义 JavaScript 代码 */
  customScript?: string;
  /** 截图选项 */
  screenshot?: {
    enabled: boolean;
    path?: string;
    fullPage?: boolean;
  };
  /** 视口大小 */
  viewport?: {
    width: number;
    height: number;
  };
  /** 是否无头模式 */
  headless?: boolean;
  /** 代理配置 */
  proxy?: ProxyConfig;
  /** 用户代理 */
  userAgent?: string;
  /** Cookie */
  cookies?: Array<{
    name: string;
    value: string;
    domain?: string;
    path?: string;
  }>;
  /** 自定义选项 */
  customOptions?: Record<string, any>;
  /** 是否模拟人类行为 */
  simulateHuman?: boolean;
}

/**
 * 下载响应接口
 */
export interface FetchResponse {
  /** 响应内容 */
  content: string;
  /** HTTP 状态码 */
  statusCode: number;
  /** 响应头 */
  headers: Record<string, string>;
  /** 内容类型 */
  contentType: string;
  /** 响应大小 (字节) */
  size: number;
  /** 响应时间 (毫秒) */
  responseTime: number;
  /** 最终URL (处理重定向后) */
  finalUrl: string;
  /** 下载器类型 */
  fetcherType?: 'http' | 'browser';
  /** 是否来自缓存 */
  fromCache?: boolean;
  /** 额外的元数据 */
  metadata?: Record<string, any>;
}

/**
 * 下载器接口
 */
export interface IFetcher {
  /** 下载器名称 */
  readonly name: string;
  /** 下载器类型 */
  readonly type: 'http' | 'browser';
  /** 下载器版本 */
  readonly version: string;

  /**
   * 执行下载
   * @param url 目标URL
   * @param options 请求选项
   */
  fetch(url: string, options?: HttpRequestOptions | BrowserRequestOptions): Promise<FetchResponse>;

  /**
   * 检查是否支持该URL
   * @param url 目标URL
   */
  canHandle(url: string): boolean;

  /**
   * 初始化下载器
   */
  initialize?(): Promise<void>;

  /**
   * 清理下载器资源
   */
  cleanup?(): Promise<void>;

  /**
   * 获取下载器统计信息
   */
  getStats?(): {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
  };
}

/**
 * HTTP 下载器接口
 */
export interface IHttpFetcher extends IFetcher {
  readonly type: 'http';

  /**
   * 执行 HTTP 请求
   * @param url 目标URL
   * @param options HTTP 请求选项
   */
  fetch(url: string, options?: HttpRequestOptions): Promise<FetchResponse>;
}

/**
 * 浏览器下载器接口
 */
export interface IBrowserFetcher extends IFetcher {
  readonly type: 'browser';

  /**
   * 执行浏览器请求
   * @param url 目标URL
   * @param options 浏览器请求选项
   */
  fetch(url: string, options?: BrowserRequestOptions): Promise<FetchResponse>;

  /**
   * 获取浏览器实例数量
   */
  getBrowserInstanceCount(): number;

  /**
   * 关闭所有浏览器实例
   */
  closeAllBrowsers(): Promise<void>;
}

/**
 * 代理管理器接口
 */
export interface IProxyManager {
  /**
   * 获取可用的代理
   */
  getProxy(): ProxyConfig | null;

  /**
   * 标记代理为失败
   * @param proxy 代理配置
   * @param error 错误信息
   */
  markProxyFailed(proxy: ProxyConfig, error: ErrorInfo): void;

  /**
   * 标记代理为成功
   * @param proxy 代理配置
   */
  markProxySuccess(proxy: ProxyConfig): void;

  /**
   * 添加代理
   * @param proxy 代理配置
   */
  addProxy(proxy: ProxyConfig): void;

  /**
   * 移除代理
   * @param proxy 代理配置
   */
  removeProxy(proxy: ProxyConfig): void;

  /**
   * 获取所有代理
   */
  getAllProxies(): ProxyConfig[];

  /**
   * 获取可用代理数量
   */
  getAvailableProxyCount(): number;

  /**
   * 启动健康检查
   */
  startHealthCheck(): void;

  /**
   * 停止健康检查
   */
  stopHealthCheck(): void;

  /**
   * 获取代理统计信息
   */
  getProxyStats(): Array<{
    proxy: ProxyConfig;
    status: 'active' | 'failed' | 'recovering';
    successCount: number;
    failureCount: number;
    lastUsed?: Date;
    lastChecked?: Date;
  }>;
}

/**
 * 下载器管理器接口
 */
export interface IFetcherManager {
  /**
   * 注册下载器
   * @param fetcher 下载器实例
   */
  registerFetcher(fetcher: IFetcher): void;

  /**
   * 注销下载器
   * @param name 下载器名称
   */
  unregisterFetcher(name: string): void;

  /**
   * 获取适用的下载器
   * @param url 目标URL
   * @param preferredType 首选下载器类型
   */
  getFetcher(url: string, preferredType?: 'http' | 'browser'): IFetcher | null;

  /**
   * 执行下载
   * @param url 目标URL
   * @param options 请求选项
   * @param preferredType 首选下载器类型
   */
  fetch(
    url: string,
    options?: HttpRequestOptions | BrowserRequestOptions,
    preferredType?: 'http' | 'browser'
  ): Promise<FetchResponse>;

  /**
   * 获取所有已注册的下载器
   */
  getRegisteredFetchers(): Map<string, IFetcher>;

  /**
   * 初始化所有下载器
   */
  initialize(): Promise<void>;

  /**
   * 清理所有下载器
   */
  cleanup(): Promise<void>;

  /**
   * 获取下载器统计信息
   */
  getStats(): Record<string, any>;
}
