#!/usr/bin/env node

/**
 * 测试新功能的脚本
 * 验证监控面板重构、Worker节点健康监控、任务管理操作和代理配置功能
 */

const http = require('http');
const fs = require('fs').promises;
const path = require('path');

// 测试配置
const TEST_CONFIG = {
  backendUrl: 'http://localhost:3001',
  frontendUrl: 'http://localhost:3002',
  timeout: 5000,
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP 请求工具
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = http.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = {
            statusCode: res.statusCode,
            headers: res.headers,
            data: res.headers['content-type']?.includes('application/json') 
              ? JSON.parse(data) 
              : data
          };
          resolve(result);
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(TEST_CONFIG.timeout, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

// 测试后端API
async function testBackendAPIs() {
  log('blue', '\n=== 测试后端监控API ===');

  const tests = [
    {
      name: '系统状态',
      url: `${TEST_CONFIG.backendUrl}/api/status`,
    },
    {
      name: '模块状态',
      url: `${TEST_CONFIG.backendUrl}/api/modules`,
    },
    {
      name: '队列状态',
      url: `${TEST_CONFIG.backendUrl}/api/queues`,
    },
    {
      name: '会话列表',
      url: `${TEST_CONFIG.backendUrl}/api/sessions`,
    },
    {
      name: 'Worker健康状况',
      url: `${TEST_CONFIG.backendUrl}/api/workers/health`,
    },
    {
      name: 'Fetcher统计',
      url: `${TEST_CONFIG.backendUrl}/api/workers/fetchers`,
    },
    {
      name: 'Parser统计',
      url: `${TEST_CONFIG.backendUrl}/api/workers/parsers`,
    },
  ];

  for (const test of tests) {
    try {
      const response = await makeRequest(test.url);
      if (response.statusCode === 200) {
        log('green', `✓ ${test.name}: 成功`);
      } else {
        log('yellow', `⚠ ${test.name}: HTTP ${response.statusCode}`);
      }
    } catch (error) {
      log('red', `✗ ${test.name}: ${error.message}`);
    }
  }
}

// 测试任务管理操作
async function testTaskManagement() {
  log('blue', '\n=== 测试任务管理操作 ===');

  const operations = [
    {
      name: '暂停系统',
      url: `${TEST_CONFIG.backendUrl}/api/control/pause`,
      method: 'POST',
    },
    {
      name: '恢复系统',
      url: `${TEST_CONFIG.backendUrl}/api/control/resume`,
      method: 'POST',
    },
  ];

  for (const op of operations) {
    try {
      const response = await makeRequest(op.url, {
        method: op.method,
        headers: { 'Content-Type': 'application/json' },
      });
      
      if (response.statusCode === 200) {
        log('green', `✓ ${op.name}: 成功`);
      } else {
        log('yellow', `⚠ ${op.name}: HTTP ${response.statusCode}`);
      }
    } catch (error) {
      log('red', `✗ ${op.name}: ${error.message}`);
    }
  }
}

// 测试前端可访问性
async function testFrontend() {
  log('blue', '\n=== 测试前端监控面板 ===');

  try {
    const response = await makeRequest(TEST_CONFIG.frontendUrl);
    if (response.statusCode === 200) {
      log('green', '✓ 前端监控面板: 可访问');
      
      // 检查是否包含预期的内容
      if (response.data.includes('Nodex 监控面板')) {
        log('green', '✓ 页面内容: 正确');
      } else {
        log('yellow', '⚠ 页面内容: 可能不完整');
      }
    } else {
      log('yellow', `⚠ 前端监控面板: HTTP ${response.statusCode}`);
    }
  } catch (error) {
    log('red', `✗ 前端监控面板: ${error.message}`);
  }
}

// 测试代理配置功能
async function testProxyConfiguration() {
  log('blue', '\n=== 测试代理配置功能 ===');

  // 创建测试代理配置文件
  const testProxyConfig = {
    proxies: [
      {
        host: 'test-proxy.example.com',
        port: 8080,
        auth: {
          username: 'testuser',
          password: 'testpass'
        }
      }
    ],
    weights: {
      'test-proxy.example.com:8080': 1
    }
  };

  const configPath = path.join(__dirname, '../test-proxy-config.json');

  try {
    // 写入测试配置文件
    await fs.writeFile(configPath, JSON.stringify(testProxyConfig, null, 2));
    log('green', '✓ 创建测试代理配置文件: 成功');

    // 验证文件存在
    const stats = await fs.stat(configPath);
    if (stats.isFile()) {
      log('green', '✓ 代理配置文件验证: 成功');
    }

    // 清理测试文件
    await fs.unlink(configPath);
    log('green', '✓ 清理测试文件: 成功');

  } catch (error) {
    log('red', `✗ 代理配置测试: ${error.message}`);
  }
}

// 检查依赖和环境
async function checkEnvironment() {
  log('blue', '\n=== 环境检查 ===');

  // 检查 Node.js 版本
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion >= 18) {
    log('green', `✓ Node.js 版本: ${nodeVersion} (支持)`);
  } else {
    log('red', `✗ Node.js 版本: ${nodeVersion} (需要 18+)`);
  }

  // 检查必要的目录
  const directories = [
    'src/frontend',
    'src/monitoring',
    'src/modules',
    'examples',
    'docs'
  ];

  for (const dir of directories) {
    try {
      const stats = await fs.stat(dir);
      if (stats.isDirectory()) {
        log('green', `✓ 目录存在: ${dir}`);
      }
    } catch (error) {
      log('red', `✗ 目录缺失: ${dir}`);
    }
  }

  // 检查关键文件
  const files = [
    'src/frontend/package.json',
    'src/frontend/next.config.js',
    'src/frontend/tailwind.config.js',
    'examples/proxy-config.json',
    'docs/proxy-configuration.md'
  ];

  for (const file of files) {
    try {
      await fs.access(file);
      log('green', `✓ 文件存在: ${file}`);
    } catch (error) {
      log('red', `✗ 文件缺失: ${file}`);
    }
  }
}

// 主测试函数
async function runTests() {
  log('cyan', '🚀 开始测试 Nodex 新功能...\n');

  try {
    await checkEnvironment();
    await testProxyConfiguration();
    await testBackendAPIs();
    await testTaskManagement();
    await testFrontend();

    log('cyan', '\n🎉 测试完成！');
    log('yellow', '\n注意事项:');
    log('yellow', '- 确保后端服务运行在 localhost:3001');
    log('yellow', '- 确保前端服务运行在 localhost:3002');
    log('yellow', '- 某些API可能需要系统完全启动后才能正常响应');

  } catch (error) {
    log('red', `\n❌ 测试过程中发生错误: ${error.message}`);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testBackendAPIs,
  testTaskManagement,
  testFrontend,
  testProxyConfiguration,
  checkEnvironment
};
