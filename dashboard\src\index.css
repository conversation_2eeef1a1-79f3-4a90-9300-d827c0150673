@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  /* 自定义滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }

  /* 动画类 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* 状态指示器 */
  .status-indicator {
    @apply inline-block w-2 h-2 rounded-full;
  }
  
  .status-indicator.healthy {
    @apply bg-green-500;
  }
  
  .status-indicator.warning {
    @apply bg-yellow-500;
  }
  
  .status-indicator.critical {
    @apply bg-red-500;
  }
  
  .status-indicator.stopped {
    @apply bg-gray-500;
  }

  /* 卡片阴影效果 */
  .card-shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }
  
  .card-shadow-hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .gradient-bg-success {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
  }
  
  .gradient-bg-warning {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  }
  
  .gradient-bg-danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  }
}

@layer utilities {
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }
  
  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 响应式隐藏 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* 焦点样式 */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset;
  }
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Chart.js 样式覆盖 */
.chartjs-tooltip {
  @apply bg-white border border-gray-200 rounded-lg shadow-lg;
}

.chartjs-tooltip-body {
  @apply text-gray-900;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .responsive-table {
    @apply block overflow-x-auto whitespace-nowrap;
  }
  
  .responsive-table table {
    @apply min-w-full;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}
