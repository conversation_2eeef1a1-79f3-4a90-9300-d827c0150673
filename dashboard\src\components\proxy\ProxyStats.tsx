import React from 'react';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface ProxyStatsData {
  total: number;
  available: number;
  healthy: number;
  failed: number;
}

interface ProxyStatsProps {
  stats: ProxyStatsData | null;
  loading: boolean;
}

export const ProxyStats: React.FC<ProxyStatsProps> = ({ stats, loading }) => {
  if (loading && !stats) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center">
          <LoadingSpinner size="sm" />
          <span className="ml-2 text-gray-500">加载代理统计中...</span>
        </div>
      </Card>
    );
  }

  const statItems = [
    {
      label: '总代理数',
      value: stats?.total || 0,
      color: 'text-gray-900',
      bgColor: 'bg-gray-50',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
    },
    {
      label: '可用代理',
      value: stats?.available || 0,
      color: 'text-blue-900',
      bgColor: 'bg-blue-50',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      label: '健康代理',
      value: stats?.healthy || 0,
      color: 'text-green-900',
      bgColor: 'bg-green-50',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
    },
    {
      label: '失败代理',
      value: stats?.failed || 0,
      color: 'text-red-900',
      bgColor: 'bg-red-50',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      ),
    },
  ];

  const healthyPercentage = stats?.total ? Math.round((stats.healthy / stats.total) * 100) : 0;
  const availablePercentage = stats?.total ? Math.round((stats.available / stats.total) * 100) : 0;

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900">代理统计</h3>
        {loading && (
          <LoadingSpinner size="sm" />
        )}
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {statItems.map((item, index) => (
          <div
            key={index}
            className={`${item.bgColor} rounded-lg p-4 border border-gray-200`}
          >
            <div className="flex items-center">
              <div className={`${item.color} mr-3`}>
                {item.icon}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">{item.label}</p>
                <p className={`text-2xl font-bold ${item.color}`}>{item.value}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 健康度指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 健康率 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">健康率</span>
            <span className="text-sm text-gray-500">{healthyPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                healthyPercentage >= 80
                  ? 'bg-green-500'
                  : healthyPercentage >= 60
                  ? 'bg-yellow-500'
                  : 'bg-red-500'
              }`}
              style={{ width: `${healthyPercentage}%` }}
            />
          </div>
        </div>

        {/* 可用率 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">可用率</span>
            <span className="text-sm text-gray-500">{availablePercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                availablePercentage >= 80
                  ? 'bg-blue-500'
                  : availablePercentage >= 60
                  ? 'bg-yellow-500'
                  : 'bg-red-500'
              }`}
              style={{ width: `${availablePercentage}%` }}
            />
          </div>
        </div>
      </div>
    </Card>
  );
};
