# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Nodex is an enterprise-grade intelligent web crawler system built with Node.js and TypeScript. It provides a RESTful API service for automated, large-scale, high-quality data extraction from any website topic with AI enhancement.

## Common Development Commands

### Build and Development
```bash
# Build the project
npm run build

# Development mode (both backend and frontend)
npm run dev

# Backend only (development)
npm run dev:backend

# Frontend only (development)
npm run dev:frontend

# Full build (includes frontend)
npm run build:full
```

### Testing and Quality
```bash
# Run tests
npm test

# Run tests in watch mode
npm test:watch

# Run tests with coverage
npm test:coverage

# Type checking
npm run type-check

# Type checking for all (backend + frontend)
npm run type-check:all

# Linting
npm run lint

# Linting with auto-fix
npm run lint:fix

# Lint all (backend + frontend)
npm run lint:all

# Code formatting
npm run format

# Check formatting
npm run format:check
```

### Production
```bash
# Start production server
npm run start:production

# Deploy build (clean + build + start)
npm run deploy:start
```

## Architecture Overview

The system follows a modular, event-driven architecture:

```
API Layer (Express + Socket.IO)
         ↓
App Manager (Application Lifecycle)
         ↓
Task Manager (Core Business Logic)
         ↓
Module System (Fetcher, Parser, Storage, AI)
         ↓
Queue System (BullMQ + Redis)
```

### Key Modules

- **App Manager** (`src/app/app-manager.ts`): Manages application lifecycle and TaskManager initialization
- **Task Manager** (`src/app/task-manager.ts`): Core business logic for crawling tasks
- **API Server** (`src/api/server.ts`): HTTP REST API and WebSocket server
- **Fetcher Module** (`src/modules/fetcher/`): HTTP/Browser-based content fetching with proxy support
- **Parser Module** (`src/modules/parser/`): Content parsing (Cheerio, AI, JSON, XML, Regex)
- **Storage Module** (`src/modules/storage/`): Data persistence (Memory, MongoDB)
- **AI Module** (`src/modules/ai/`): AI-enhanced content extraction (OpenAI, Gemini)
- **Queue System** (`src/modules/queue/`): Redis-based task queuing with BullMQ

### Path Aliases

The project uses TypeScript path aliases:
- `@/*` → `src/*`
- `@/shared-types` → `src/shared-types/index`
- `@/types/*` → `src/types/*`
- `@/interfaces/*` → `src/interfaces/*`
- `@/modules/*` → `src/modules/*`
- `@/utils/*` → `src/utils/*`
- `@/config/*` → `src/config/*`
- `@/app/*` → `src/app/*`
- `@/api/*` → `src/api/*`

## Frontend Architecture

The frontend is located in the `dashboard/` directory and is built with:
- **React + TypeScript + Vite**
- **Tailwind CSS** for styling
- **Socket.IO** for real-time communication
- Component structure: `dashboard/src/components/`
  - `crawler/` - Crawler management components
  - `proxy/` - Proxy management components  
  - `monitoring/` - System monitoring components
  - `ui/` - Reusable UI components

## Configuration

- Main config: `nodex.config.json`
- Environment variables: `.env` file
- TypeScript: `tsconfig.json` (strict mode enabled)
- ESLint: `eslint.config.js` (flat config format)
- Jest: `jest.config.js`

## Logging System

The project uses a sophisticated logging system with:
- Multiple log levels (DEBUG, INFO, WARN, ERROR)
- Environment variable control via `LOG_LEVEL`
- Colored console output (disable with `NO_COLOR=1`)
- Module-specific loggers via `getLogger('ModuleName')`
- Performance timing with `time()` function

```typescript
import { info, error, getLogger, time } from '@/utils';

const logger = getLogger('MyModule');
const timer = time('Operation');
// ... operation
timer(); // logs execution time
```

## Key Development Notes

1. **Module Pattern**: Each major functionality is organized as a self-contained module with factory patterns
2. **Event-Driven**: TaskManager emits events for status changes, which are propagated via WebSocket
3. **Dependency Injection**: Components receive dependencies through constructor injection
4. **Error Handling**: Use `errorWithStack()` utility for consistent error logging
5. **Proxy Management**: Built-in proxy rotation and health checking for anti-blocking
6. **AI Integration**: Supports multiple AI providers for intelligent content extraction
7. **Queue-Based**: Uses Redis/BullMQ for reliable background task processing

## Testing

- Test files: `**/*.test.ts` and `**/*.spec.ts`
- Setup file: `tests/setup.ts`
- Coverage reports generated in `coverage/` directory
- 30-second test timeout configured

## Production Considerations

- Set `NODE_ENV=production`
- Configure `STATIC_PATH` for frontend assets
- Use appropriate `LOG_LEVEL` (info/warn in production)
- Ensure Redis is available for queue operations
- Configure proxy settings for anti-blocking