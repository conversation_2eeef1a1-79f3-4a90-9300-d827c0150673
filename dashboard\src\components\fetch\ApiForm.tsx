/**
 * API抓取表单组件
 * 
 * 提供API抓取请求的配置界面
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { useNotification } from '@/contexts/NotificationContext';

interface ApiFormProps {
  onSubmit: (config: ApiFetchConfig) => void;
  loading?: boolean;
}

interface ApiFetchConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers: Record<string, string>;
  body?: string;
  timeout: number;
  retryCount: number;
  useProxy: boolean;
  followRedirects: boolean;
}

export const ApiForm: React.FC<ApiFormProps> = ({ onSubmit, loading = false }) => {
  const { showError } = useNotification();
  const [config, setConfig] = useState<ApiFetchConfig>({
    url: '',
    method: 'GET',
    headers: {},
    timeout: 30000,
    retryCount: 3,
    useProxy: false,
    followRedirects: true
  });

  const [headersText, setHeadersText] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 解析headers
    let headers: Record<string, string> = {};
    if (headersText.trim()) {
      try {
        headersText.split('\n').forEach(line => {
          const [key, value] = line.split(':').map(s => s.trim());
          if (key && value) {
            headers[key] = value;
          }
        });
      } catch (err) {
        showError('Headers 格式错误', '请使用 "Key: Value" 格式，每行一个');
        return;
      }
    }

    const finalConfig = {
      ...config,
      headers
    };

    onSubmit(finalConfig);
  };

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">API抓取配置</h3>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* URL输入 */}
        <div>
          <label className="block text-sm font-medium mb-2">目标URL *</label>
          <input
            type="url"
            value={config.url}
            onChange={(e) => setConfig({ ...config, url: e.target.value })}
            placeholder="https://api.example.com/data"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        {/* HTTP方法选择 */}
        <div>
          <label className="block text-sm font-medium mb-2">HTTP方法</label>
          <select
            value={config.method}
            onChange={(e) => setConfig({ ...config, method: e.target.value as any })}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="GET">GET</option>
            <option value="POST">POST</option>
            <option value="PUT">PUT</option>
            <option value="DELETE">DELETE</option>
            <option value="PATCH">PATCH</option>
          </select>
        </div>

        {/* Headers */}
        <div>
          <label className="block text-sm font-medium mb-2">请求头 (每行一个，格式: Key: Value)</label>
          <textarea
            value={headersText}
            onChange={(e) => setHeadersText(e.target.value)}
            placeholder="Content-Type: application/json
Authorization: Bearer your-token"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={4}
          />
        </div>

        {/* Body (仅POST/PUT/PATCH) */}
        {(['POST', 'PUT', 'PATCH'].includes(config.method)) && (
          <div>
            <label className="block text-sm font-medium mb-2">请求体</label>
            <textarea
              value={config.body || ''}
              onChange={(e) => setConfig({ ...config, body: e.target.value })}
              placeholder='{"key": "value"}'
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={4}
            />
          </div>
        )}

        {/* 高级选项 */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">超时时间 (毫秒)</label>
            <input
              type="number"
              value={config.timeout}
              onChange={(e) => setConfig({ ...config, timeout: parseInt(e.target.value) })}
              min="1000"
              max="120000"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">重试次数</label>
            <input
              type="number"
              value={config.retryCount}
              onChange={(e) => setConfig({ ...config, retryCount: parseInt(e.target.value) })}
              min="0"
              max="10"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* 选项开关 */}
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.useProxy}
              onChange={(e) => setConfig({ ...config, useProxy: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">使用代理</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={config.followRedirects}
              onChange={(e) => setConfig({ ...config, followRedirects: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm">跟随重定向</span>
          </label>
        </div>

        {/* 提交按钮 */}
        <Button
          type="submit"
          disabled={loading || !config.url}
          className="w-full"
        >
          {loading ? '请求中...' : '发起API请求'}
        </Button>
      </form>
    </Card>
  );
};