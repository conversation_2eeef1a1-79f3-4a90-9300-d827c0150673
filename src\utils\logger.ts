/**
 * 增强的日志工具类
 * 支持环境变量控制、彩色输出、性能优化和结构化日志
 */

import {
  writeFileSync,
  appendFileSync,
  existsSync,
  mkdirSync,
  statSync,
  unlinkSync,
  renameSync,
} from 'fs';
import { dirname } from 'path';
import { hostname } from 'os';

// 简单的颜色工具，替代chalk
const colors = {
  gray: (text: string) => `\x1b[90m${text}\x1b[0m`,
  cyan: (text: string) => `\x1b[36m${text}\x1b[0m`,
  green: (text: string) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text: string) => `\x1b[33m${text}\x1b[0m`,
  red: (text: string) => `\x1b[31m${text}\x1b[0m`,
  blue: (text: string) => `\x1b[34m${text}\x1b[0m`,
};

/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

/**
 * 日志级别字符串映射
 */
const LOG_LEVEL_NAMES: Record<LogLevel, string> = {
  [LogLevel.DEBUG]: 'DEBUG',
  [LogLevel.INFO]: 'INFO',
  [LogLevel.WARN]: 'WARN',
  [LogLevel.ERROR]: 'ERROR',
};

/**
 * 环境变量日志级别映射
 */
const ENV_LOG_LEVELS: Record<string, LogLevel> = {
  debug: LogLevel.DEBUG,
  info: LogLevel.INFO,
  warn: LogLevel.WARN,
  error: LogLevel.ERROR,
  DEBUG: LogLevel.DEBUG,
  INFO: LogLevel.INFO,
  WARN: LogLevel.WARN,
  ERROR: LogLevel.ERROR,
};

/**
 * 日志配置接口
 */
export interface LoggerConfig {
  level: LogLevel;
  file?: string;
  console: boolean;
  format?: 'json' | 'text';
  maxFileSize?: number;
  maxFiles?: number;
  enableColors?: boolean;
  enableTimestamp?: boolean;
  enableSource?: boolean;
}

/**
 * 日志记录接口
 */
export interface LogRecord {
  timestamp: Date;
  level: LogLevel;
  message: string;
  data?: any;
  source?: string;
  pid?: number;
  hostname?: string;
}

/**
 * 增强的日志工具类
 */
export class Logger {
  private config: LoggerConfig;
  private logCount = 0;
  private static globalLogger: Logger | null = null;

  constructor(config: Partial<LoggerConfig> = {}) {
    // 从环境变量获取日志级别
    const envLogLevel =
      process.env.LOG_LEVEL || process.env.NODE_ENV === 'development' ? 'debug' : 'info';
    const logLevel = ENV_LOG_LEVELS[envLogLevel] ?? LogLevel.INFO;

    this.config = {
      level: logLevel,
      console: true,
      format: 'text',
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      enableColors: process.env.NO_COLOR !== '1' && process.env.NODE_ENV !== 'test',
      enableTimestamp: true,
      enableSource: true,
      ...config,
    };

    // 如果指定了日志级别，覆盖环境变量设置
    if (config.level !== undefined) {
      this.config.level = config.level;
    }
  }

  /**
   * 获取全局日志器实例
   */
  public static getGlobalLogger(): Logger {
    if (!Logger.globalLogger) {
      Logger.globalLogger = new Logger();
    }
    return Logger.globalLogger;
  }

  /**
   * 设置全局日志器实例
   */
  public static setGlobalLogger(logger: Logger): void {
    Logger.globalLogger = logger;
  }

  /**
   * 初始化日志系统
   */
  public async initialize(): Promise<void> {
    if (this.config.file) {
      const dir = dirname(this.config.file);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }
    }
  }

  /**
   * 记录调试信息
   */
  public debug(message: string, data?: any, source?: string): void {
    this.log(LogLevel.DEBUG, message, data, source);
  }

  /**
   * 记录信息
   */
  public info(message: string, data?: any, source?: string): void {
    this.log(LogLevel.INFO, message, data, source);
  }

  /**
   * 记录警告
   */
  public warn(message: string, data?: any, source?: string): void {
    this.log(LogLevel.WARN, message, data, source);
  }

  /**
   * 记录错误
   */
  public error(message: string, data?: any, source?: string): void {
    this.log(LogLevel.ERROR, message, data, source);
  }

  /**
   * 记录日志
   */
  public log(level: LogLevel, message: string, data?: any, source?: string): void {
    // 性能优化：如果日志级别不够，直接返回
    if (level < this.config.level) {
      return;
    }

    const record: LogRecord = {
      timestamp: new Date(),
      level,
      message,
      data,
      source,
      pid: process.pid,
      hostname: hostname(),
    };

    // 输出到控制台
    if (this.config.console) {
      this.logToConsole(record);
    }

    // 输出到文件
    if (this.config.file) {
      this.logToFile(record);
    }

    this.logCount++;
  }

  /**
   * 检查是否启用了指定级别的日志
   */
  public isLevelEnabled(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  /**
   * 输出到控制台
   */
  private logToConsole(record: LogRecord): void {
    const levelName = LOG_LEVEL_NAMES[record.level];
    const parts: string[] = [];

    // 时间戳
    if (this.config.enableTimestamp) {
      const timestamp = record.timestamp.toISOString();
      parts.push(this.config.enableColors ? colors.gray(timestamp) : timestamp);
    }

    // 日志级别（带颜色）
    let levelText = levelName;
    if (this.config.enableColors) {
      switch (record.level) {
        case LogLevel.DEBUG:
          levelText = colors.cyan(levelName);
          break;
        case LogLevel.INFO:
          levelText = colors.green(levelName);
          break;
        case LogLevel.WARN:
          levelText = colors.yellow(levelName);
          break;
        case LogLevel.ERROR:
          levelText = colors.red(levelName);
          break;
      }
    }
    parts.push(`[${levelText}]`);

    // 来源
    if (this.config.enableSource && record.source) {
      const sourceText = this.config.enableColors
        ? colors.blue(`[${record.source}]`)
        : `[${record.source}]`;
      parts.push(sourceText);
    }

    // 消息
    parts.push(record.message);

    // 输出主要日志行
    console.log(parts.join(' '));

    // 输出附加数据
    if (record.data) {
      if (this.config.enableColors) {
        console.log(colors.gray('  Data:'), record.data);
      } else {
        console.log('  Data:', record.data);
      }
    }
  }

  /**
   * 输出到文件
   */
  private logToFile(record: LogRecord): void {
    if (!this.config.file) {
      return;
    }

    let logLine: string;

    if (this.config.format === 'json') {
      // JSON 格式包含更多结构化信息
      const jsonRecord = {
        timestamp: record.timestamp.toISOString(),
        level: LOG_LEVEL_NAMES[record.level],
        message: record.message,
        source: record.source,
        pid: record.pid,
        hostname: record.hostname,
        data: record.data,
      };
      logLine = JSON.stringify(jsonRecord) + '\n';
    } else {
      // 文本格式
      const timestamp = record.timestamp.toISOString();
      const levelName = LOG_LEVEL_NAMES[record.level];
      const source = record.source ? `[${record.source}]` : '';
      const dataStr = record.data ? ` | ${JSON.stringify(record.data)}` : '';

      logLine = `[${timestamp}] ${levelName}${source}: ${record.message}${dataStr}\n`;
    }

    try {
      appendFileSync(this.config.file, logLine, 'utf-8');

      // 检查文件大小并轮转
      this.rotateLogIfNeeded();
    } catch (error) {
      // 避免递归日志错误，直接输出到控制台
      console.error('写入日志文件失败:', error);
    }
  }

  /**
   * 轮转日志文件
   */
  private rotateLogIfNeeded(): void {
    if (!this.config.file || !this.config.maxFileSize) {
      return;
    }

    try {
      const stats = statSync(this.config.file);
      if (stats.size >= this.config.maxFileSize) {
        this.rotateLog();
      }
    } catch (error) {
      // 文件不存在或其他错误，忽略
    }
  }

  /**
   * 轮转日志
   */
  private rotateLog(): void {
    if (!this.config.file || !this.config.maxFiles) {
      return;
    }

    // 删除最旧的日志文件
    const oldestFile = `${this.config.file}.${this.config.maxFiles}`;
    if (existsSync(oldestFile)) {
      unlinkSync(oldestFile);
    }

    // 重命名现有的日志文件
    for (let i = this.config.maxFiles - 1; i >= 1; i--) {
      const currentFile = i === 1 ? this.config.file : `${this.config.file}.${i}`;
      const nextFile = `${this.config.file}.${i + 1}`;

      if (existsSync(currentFile)) {
        renameSync(currentFile, nextFile);
      }
    }

    // 创建新的日志文件
    writeFileSync(this.config.file, '', 'utf-8');
  }

  /**
   * 获取日志统计信息
   */
  public getStats(): {
    logCount: number;
    level: LogLevel;
    file?: string;
    console: boolean;
  } {
    return {
      logCount: this.logCount,
      level: this.config.level,
      file: this.config.file,
      console: this.config.console,
    };
  }

  /**
   * 设置日志级别
   */
  public setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  /**
   * 关闭日志系统
   */
  public async close(): Promise<void> {
    // 目前没有需要清理的资源
    // 如果将来使用流或其他资源，在这里清理
  }

  /**
   * 创建子日志器
   */
  public createChild(source: string): Logger {
    const childLogger = new Logger(this.config);

    // 重写日志方法以自动添加source
    const originalLog = childLogger.log.bind(childLogger);
    childLogger.log = (level: LogLevel, message: string, data?: any, childSource?: string) => {
      const finalSource = childSource ? `${source}.${childSource}` : source;
      originalLog(level, message, data, finalSource);
    };

    return childLogger;
  }

  /**
   * 性能优化的调试日志方法
   * 只有在调试级别启用时才会执行消息构建
   */
  public debugLazy(messageFactory: () => string, data?: any, source?: string): void {
    if (this.isLevelEnabled(LogLevel.DEBUG)) {
      this.debug(messageFactory(), data, source);
    }
  }

  /**
   * 性能优化的信息日志方法
   */
  public infoLazy(messageFactory: () => string, data?: any, source?: string): void {
    if (this.isLevelEnabled(LogLevel.INFO)) {
      this.info(messageFactory(), data, source);
    }
  }

  /**
   * 记录方法执行时间
   */
  public time(label: string, source?: string): () => void {
    const startTime = Date.now();
    return () => {
      const duration = Date.now() - startTime;
      this.debug(`${label} 执行时间: ${duration}ms`, { duration }, source);
    };
  }

  /**
   * 记录错误堆栈
   */
  public errorWithStack(message: string, error: Error, source?: string): void {
    this.error(
      message,
      {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      source
    );
  }
}

// 导出便捷的全局日志方法
export const logger = Logger.getGlobalLogger();

// 导出便捷的日志方法
export const debug = (message: string, data?: any, source?: string) =>
  logger.debug(message, data, source);
export const info = (message: string, data?: any, source?: string) =>
  logger.info(message, data, source);
export const warn = (message: string, data?: any, source?: string) =>
  logger.warn(message, data, source);
export const error = (message: string, data?: any, source?: string) =>
  logger.error(message, data, source);

// 导出性能优化方法
export const debugLazy = (messageFactory: () => string, data?: any, source?: string) =>
  logger.debugLazy(messageFactory, data, source);
export const infoLazy = (messageFactory: () => string, data?: any, source?: string) =>
  logger.infoLazy(messageFactory, data, source);

// 导出时间测量方法
export const time = (label: string, source?: string) => logger.time(label, source);

// 导出错误记录方法
export const errorWithStack = (message: string, err: Error, source?: string) =>
  logger.errorWithStack(message, err, source);
