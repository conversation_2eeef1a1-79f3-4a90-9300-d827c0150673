/**
 * XML 解析器实现
 */

import * as xml2js from 'xml2js';
import {
  IParser,
  ParserInput,
  ParserOutput,
} from '@/interfaces';

/**
 * XML 解析器选项接口
 */
export interface XmlParserOptions {
  /** 元素选择器映射 */
  elementMappings: Record<string, string>;
  /** 属性提取配置 */
  attributeExtraction?: {
    enabled: boolean;
    attributes: string[];
  };
  /** 命名空间处理 */
  namespaceHandling?: {
    enabled: boolean;
    stripPrefix: boolean;
    preserveNamespaces: string[];
  };
  /** 文本内容处理 */
  textProcessing?: {
    trim: boolean;
    normalize: boolean;
    preserveWhitespace: boolean;
  };
  /** 链接提取配置 */
  linkExtraction?: {
    enabled: boolean;
    linkElements: string[];
    linkAttributes: string[];
  };
  /** XML解析选项 */
  parseOptions?: xml2js.OptionsV2;
}

/**
 * XML 解析器实现类
 */
export class XmlParser implements IParser {
  public readonly name = 'xml-parser';
  public readonly version = '1.0.0';
  public readonly description = 'XML content parser with XPath-like element selection';

  private options: XmlParserOptions;
  private parser: xml2js.Parser;

  constructor(options: XmlParserOptions) {
    this.options = {
      attributeExtraction: {
        enabled: true,
        attributes: ['href', 'src', 'url', 'link'],
      },
      namespaceHandling: {
        enabled: true,
        stripPrefix: false,
        preserveNamespaces: [],
      },
      textProcessing: {
        trim: true,
        normalize: true,
        preserveWhitespace: false,
      },
      linkExtraction: {
        enabled: true,
        linkElements: ['link', 'url', 'loc'],
        linkAttributes: ['href', 'src', 'url'],
      },
      parseOptions: {
        explicitArray: false,
        mergeAttrs: false,
        explicitRoot: false,
        ignoreAttrs: false,
      },
      ...options,
    };

    this.parser = new xml2js.Parser({
      ...this.options.parseOptions,
      // async: false, // 移除不支持的选项
    });
  }

  /**
   * 判断此解析器是否适用于该页面
   */
  public canParse(input: ParserInput): boolean {
    // 检查内容类型
    const contentType = input.headers?.['content-type'] || '';
    if (contentType.includes('application/xml') || 
        contentType.includes('text/xml') || 
        contentType.includes('application/rss+xml') ||
        contentType.includes('application/atom+xml')) {
      return true;
    }

    // 检查URL路径
    if (input.url.includes('.xml') || 
        input.url.includes('.rss') || 
        input.url.includes('.atom') ||
        input.url.includes('/rss') ||
        input.url.includes('/feed')) {
      return true;
    }

    // 检查内容是否以XML声明开始
    const trimmedContent = input.content.trim();
    if (trimmedContent.startsWith('<?xml') || 
        trimmedContent.startsWith('<rss') ||
        trimmedContent.startsWith('<feed') ||
        trimmedContent.startsWith('<urlset')) {
      return true;
    }

    return false;
  }

  /**
   * 执行解析
   */
  public async parse(input: ParserInput): Promise<ParserOutput> {
    const startTime = Date.now();

    try {
      // 解析XML内容
      const xmlData = await this.parseXml(input.content);
      
      // 提取数据
      const extractedData = this.extractData(xmlData);
      
      // 提取链接
      const newUrls = this.options.linkExtraction?.enabled 
        ? this.extractLinks(xmlData, input.url) 
        : [];
      
      const parseTime = Date.now() - startTime;
      
      return {
        extractedData,
        newUrls,
        metadata: {
          parserName: this.name,
          parseTime,
          extractedFieldsCount: extractedData ? Object.keys(extractedData).length : 0,
          discoveredUrlsCount: newUrls.length,
          // xmlStructure: this.analyzeXmlStructure(xmlData), // 移除不支持的字段
        },
      };
      
    } catch (error) {
      return {
        extractedData: null,
        newUrls: [],
        error: {
          message: error instanceof Error ? error.message : 'XML parsing failed',
          code: 'XML_PARSE_ERROR',
          details: error,
        },
      };
    }
  }

  /**
   * 解析XML内容
   */
  private async parseXml(content: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.parser.parseString(content, (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  /**
   * 提取数据
   */
  private extractData(xmlData: any): Record<string, any> | null {
    const data: Record<string, any> = {};
    
    // 使用元素映射提取数据
    for (const [field, elementPath] of Object.entries(this.options.elementMappings)) {
      try {
        const value = this.getElementByPath(xmlData, elementPath);
        data[field] = this.processValue(value);
      } catch (error) {
        console.warn(`Failed to extract field '${field}' with path '${elementPath}':`, error);
        data[field] = null;
      }
    }

    // 如果没有配置元素映射，尝试智能提取
    if (Object.keys(this.options.elementMappings).length === 0) {
      return this.smartExtract(xmlData);
    }

    return Object.keys(data).length > 0 ? data : null;
  }

  /**
   * 根据路径获取元素
   */
  private getElementByPath(obj: any, path: string): any {
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (current === null || current === undefined) {
        return undefined;
      }
      
      // 处理数组索引
      if (part.includes('[') && part.includes(']')) {
        const [elementName, indexStr] = part.split('[');
        if (!indexStr) return undefined;
        const index = parseInt(indexStr.replace(']', ''), 10);

        if (current && elementName && current[elementName] && Array.isArray(current[elementName])) {
          current = current[elementName][index];
        } else {
          return undefined;
        }
      } else {
        current = current && current[part];
      }
    }
    
    return current;
  }

  /**
   * 处理值
   */
  private processValue(value: any): any {
    if (typeof value === 'string') {
      if (this.options.textProcessing?.trim) {
        value = value.trim();
      }
      
      if (this.options.textProcessing?.normalize) {
        value = value.replace(/\s+/g, ' ');
      }
    }
    
    return value;
  }

  /**
   * 智能提取数据
   */
  private smartExtract(xmlData: any): Record<string, any> | null {
    const data: Record<string, any> = {};
    
    // 检测常见的XML结构
    if (this.isRssFeed(xmlData)) {
      return this.extractRssData(xmlData);
    } else if (this.isAtomFeed(xmlData)) {
      return this.extractAtomData(xmlData);
    } else if (this.isSitemap(xmlData)) {
      return this.extractSitemapData(xmlData);
    }
    
    // 通用提取
    return this.extractGenericXmlData(xmlData);
  }

  /**
   * 检查是否为RSS feed
   */
  private isRssFeed(xmlData: any): boolean {
    return xmlData.rss || xmlData.channel;
  }

  /**
   * 检查是否为Atom feed
   */
  private isAtomFeed(xmlData: any): boolean {
    return xmlData.feed;
  }

  /**
   * 检查是否为Sitemap
   */
  private isSitemap(xmlData: any): boolean {
    return xmlData.urlset || xmlData.sitemapindex;
  }

  /**
   * 提取RSS数据
   */
  private extractRssData(xmlData: any): Record<string, any> {
    const rss = xmlData.rss || xmlData;
    const channel = rss.channel || rss;
    
    return {
      type: 'rss',
      title: channel.title,
      description: channel.description,
      link: channel.link,
      language: channel.language,
      lastBuildDate: channel.lastBuildDate,
      items: Array.isArray(channel.item) ? channel.item : [channel.item].filter(Boolean),
    };
  }

  /**
   * 提取Atom数据
   */
  private extractAtomData(xmlData: any): Record<string, any> {
    const feed = xmlData.feed || xmlData;
    
    return {
      type: 'atom',
      title: feed.title,
      subtitle: feed.subtitle,
      link: feed.link,
      updated: feed.updated,
      entries: Array.isArray(feed.entry) ? feed.entry : [feed.entry].filter(Boolean),
    };
  }

  /**
   * 提取Sitemap数据
   */
  private extractSitemapData(xmlData: any): Record<string, any> {
    if (xmlData.urlset) {
      return {
        type: 'sitemap',
        urls: Array.isArray(xmlData.urlset.url) ? xmlData.urlset.url : [xmlData.urlset.url].filter(Boolean),
      };
    } else if (xmlData.sitemapindex) {
      return {
        type: 'sitemapindex',
        sitemaps: Array.isArray(xmlData.sitemapindex.sitemap) ? xmlData.sitemapindex.sitemap : [xmlData.sitemapindex.sitemap].filter(Boolean),
      };
    }
    
    return {};
  }

  /**
   * 提取通用XML数据
   */
  private extractGenericXmlData(xmlData: any): Record<string, any> {
    const data: Record<string, any> = {};
    
    const extractRecursive = (obj: any, prefix = ''): void => {
      if (typeof obj === 'object' && obj !== null) {
        for (const [key, value] of Object.entries(obj)) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          
          if (Array.isArray(value)) {
            data[fullKey] = value;
          } else if (typeof value === 'object' && value !== null) {
            extractRecursive(value, fullKey);
          } else {
            data[fullKey] = value;
          }
        }
      }
    };
    
    extractRecursive(xmlData);
    return data;
  }

  /**
   * 提取链接
   */
  private extractLinks(xmlData: any, baseUrl: string): string[] {
    const links: string[] = [];
    
    if (!this.options.linkExtraction?.enabled) {
      return links;
    }

    const { linkElements, linkAttributes } = this.options.linkExtraction;
    
    // 递归搜索链接
    this.searchLinks(xmlData, linkElements, linkAttributes, links);

    // 去重并转换为绝对URL
    return [...new Set(links)].map(url => this.resolveUrl(url, baseUrl)).filter(Boolean) as string[];
  }

  /**
   * 递归搜索链接
   */
  private searchLinks(obj: any, linkElements: string[], linkAttributes: string[], links: string[]): void {
    if (typeof obj === 'object' && obj !== null) {
      if (Array.isArray(obj)) {
        for (const item of obj) {
          this.searchLinks(item, linkElements, linkAttributes, links);
        }
      } else {
        for (const [key, value] of Object.entries(obj)) {
          // 检查元素名称
          if (linkElements.includes(key.toLowerCase()) && typeof value === 'string') {
            if (this.isValidUrl(value)) {
              links.push(value);
            }
          }
          
          // 检查属性
          if (typeof value === 'object' && value !== null && (value as any).$) {
            for (const attr of linkAttributes) {
              const attrValue = (value as any).$[attr];
              if (attrValue && this.isValidUrl(attrValue)) {
                links.push(attrValue);
              }
            }
          }
          
          // 递归搜索
          if (typeof value === 'object') {
            this.searchLinks(value, linkElements, linkAttributes, links);
          }
        }
      }
    }
  }

  /**
   * 检查是否为有效URL
   */
  private isValidUrl(url: string): boolean {
    if (typeof url !== 'string' || url.length === 0) {
      return false;
    }

    try {
      new URL(url);
      return true;
    } catch {
      return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');
    }
  }

  /**
   * 解析URL为绝对URL
   */
  private resolveUrl(url: string, baseUrl: string): string | null {
    try {
      return new URL(url, baseUrl).toString();
    } catch {
      return null;
    }
  }

  /**
   * 分析XML结构
   */
  private analyzeXmlStructure(xmlData: any): {
    rootElement: string;
    depth: number;
    elementCount: number;
    attributeCount: number;
    namespaces: string[];
  } {
    let depth = 0;
    let elementCount = 0;
    let attributeCount = 0;
    const namespaces = new Set<string>();
    
    const analyze = (obj: any, currentDepth: number): void => {
      depth = Math.max(depth, currentDepth);
      
      if (typeof obj === 'object' && obj !== null) {
        if (Array.isArray(obj)) {
          for (const item of obj) {
            analyze(item, currentDepth);
          }
        } else {
          elementCount++;
          
          for (const [key, value] of Object.entries(obj)) {
            if (key === '$') {
              attributeCount += Object.keys(value as object).length;
            } else {
              if (key.includes(':')) {
                const namespace = key.split(':')[0];
                if (namespace) {
                  namespaces.add(namespace);
                }
              }
              analyze(value, currentDepth + 1);
            }
          }
        }
      }
    };
    
    analyze(xmlData, 0);
    
    return {
      rootElement: Object.keys(xmlData)[0] || 'unknown',
      depth,
      elementCount,
      attributeCount,
      namespaces: Array.from(namespaces),
    };
  }
}
