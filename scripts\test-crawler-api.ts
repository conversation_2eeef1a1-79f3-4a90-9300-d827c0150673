/**
 * 测试爬虫API功能
 */

import { info, error } from '@/utils';

// 测试任务配置
const testTask = {
  name: '测试爬虫任务',
  urls: ['https://httpbin.org/get', 'https://httpbin.org/json'],
  config: {
    maxDepth: 2,
    maxConcurrency: 3,
    delayMs: 500,
    enableProxy: false,
    enableAI: false,
    allowedDomains: ['httpbin.org'],
    selectors: {
      title: 'title',
      content: 'body'
    }
  }
};

/**
 * 测试API端点
 */
export async function testCrawlerAPI() {
  try {
    info('开始测试爬虫API...');

    // 这里可以添加实际的API测试
    // 由于是服务器端测试，我们暂时只打印配置
    info('测试任务配置:', testTask);
    info('API端点:');
    info('- POST /api/v1/tasks - 创建任务');
    info('- GET /api/v1/tasks - 获取任务列表');
    info('- GET /api/v1/tasks/stats - 获取统计数据');
    info('- GET /api/v1/tasks/:taskId - 获取任务详情');
    info('- POST /api/v1/tasks/:taskId/start - 启动任务');
    info('- POST /api/v1/tasks/:taskId/pause - 暂停任务');
    info('- POST /api/v1/tasks/:taskId/stop - 停止任务');
    info('- DELETE /api/v1/tasks/:taskId - 删除任务');

    info('✅ 爬虫API配置完成');
    return true;
  } catch (err) {
    error('❌ 爬虫API测试失败:', err);
    return false;
  }
}

if (require.main === module) {
  testCrawlerAPI();
}